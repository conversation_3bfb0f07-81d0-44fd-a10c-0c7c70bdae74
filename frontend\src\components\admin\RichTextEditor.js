import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Paper,
  Toolbar,
  IconButton,
  Divider,
  Typography,
  Button,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Tooltip,
  ButtonGroup
} from '@mui/material';
import {
  FormatBold,
  FormatItalic,
  FormatUnderlined,
  FormatListBulleted,
  FormatListNumbered,
  FormatAlignLeft,
  FormatAlignCenter,
  FormatAlignRight,
  Link,
  Image,
  VideoLibrary,
  Code,
  FormatQuote,
  Undo,
  Redo,
  FormatSize,
  FormatColorText,
  FormatColorFill,
  Table,
  InsertEmoticon
} from '@mui/icons-material';

const RichTextEditor = ({ 
  value = '', 
  onChange, 
  placeholder = 'ابدأ الكتابة...', 
  height = 400,
  toolbar = 'full' // 'basic', 'full', 'minimal'
}) => {
  const editorRef = useRef(null);
  const [content, setContent] = useState(value);
  const [linkDialog, setLinkDialog] = useState(false);
  const [imageDialog, setImageDialog] = useState(false);
  const [tableDialog, setTableDialog] = useState(false);
  const [colorMenu, setColorMenu] = useState(null);
  const [fontMenu, setFontMenu] = useState(null);
  
  const [linkData, setLinkData] = useState({ text: '', url: '' });
  const [imageData, setImageData] = useState({ url: '', alt: '', width: '', height: '' });
  const [tableData, setTableData] = useState({ rows: 3, cols: 3 });

  useEffect(() => {
    if (editorRef.current && content !== editorRef.current.innerHTML) {
      editorRef.current.innerHTML = content;
    }
  }, [value]);

  // تنفيذ أوامر التنسيق
  const execCommand = (command, value = null) => {
    document.execCommand(command, false, value);
    updateContent();
  };

  // تحديث المحتوى
  const updateContent = () => {
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML;
      setContent(newContent);
      if (onChange) {
        onChange(newContent);
      }
    }
  };

  // إدراج رابط
  const insertLink = () => {
    const selection = window.getSelection();
    const selectedText = selection.toString();
    
    setLinkData({
      text: selectedText || '',
      url: ''
    });
    setLinkDialog(true);
  };

  const handleInsertLink = () => {
    if (linkData.url) {
      const linkHtml = `<a href="${linkData.url}" target="_blank" rel="noopener noreferrer">${linkData.text || linkData.url}</a>`;
      execCommand('insertHTML', linkHtml);
    }
    setLinkDialog(false);
    setLinkData({ text: '', url: '' });
  };

  // إدراج صورة
  const handleInsertImage = () => {
    if (imageData.url) {
      let imgHtml = `<img src="${imageData.url}" alt="${imageData.alt}"`;
      if (imageData.width) imgHtml += ` width="${imageData.width}"`;
      if (imageData.height) imgHtml += ` height="${imageData.height}"`;
      imgHtml += ' style="max-width: 100%; height: auto;" />';
      
      execCommand('insertHTML', imgHtml);
    }
    setImageDialog(false);
    setImageData({ url: '', alt: '', width: '', height: '' });
  };

  // إدراج جدول
  const handleInsertTable = () => {
    let tableHtml = '<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">';
    
    for (let i = 0; i < tableData.rows; i++) {
      tableHtml += '<tr>';
      for (let j = 0; j < tableData.cols; j++) {
        tableHtml += '<td style="padding: 8px; border: 1px solid #ddd;">&nbsp;</td>';
      }
      tableHtml += '</tr>';
    }
    tableHtml += '</table>';
    
    execCommand('insertHTML', tableHtml);
    setTableDialog(false);
    setTableData({ rows: 3, cols: 3 });
  };

  // تغيير لون النص
  const changeTextColor = (color) => {
    execCommand('foreColor', color);
    setColorMenu(null);
  };

  // تغيير حجم الخط
  const changeFontSize = (size) => {
    execCommand('fontSize', size);
    setFontMenu(null);
  };

  // أشرطة الأدوات المختلفة
  const getToolbarButtons = () => {
    const basicButtons = [
      { icon: <FormatBold />, command: 'bold', title: 'عريض' },
      { icon: <FormatItalic />, command: 'italic', title: 'مائل' },
      { icon: <FormatUnderlined />, command: 'underline', title: 'تحته خط' },
    ];

    const formatButtons = [
      { icon: <FormatListBulleted />, command: 'insertUnorderedList', title: 'قائمة نقطية' },
      { icon: <FormatListNumbered />, command: 'insertOrderedList', title: 'قائمة مرقمة' },
      { icon: <FormatAlignLeft />, command: 'justifyLeft', title: 'محاذاة يسار' },
      { icon: <FormatAlignCenter />, command: 'justifyCenter', title: 'محاذاة وسط' },
      { icon: <FormatAlignRight />, command: 'justifyRight', title: 'محاذاة يمين' },
    ];

    const advancedButtons = [
      { icon: <Link />, action: insertLink, title: 'إدراج رابط' },
      { icon: <Image />, action: () => setImageDialog(true), title: 'إدراج صورة' },
      { icon: <Table />, action: () => setTableDialog(true), title: 'إدراج جدول' },
      { icon: <Code />, command: 'formatBlock', value: 'pre', title: 'كود' },
      { icon: <FormatQuote />, command: 'formatBlock', value: 'blockquote', title: 'اقتباس' },
    ];

    const utilityButtons = [
      { icon: <Undo />, command: 'undo', title: 'تراجع' },
      { icon: <Redo />, command: 'redo', title: 'إعادة' },
    ];

    switch (toolbar) {
      case 'basic':
        return [...basicButtons];
      case 'minimal':
        return [...basicButtons.slice(0, 3)];
      case 'full':
      default:
        return [...basicButtons, ...formatButtons, ...advancedButtons, ...utilityButtons];
    }
  };

  const colors = [
    '#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', 
    '#FF00FF', '#00FFFF', '#FFA500', '#800080', '#008000'
  ];

  const fontSizes = [1, 2, 3, 4, 5, 6, 7];

  return (
    <Paper elevation={2} sx={{ border: '1px solid #ddd' }}>
      {/* شريط الأدوات */}
      <Toolbar 
        variant="dense" 
        sx={{ 
          backgroundColor: '#f5f5f5', 
          borderBottom: '1px solid #ddd',
          minHeight: '48px !important',
          flexWrap: 'wrap',
          gap: 1
        }}
      >
        {/* أزرار التنسيق الأساسية */}
        <ButtonGroup size="small">
          {getToolbarButtons().slice(0, 3).map((button, index) => (
            <Tooltip key={index} title={button.title}>
              <IconButton
                size="small"
                onClick={() => button.command ? execCommand(button.command, button.value) : button.action()}
              >
                {button.icon}
              </IconButton>
            </Tooltip>
          ))}
        </ButtonGroup>

        <Divider orientation="vertical" flexItem />

        {/* حجم الخط */}
        {toolbar === 'full' && (
          <>
            <Tooltip title="حجم الخط">
              <IconButton
                size="small"
                onClick={(e) => setFontMenu(e.currentTarget)}
              >
                <FormatSize />
              </IconButton>
            </Tooltip>

            <Menu
              anchorEl={fontMenu}
              open={Boolean(fontMenu)}
              onClose={() => setFontMenu(null)}
            >
              {fontSizes.map(size => (
                <MenuItem key={size} onClick={() => changeFontSize(size)}>
                  <Typography variant="body2" style={{ fontSize: `${8 + size * 2}px` }}>
                    حجم {size}
                  </Typography>
                </MenuItem>
              ))}
            </Menu>

            {/* لون النص */}
            <Tooltip title="لون النص">
              <IconButton
                size="small"
                onClick={(e) => setColorMenu(e.currentTarget)}
              >
                <FormatColorText />
              </IconButton>
            </Tooltip>

            <Menu
              anchorEl={colorMenu}
              open={Boolean(colorMenu)}
              onClose={() => setColorMenu(null)}
            >
              <Box sx={{ p: 1, display: 'grid', gridTemplateColumns: 'repeat(5, 1fr)', gap: 1 }}>
                {colors.map(color => (
                  <Box
                    key={color}
                    sx={{
                      width: 24,
                      height: 24,
                      backgroundColor: color,
                      cursor: 'pointer',
                      border: '1px solid #ccc',
                      borderRadius: 1
                    }}
                    onClick={() => changeTextColor(color)}
                  />
                ))}
              </Box>
            </Menu>

            <Divider orientation="vertical" flexItem />
          </>
        )}

        {/* أزرار التنسيق */}
        {toolbar !== 'minimal' && (
          <>
            <ButtonGroup size="small">
              {getToolbarButtons().slice(3, 8).map((button, index) => (
                <Tooltip key={index} title={button.title}>
                  <IconButton
                    size="small"
                    onClick={() => button.command ? execCommand(button.command, button.value) : button.action()}
                  >
                    {button.icon}
                  </IconButton>
                </Tooltip>
              ))}
            </ButtonGroup>

            <Divider orientation="vertical" flexItem />
          </>
        )}

        {/* أزرار متقدمة */}
        {toolbar === 'full' && (
          <>
            <ButtonGroup size="small">
              {getToolbarButtons().slice(8, 13).map((button, index) => (
                <Tooltip key={index} title={button.title}>
                  <IconButton
                    size="small"
                    onClick={() => button.command ? execCommand(button.command, button.value) : button.action()}
                  >
                    {button.icon}
                  </IconButton>
                </Tooltip>
              ))}
            </ButtonGroup>

            <Divider orientation="vertical" flexItem />

            {/* أزرار التراجع والإعادة */}
            <ButtonGroup size="small">
              {getToolbarButtons().slice(-2).map((button, index) => (
                <Tooltip key={index} title={button.title}>
                  <IconButton
                    size="small"
                    onClick={() => execCommand(button.command)}
                  >
                    {button.icon}
                  </IconButton>
                </Tooltip>
              ))}
            </ButtonGroup>
          </>
        )}
      </Toolbar>

      {/* منطقة التحرير */}
      <Box
        ref={editorRef}
        contentEditable
        suppressContentEditableWarning
        onInput={updateContent}
        onBlur={updateContent}
        sx={{
          minHeight: height,
          p: 2,
          outline: 'none',
          direction: 'rtl',
          textAlign: 'right',
          fontFamily: 'Arial, sans-serif',
          fontSize: '14px',
          lineHeight: 1.6,
          '&:empty::before': {
            content: `"${placeholder}"`,
            color: '#aaa',
            fontStyle: 'italic'
          },
          '& img': {
            maxWidth: '100%',
            height: 'auto'
          },
          '& table': {
            borderCollapse: 'collapse',
            width: '100%',
            margin: '10px 0'
          },
          '& td, & th': {
            border: '1px solid #ddd',
            padding: '8px',
            textAlign: 'right'
          },
          '& blockquote': {
            borderRight: '4px solid #4169E1',
            paddingRight: '16px',
            margin: '16px 0',
            fontStyle: 'italic',
            backgroundColor: '#f9f9f9',
            padding: '12px 16px'
          },
          '& pre': {
            backgroundColor: '#f4f4f4',
            padding: '12px',
            borderRadius: '4px',
            overflow: 'auto',
            fontFamily: 'monospace',
            direction: 'ltr',
            textAlign: 'left'
          },
          '& a': {
            color: '#4169E1',
            textDecoration: 'none',
            '&:hover': {
              textDecoration: 'underline'
            }
          }
        }}
        dangerouslySetInnerHTML={{ __html: content }}
      />

      {/* نافذة إدراج رابط */}
      <Dialog open={linkDialog} onClose={() => setLinkDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>إدراج رابط</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="نص الرابط"
            value={linkData.text}
            onChange={(e) => setLinkData({ ...linkData, text: e.target.value })}
            sx={{ mb: 2, mt: 1 }}
          />
          <TextField
            fullWidth
            label="عنوان الرابط (URL)"
            value={linkData.url}
            onChange={(e) => setLinkData({ ...linkData, url: e.target.value })}
            placeholder="https://example.com"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setLinkDialog(false)}>إلغاء</Button>
          <Button variant="contained" onClick={handleInsertLink}>إدراج</Button>
        </DialogActions>
      </Dialog>

      {/* نافذة إدراج صورة */}
      <Dialog open={imageDialog} onClose={() => setImageDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>إدراج صورة</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="رابط الصورة (URL)"
            value={imageData.url}
            onChange={(e) => setImageData({ ...imageData, url: e.target.value })}
            sx={{ mb: 2, mt: 1 }}
            placeholder="https://example.com/image.jpg"
          />
          <TextField
            fullWidth
            label="نص بديل"
            value={imageData.alt}
            onChange={(e) => setImageData({ ...imageData, alt: e.target.value })}
            sx={{ mb: 2 }}
          />
          <Box sx={{ display: 'flex', gap: 2 }}>
            <TextField
              label="العرض (px)"
              value={imageData.width}
              onChange={(e) => setImageData({ ...imageData, width: e.target.value })}
              type="number"
            />
            <TextField
              label="الارتفاع (px)"
              value={imageData.height}
              onChange={(e) => setImageData({ ...imageData, height: e.target.value })}
              type="number"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setImageDialog(false)}>إلغاء</Button>
          <Button variant="contained" onClick={handleInsertImage}>إدراج</Button>
        </DialogActions>
      </Dialog>

      {/* نافذة إدراج جدول */}
      <Dialog open={tableDialog} onClose={() => setTableDialog(false)} maxWidth="xs" fullWidth>
        <DialogTitle>إدراج جدول</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="عدد الصفوف"
            type="number"
            value={tableData.rows}
            onChange={(e) => setTableData({ ...tableData, rows: parseInt(e.target.value) || 1 })}
            sx={{ mb: 2, mt: 1 }}
            inputProps={{ min: 1, max: 20 }}
          />
          <TextField
            fullWidth
            label="عدد الأعمدة"
            type="number"
            value={tableData.cols}
            onChange={(e) => setTableData({ ...tableData, cols: parseInt(e.target.value) || 1 })}
            inputProps={{ min: 1, max: 10 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTableDialog(false)}>إلغاء</Button>
          <Button variant="contained" onClick={handleInsertTable}>إدراج</Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default RichTextEditor;
