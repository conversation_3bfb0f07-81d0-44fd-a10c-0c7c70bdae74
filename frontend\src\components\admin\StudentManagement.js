import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  Avatar,
  LinearProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Divider
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Visibility,
  PersonAdd,
  School,
  TrendingUp,
  CheckCircle,
  Error as ErrorIcon,
  Refresh,
  Download,
  Send,
  Block,
  CheckBox,
  Assignment,
  Timeline
} from '@mui/icons-material';
import { useLanguage } from '../../contexts/LanguageContext';
// import { supabase } from '../../config/supabase';
import toast from 'react-hot-toast';

const StudentManagement = () => {
  const { language, t } = useLanguage();
  const [loading, setLoading] = useState(true);
  const [students, setStudents] = useState([]);
  const [courses, setCourses] = useState([]);
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedStudent, setSelectedStudent] = useState(null);
  
  // حالات النوافذ المنبثقة
  const [studentDialog, setStudentDialog] = useState(false);
  const [enrollDialog, setEnrollDialog] = useState(false);
  const [progressDialog, setProgressDialog] = useState(false);
  
  // حالات النماذج
  const [studentForm, setStudentForm] = useState({
    name: '',
    email: '',
    phone: '',
    student_code: '',
    is_active: true
  });
  
  const [enrollForm, setEnrollForm] = useState({
    student_id: '',
    course_id: '',
    enrollment_date: new Date().toISOString().split('T')[0]
  });

  const [stats, setStats] = useState({
    totalStudents: 0,
    activeStudents: 0,
    newThisMonth: 0,
    completedCourses: 0
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // محاكاة تحميل البيانات من قاعدة البيانات
      const studentsData = [
        {
          id: 1,
          name: 'أحمد محمد',
          email: '<EMAIL>',
          phone: '0501234567',
          student_code: '123456',
          is_active: true,
          created_at: new Date().toISOString(),
          enrollments: [
            {
              id: 1,
              progress: 75,
              completed_at: null,
              courses: { title: 'أساسيات البرمجة', level: 'beginner' }
            }
          ]
        }
      ];

      const coursesData = [
        { id: 1, title: 'أساسيات البرمجة', level: 'beginner' },
        { id: 2, title: 'تطوير المواقع', level: 'intermediate' }
      ];
      
      setStudents(studentsData);
      setCourses(coursesData);
      
      // حساب الإحصائيات
      const totalStudents = studentsData.length;
      const activeStudents = studentsData.filter(s => s.is_active).length;
      const newThisMonth = studentsData.filter(s => {
        const createdDate = new Date(s.created_at);
        const now = new Date();
        return createdDate.getMonth() === now.getMonth() &&
               createdDate.getFullYear() === now.getFullYear();
      }).length;
      
      setStats({
        totalStudents,
        activeStudents,
        newThisMonth,
        completedCourses: 0 // سيتم حسابها لاحقاً
      });
      
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      toast.error('خطأ في تحميل البيانات');
      
      // بيانات تجريبية
      setStudents([
        {
          id: 1,
          name: 'أحمد محمد',
          email: '<EMAIL>',
          phone: '0501234567',
          student_code: '123456',
          is_active: true,
          created_at: new Date().toISOString(),
          enrollments: [
            {
              id: 1,
              progress: 75,
              completed_at: null,
              courses: { title: 'أساسيات البرمجة', level: 'beginner' }
            }
          ]
        }
      ]);
      
      setCourses([
        { id: 1, title: 'أساسيات البرمجة', level: 'beginner' },
        { id: 2, title: 'تطوير المواقع', level: 'intermediate' }
      ]);
      
      setStats({
        totalStudents: 150,
        activeStudents: 120,
        newThisMonth: 25,
        completedCourses: 89
      });
    } finally {
      setLoading(false);
    }
  };

  const generateStudentCode = () => {
    return Math.floor(100000 + Math.random() * 900000).toString();
  };

  const handleCreateStudent = async () => {
    try {
      const newStudentCode = generateStudentCode();
      const studentData = {
        id: Date.now(),
        ...studentForm,
        student_code: newStudentCode,
        created_at: new Date().toISOString(),
        enrollments: []
      };

      setStudents([studentData, ...students]);
      setStudentDialog(false);
      resetStudentForm();
      toast.success(`تم إنشاء الطالب بنجاح. كود الطالب: ${newStudentCode}`);

    } catch (error) {
      console.error('خطأ في إنشاء الطالب:', error);
      toast.error('خطأ في إنشاء الطالب');
    }
  };

  const handleEnrollStudent = async () => {
    try {
      // محاكاة تسجيل الطالب في الكورس
      const enrollment = {
        id: Date.now(),
        ...enrollForm,
        progress: 0,
        created_at: new Date().toISOString()
      };

      setEnrollDialog(false);
      resetEnrollForm();
      toast.success('تم تسجيل الطالب في الكورس بنجاح');
      loadData(); // إعادة تحميل البيانات

    } catch (error) {
      console.error('خطأ في تسجيل الطالب:', error);
      toast.error('خطأ في تسجيل الطالب');
    }
  };

  const resetStudentForm = () => {
    setStudentForm({
      name: '',
      email: '',
      phone: '',
      student_code: '',
      is_active: true
    });
  };

  const resetEnrollForm = () => {
    setEnrollForm({
      student_id: '',
      course_id: '',
      enrollment_date: new Date().toISOString().split('T')[0]
    });
  };

  // مكون بطاقة الإحصائيات
  const StatCard = ({ title, value, icon, color, subtitle }) => (
    <Card
      sx={{
        background: `linear-gradient(135deg, ${color}15 0%, ${color}05 100%)`,
        border: `1px solid ${color}30`,
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: `0 8px 25px ${color}25`
        }
      }}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar sx={{ bgcolor: color, mr: 2 }}>
            {icon}
          </Avatar>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 'bold', color }}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
          </Box>
        </Box>
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );

  // مكون صف الطالب في الجدول
  const StudentRow = ({ student }) => {
    const totalEnrollments = student.enrollments?.length || 0;
    const completedEnrollments = student.enrollments?.filter(e => e.completed_at)?.length || 0;
    const averageProgress = totalEnrollments > 0 
      ? student.enrollments.reduce((sum, e) => sum + (e.progress || 0), 0) / totalEnrollments 
      : 0;

    return (
      <TableRow>
        <TableCell>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
              {student.name.charAt(0)}
            </Avatar>
            <Box>
              <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                {student.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {student.email}
              </Typography>
            </Box>
          </Box>
        </TableCell>
        <TableCell>
          <Chip
            label={student.student_code}
            size="small"
            color="primary"
            variant="outlined"
          />
        </TableCell>
        <TableCell>
          <Chip
            label={student.is_active ? 'نشط' : 'غير نشط'}
            size="small"
            color={student.is_active ? 'success' : 'error'}
          />
        </TableCell>
        <TableCell>{totalEnrollments}</TableCell>
        <TableCell>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <LinearProgress
              variant="determinate"
              value={averageProgress}
              sx={{ flex: 1, height: 8, borderRadius: 4 }}
            />
            <Typography variant="caption">
              {Math.round(averageProgress)}%
            </Typography>
          </Box>
        </TableCell>
        <TableCell>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="عرض التفاصيل">
              <IconButton
                size="small"
                onClick={() => {
                  setSelectedStudent(student);
                  setProgressDialog(true);
                }}
              >
                <Visibility />
              </IconButton>
            </Tooltip>
            <Tooltip title="تسجيل في كورس">
              <IconButton
                size="small"
                onClick={() => {
                  setEnrollForm({ ...enrollForm, student_id: student.id });
                  setEnrollDialog(true);
                }}
              >
                <Add />
              </IconButton>
            </Tooltip>
            <Tooltip title="تعديل">
              <IconButton size="small">
                <Edit />
              </IconButton>
            </Tooltip>
          </Box>
        </TableCell>
      </TableRow>
    );
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          إدارة الطلاب
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadData}
          >
            تحديث
          </Button>
          <Button
            variant="contained"
            startIcon={<PersonAdd />}
            onClick={() => setStudentDialog(true)}
            sx={{
              background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)'
              }
            }}
          >
            إضافة طالب جديد
          </Button>
        </Box>
      </Box>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي الطلاب"
            value={stats.totalStudents}
            icon={<PersonAdd />}
            color="#4169E1"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="الطلاب النشطون"
            value={stats.activeStudents}
            icon={<CheckCircle />}
            color="#4CAF50"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="طلاب جدد هذا الشهر"
            value={stats.newThisMonth}
            icon={<TrendingUp />}
            color="#FF6B35"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="الكورسات المكتملة"
            value={stats.completedCourses}
            icon={<School />}
            color="#FFD700"
          />
        </Grid>
      </Grid>

      {/* التبويبات */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={selectedTab}
          onChange={(e, newValue) => setSelectedTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="جميع الطلاب" />
          <Tab label="الطلاب النشطون" />
          <Tab label="الطلاب غير النشطين" />
          <Tab label="التسجيلات الحديثة" />
        </Tabs>
      </Paper>

      {/* جدول الطلاب */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>الطالب</TableCell>
              <TableCell>كود الطالب</TableCell>
              <TableCell>الحالة</TableCell>
              <TableCell>عدد الكورسات</TableCell>
              <TableCell>متوسط التقدم</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {students.map((student) => (
              <StudentRow key={student.id} student={student} />
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {students.length === 0 && (
        <Box sx={{ textAlign: 'center', mt: 8 }}>
          <PersonAdd sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            لا يوجد طلاب مسجلون حالياً
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            ابدأ بإضافة أول طالب
          </Typography>
          <Button
            variant="contained"
            startIcon={<PersonAdd />}
            onClick={() => setStudentDialog(true)}
          >
            إضافة طالب جديد
          </Button>
        </Box>
      )}

      {/* نافذة إضافة طالب */}
      <Dialog
        open={studentDialog}
        onClose={() => setStudentDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>إضافة طالب جديد</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="اسم الطالب"
                value={studentForm.name}
                onChange={(e) => setStudentForm({ ...studentForm, name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="البريد الإلكتروني"
                type="email"
                value={studentForm.email}
                onChange={(e) => setStudentForm({ ...studentForm, email: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="رقم الهاتف"
                value={studentForm.phone}
                onChange={(e) => setStudentForm({ ...studentForm, phone: e.target.value })}
              />
            </Grid>
          </Grid>
          
          <Alert severity="info" sx={{ mt: 2 }}>
            سيتم إنشاء كود طالب فريد تلقائياً (6 أرقام)
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStudentDialog(false)}>
            إلغاء
          </Button>
          <Button
            variant="contained"
            onClick={handleCreateStudent}
            disabled={!studentForm.name || !studentForm.email}
          >
            إضافة الطالب
          </Button>
        </DialogActions>
      </Dialog>

      {/* نافذة تسجيل في كورس */}
      <Dialog
        open={enrollDialog}
        onClose={() => setEnrollDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>تسجيل طالب في كورس</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>الطالب</InputLabel>
                <Select
                  value={enrollForm.student_id}
                  onChange={(e) => setEnrollForm({ ...enrollForm, student_id: e.target.value })}
                >
                  {students.map((student) => (
                    <MenuItem key={student.id} value={student.id}>
                      {student.name} - {student.student_code}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>الكورس</InputLabel>
                <Select
                  value={enrollForm.course_id}
                  onChange={(e) => setEnrollForm({ ...enrollForm, course_id: e.target.value })}
                >
                  {courses.map((course) => (
                    <MenuItem key={course.id} value={course.id}>
                      {course.title} - {course.level}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="تاريخ التسجيل"
                type="date"
                value={enrollForm.enrollment_date}
                onChange={(e) => setEnrollForm({ ...enrollForm, enrollment_date: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEnrollDialog(false)}>
            إلغاء
          </Button>
          <Button
            variant="contained"
            onClick={handleEnrollStudent}
            disabled={!enrollForm.student_id || !enrollForm.course_id}
          >
            تسجيل في الكورس
          </Button>
        </DialogActions>
      </Dialog>

      {/* نافذة تفاصيل تقدم الطالب */}
      <Dialog
        open={progressDialog}
        onClose={() => setProgressDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          تفاصيل تقدم الطالب: {selectedStudent?.name}
        </DialogTitle>
        <DialogContent>
          {selectedStudent?.enrollments?.length > 0 ? (
            <List>
              {selectedStudent.enrollments.map((enrollment, index) => (
                <React.Fragment key={enrollment.id}>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        <School />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={enrollment.courses.title}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            المستوى: {enrollment.courses.level}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                            <LinearProgress
                              variant="determinate"
                              value={enrollment.progress || 0}
                              sx={{ flex: 1, height: 8, borderRadius: 4 }}
                            />
                            <Typography variant="caption">
                              {enrollment.progress || 0}%
                            </Typography>
                          </Box>
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      {enrollment.completed_at ? (
                        <Chip
                          label="مكتمل"
                          size="small"
                          color="success"
                          icon={<CheckCircle />}
                        />
                      ) : (
                        <Chip
                          label="قيد التقدم"
                          size="small"
                          color="primary"
                          icon={<Timeline />}
                        />
                      )}
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < selectedStudent.enrollments.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          ) : (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <School sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                لم يسجل الطالب في أي كورس بعد
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setProgressDialog(false)}>
            إغلاق
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StudentManagement;
