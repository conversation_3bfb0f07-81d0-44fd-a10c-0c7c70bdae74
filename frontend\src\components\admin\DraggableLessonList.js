import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  IconButton,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Switch,
  Divider,
  Tooltip,
  Menu,
  MenuItem,
  Alert
} from '@mui/material';
import {
  DragIndicator,
  PlayArrow,
  PictureAsPdf,
  InsertDriveFile,
  Edit,
  Delete,
  Visibility,
  VisibilityOff,
  Lock,
  LockOpen,
  MoreVert,
  Add,
  Save,
  Cancel,
  Timer,
  Assignment,
  Quiz
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import toast from 'react-hot-toast';

const DraggableLessonList = ({ 
  courseId, 
  lessons: initialLessons = [], 
  onLessonsReorder,
  onLessonUpdate,
  onLessonDelete,
  editable = true 
}) => {
  const [lessons, setLessons] = useState(initialLessons);
  const [editDialog, setEditDialog] = useState(false);
  const [editingLesson, setEditingLesson] = useState(null);
  const [menuAnchor, setMenuAnchor] = useState(null);
  const [selectedLesson, setSelectedLesson] = useState(null);
  const [hasChanges, setHasChanges] = useState(false);

  const [lessonForm, setLessonForm] = useState({
    title: '',
    description: '',
    duration: '',
    isFree: false,
    isVisible: true,
    type: 'video' // video, pdf, document, quiz, assignment
  });

  useEffect(() => {
    setLessons(initialLessons);
  }, [initialLessons]);

  // معالجة السحب والإفلات
  const handleDragEnd = (result) => {
    if (!result.destination || !editable) return;

    const items = Array.from(lessons);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // تحديث ترتيب الدروس
    const updatedLessons = items.map((lesson, index) => ({
      ...lesson,
      order: index + 1
    }));

    setLessons(updatedLessons);
    setHasChanges(true);

    if (onLessonsReorder) {
      onLessonsReorder(updatedLessons);
    }

    toast.success('تم تغيير ترتيب الدروس');
  };

  // حفظ التغييرات
  const handleSaveChanges = async () => {
    try {
      // محاكاة حفظ في قاعدة البيانات
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setHasChanges(false);
      toast.success('تم حفظ ترتيب الدروس بنجاح');
    } catch (error) {
      console.error('خطأ في حفظ الترتيب:', error);
      toast.error('خطأ في حفظ الترتيب');
    }
  };

  // إلغاء التغييرات
  const handleCancelChanges = () => {
    setLessons(initialLessons);
    setHasChanges(false);
    toast.info('تم إلغاء التغييرات');
  };

  // تحرير درس
  const handleEditLesson = (lesson) => {
    setEditingLesson(lesson);
    setLessonForm({
      title: lesson.title,
      description: lesson.description || '',
      duration: lesson.duration || '',
      isFree: lesson.isFree || false,
      isVisible: lesson.isVisible !== false,
      type: lesson.type || 'video'
    });
    setEditDialog(true);
    setMenuAnchor(null);
  };

  // حفظ تعديل الدرس
  const handleSaveLesson = () => {
    const updatedLessons = lessons.map(lesson =>
      lesson.id === editingLesson.id
        ? { ...lesson, ...lessonForm }
        : lesson
    );

    setLessons(updatedLessons);
    setEditDialog(false);
    setEditingLesson(null);

    if (onLessonUpdate) {
      onLessonUpdate(editingLesson.id, lessonForm);
    }

    toast.success('تم تحديث الدرس بنجاح');
  };

  // حذف درس
  const handleDeleteLesson = (lessonId) => {
    const updatedLessons = lessons.filter(lesson => lesson.id !== lessonId);
    setLessons(updatedLessons);
    setMenuAnchor(null);

    if (onLessonDelete) {
      onLessonDelete(lessonId);
    }

    toast.success('تم حذف الدرس');
  };

  // تبديل حالة الرؤية
  const toggleVisibility = (lessonId) => {
    const updatedLessons = lessons.map(lesson =>
      lesson.id === lessonId
        ? { ...lesson, isVisible: !lesson.isVisible }
        : lesson
    );

    setLessons(updatedLessons);
    
    if (onLessonUpdate) {
      const lesson = lessons.find(l => l.id === lessonId);
      onLessonUpdate(lessonId, { isVisible: !lesson.isVisible });
    }
  };

  // تبديل حالة المجانية
  const toggleFree = (lessonId) => {
    const updatedLessons = lessons.map(lesson =>
      lesson.id === lessonId
        ? { ...lesson, isFree: !lesson.isFree }
        : lesson
    );

    setLessons(updatedLessons);
    
    if (onLessonUpdate) {
      const lesson = lessons.find(l => l.id === lessonId);
      onLessonUpdate(lessonId, { isFree: !lesson.isFree });
    }
  };

  // الحصول على أيقونة نوع الدرس
  const getLessonIcon = (type) => {
    switch (type) {
      case 'video': return <PlayArrow color="error" />;
      case 'pdf': return <PictureAsPdf color="error" />;
      case 'quiz': return <Quiz color="primary" />;
      case 'assignment': return <Assignment color="warning" />;
      default: return <InsertDriveFile color="action" />;
    }
  };

  // الحصول على لون نوع الدرس
  const getLessonTypeColor = (type) => {
    switch (type) {
      case 'video': return 'error';
      case 'pdf': return 'warning';
      case 'quiz': return 'primary';
      case 'assignment': return 'secondary';
      default: return 'default';
    }
  };

  // تنسيق مدة الدرس
  const formatDuration = (duration) => {
    if (!duration) return '';
    if (duration.includes(':')) return duration;
    return `${duration} دقيقة`;
  };

  return (
    <Box>
      {/* رأس القائمة */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
          دروس الكورس ({lessons.length})
        </Typography>
        
        {hasChanges && editable && (
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              size="small"
              startIcon={<Cancel />}
              onClick={handleCancelChanges}
            >
              إلغاء
            </Button>
            <Button
              variant="contained"
              size="small"
              startIcon={<Save />}
              onClick={handleSaveChanges}
              sx={{
                background: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)'
                }
              }}
            >
              حفظ الترتيب
            </Button>
          </Box>
        )}
      </Box>

      {/* تنبيه التغييرات */}
      {hasChanges && (
        <Alert severity="info" sx={{ mb: 2 }}>
          تم تغيير ترتيب الدروس. لا تنس حفظ التغييرات.
        </Alert>
      )}

      {/* قائمة الدروس القابلة للسحب */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="lessons">
          {(provided, snapshot) => (
            <List
              {...provided.droppableProps}
              ref={provided.innerRef}
              sx={{
                backgroundColor: snapshot.isDraggingOver ? 'rgba(65, 105, 225, 0.1)' : 'transparent',
                borderRadius: 2,
                transition: 'background-color 0.2s ease'
              }}
            >
              {lessons.map((lesson, index) => (
                <Draggable
                  key={lesson.id}
                  draggableId={lesson.id.toString()}
                  index={index}
                  isDragDisabled={!editable}
                >
                  {(provided, snapshot) => (
                    <Card
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      sx={{
                        mb: 1,
                        opacity: !lesson.isVisible ? 0.6 : 1,
                        transform: snapshot.isDragging ? 'rotate(5deg)' : 'none',
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          boxShadow: 3
                        }
                      }}
                    >
                      <ListItem sx={{ p: 2 }}>
                        {/* مقبض السحب */}
                        {editable && (
                          <ListItemIcon {...provided.dragHandleProps}>
                            <DragIndicator sx={{ cursor: 'grab', color: 'text.secondary' }} />
                          </ListItemIcon>
                        )}

                        {/* رقم الدرس */}
                        <Avatar
                          sx={{
                            width: 32,
                            height: 32,
                            fontSize: '0.875rem',
                            bgcolor: 'primary.main',
                            mr: 2
                          }}
                        >
                          {index + 1}
                        </Avatar>

                        {/* أيقونة نوع الدرس */}
                        <ListItemIcon sx={{ minWidth: 40 }}>
                          {getLessonIcon(lesson.type)}
                        </ListItemIcon>

                        {/* محتوى الدرس */}
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                              <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                                {lesson.title}
                              </Typography>
                              
                              {/* شارات الحالة */}
                              <Box sx={{ display: 'flex', gap: 0.5 }}>
                                <Chip
                                  size="small"
                                  label={lesson.type === 'video' ? 'فيديو' : 
                                        lesson.type === 'pdf' ? 'PDF' :
                                        lesson.type === 'quiz' ? 'اختبار' :
                                        lesson.type === 'assignment' ? 'واجب' : 'ملف'}
                                  color={getLessonTypeColor(lesson.type)}
                                />
                                
                                {lesson.isFree && (
                                  <Chip
                                    size="small"
                                    label="مجاني"
                                    color="success"
                                    icon={<LockOpen />}
                                  />
                                )}
                                
                                {!lesson.isVisible && (
                                  <Chip
                                    size="small"
                                    label="مخفي"
                                    color="default"
                                    icon={<VisibilityOff />}
                                  />
                                )}
                              </Box>
                            </Box>
                          }
                          secondary={
                            <Box>
                              {lesson.description && (
                                <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                                  {lesson.description}
                                </Typography>
                              )}
                              
                              {lesson.duration && (
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                  <Timer sx={{ fontSize: 16, color: 'text.secondary' }} />
                                  <Typography variant="caption" color="text.secondary">
                                    {formatDuration(lesson.duration)}
                                  </Typography>
                                </Box>
                              )}
                            </Box>
                          }
                        />

                        {/* أزرار الإجراءات */}
                        {editable && (
                          <ListItemSecondaryAction>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              {/* تبديل الرؤية */}
                              <Tooltip title={lesson.isVisible ? 'إخفاء' : 'إظهار'}>
                                <IconButton
                                  size="small"
                                  onClick={() => toggleVisibility(lesson.id)}
                                >
                                  {lesson.isVisible ? <Visibility /> : <VisibilityOff />}
                                </IconButton>
                              </Tooltip>

                              {/* تبديل المجانية */}
                              <Tooltip title={lesson.isFree ? 'جعله مدفوع' : 'جعله مجاني'}>
                                <IconButton
                                  size="small"
                                  onClick={() => toggleFree(lesson.id)}
                                >
                                  {lesson.isFree ? <LockOpen /> : <Lock />}
                                </IconButton>
                              </Tooltip>

                              {/* قائمة الخيارات */}
                              <IconButton
                                size="small"
                                onClick={(e) => {
                                  setMenuAnchor(e.currentTarget);
                                  setSelectedLesson(lesson);
                                }}
                              >
                                <MoreVert />
                              </IconButton>
                            </Box>
                          </ListItemSecondaryAction>
                        )}
                      </ListItem>
                    </Card>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </List>
          )}
        </Droppable>
      </DragDropContext>

      {/* رسالة عدم وجود دروس */}
      {lessons.length === 0 && (
        <Card sx={{ textAlign: 'center', py: 4 }}>
          <CardContent>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
              لا توجد دروس في هذا الكورس
            </Typography>
            <Typography variant="body2" color="text.secondary">
              ابدأ بإضافة دروس لهذا الكورس
            </Typography>
          </CardContent>
        </Card>
      )}

      {/* قائمة خيارات الدرس */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem onClick={() => handleEditLesson(selectedLesson)}>
          <Edit sx={{ mr: 1 }} />
          تحرير
        </MenuItem>
        <MenuItem 
          onClick={() => handleDeleteLesson(selectedLesson?.id)}
          sx={{ color: 'error.main' }}
        >
          <Delete sx={{ mr: 1 }} />
          حذف
        </MenuItem>
      </Menu>

      {/* نافذة تحرير الدرس */}
      <Dialog open={editDialog} onClose={() => setEditDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>تحرير الدرس</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="عنوان الدرس"
              value={lessonForm.title}
              onChange={(e) => setLessonForm({ ...lessonForm, title: e.target.value })}
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              multiline
              rows={3}
              label="وصف الدرس"
              value={lessonForm.description}
              onChange={(e) => setLessonForm({ ...lessonForm, description: e.target.value })}
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="مدة الدرس"
              value={lessonForm.duration}
              onChange={(e) => setLessonForm({ ...lessonForm, duration: e.target.value })}
              placeholder="مثال: 15 دقيقة أو 00:15:30"
              sx={{ mb: 2 }}
            />

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={lessonForm.isFree}
                    onChange={(e) => setLessonForm({ ...lessonForm, isFree: e.target.checked })}
                  />
                }
                label="درس مجاني"
              />

              <FormControlLabel
                control={
                  <Switch
                    checked={lessonForm.isVisible}
                    onChange={(e) => setLessonForm({ ...lessonForm, isVisible: e.target.checked })}
                  />
                }
                label="مرئي للطلاب"
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog(false)}>
            إلغاء
          </Button>
          <Button 
            variant="contained" 
            onClick={handleSaveLesson}
            sx={{
              background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)'
              }
            }}
          >
            حفظ
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DraggableLessonList;
