import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  LinearProgress,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  TextField,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Badge,
  Avatar
} from '@mui/material';
import {
  Settings,
  Backup,
  Analytics,
  Security,
  People,
  Notifications,
  Schedule,
  Storage,
  Speed,
  ExpandMore,
  CheckCircle,
  Warning,
  Error as ErrorIcon,
  Info,
  Download,
  Upload,
  Refresh,
  Delete,
  Edit,
  Visibility,
  AdminPanelSettings,
  Shield,
  Key,
  History,
  CloudDownload,
  RestoreFromTrash,
  VerifiedUser,
  DataUsage,
  TrendingUp,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  PieChart as RechartsPieChart,
  Cell,
  BarChart as RechartsBarChart,
  Bar
} from 'recharts';
import { useLanguage } from '../../contexts/LanguageContext';
import toast from 'react-hot-toast';

const AdvancedSystemManagement = () => {
  const { language, t } = useLanguage();
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState(0);
  
  // حالات البيانات
  const [systemStats, setSystemStats] = useState({});
  const [backups, setBackups] = useState([]);
  const [activities, setActivities] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [analytics, setAnalytics] = useState({});
  
  // حالات النوافذ المنبثقة
  const [backupDialog, setBackupDialog] = useState(false);
  const [permissionDialog, setPermissionDialog] = useState(false);
  const [settingsDialog, setSettingsDialog] = useState(false);
  
  // حالات النماذج
  const [backupForm, setBackupForm] = useState({
    type: 'full',
    description: '',
    includeFiles: true,
    includeDatabase: true,
    schedule: false,
    frequency: 'daily'
  });

  const [permissionForm, setPermissionForm] = useState({
    userId: '',
    role: 'viewer',
    permissions: [],
    expiryDate: ''
  });

  const [systemSettings, setSystemSettings] = useState({
    autoBackup: true,
    backupFrequency: 'daily',
    maxFileSize: '100MB',
    allowedFileTypes: 'pdf,mp4,jpg,png',
    emailNotifications: true,
    smsNotifications: false,
    maintenanceMode: false,
    debugMode: false
  });

  useEffect(() => {
    loadSystemData();
  }, []);

  const loadSystemData = async () => {
    try {
      setLoading(true);
      
      // محاكاة تحميل بيانات النظام
      const statsData = {
        totalUsers: 1250,
        activeUsers: 980,
        totalCourses: 45,
        totalStorage: '2.5 GB',
        usedStorage: '1.8 GB',
        systemUptime: '99.9%',
        avgResponseTime: '120ms',
        errorRate: '0.1%',
        lastBackup: new Date().toISOString(),
        systemHealth: 'excellent'
      };

      const backupsData = [
        {
          id: 1,
          name: 'backup_2024_01_15_full.zip',
          type: 'full',
          size: '250 MB',
          created: '2024-01-15T10:30:00Z',
          status: 'completed',
          description: 'نسخة احتياطية كاملة شاملة'
        },
        {
          id: 2,
          name: 'backup_2024_01_14_incremental.zip',
          type: 'incremental',
          size: '45 MB',
          created: '2024-01-14T10:30:00Z',
          status: 'completed',
          description: 'نسخة احتياطية تدريجية'
        },
        {
          id: 3,
          name: 'backup_2024_01_13_database.sql',
          type: 'database',
          size: '120 MB',
          created: '2024-01-13T10:30:00Z',
          status: 'completed',
          description: 'نسخة احتياطية لقاعدة البيانات فقط'
        }
      ];

      const activitiesData = [
        {
          id: 1,
          user: 'أحمد محمد',
          action: 'تسجيل دخول إلى لوحة الإدارة',
          timestamp: new Date().toISOString(),
          ip: '*************',
          status: 'success',
          details: 'تسجيل دخول ناجح من المتصفح Chrome'
        },
        {
          id: 2,
          user: 'فاطمة علي',
          action: 'إنشاء كورس جديد',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          ip: '*************',
          status: 'success',
          details: 'تم إنشاء كورس "أساسيات التصميم"'
        },
        {
          id: 3,
          user: 'محمد سالم',
          action: 'محاولة تسجيل دخول فاشلة',
          timestamp: new Date(Date.now() - 7200000).toISOString(),
          ip: '*************',
          status: 'failed',
          details: 'كلمة مرور خاطئة'
        }
      ];

      const permissionsData = [
        {
          id: 1,
          user: 'أحمد محمد',
          role: 'super_admin',
          permissions: ['read', 'write', 'delete', 'manage_users', 'system_settings'],
          lastAccess: new Date().toISOString(),
          status: 'active'
        },
        {
          id: 2,
          user: 'فاطمة علي',
          role: 'admin',
          permissions: ['read', 'write', 'manage_courses'],
          lastAccess: new Date(Date.now() - 86400000).toISOString(),
          status: 'active'
        },
        {
          id: 3,
          user: 'محمد سالم',
          role: 'editor',
          permissions: ['read', 'write'],
          lastAccess: new Date(Date.now() - 172800000).toISOString(),
          status: 'inactive'
        }
      ];

      const analyticsData = {
        userGrowth: [
          { month: 'يناير', users: 800 },
          { month: 'فبراير', users: 950 },
          { month: 'مارس', users: 1100 },
          { month: 'أبريل', users: 1250 }
        ],
        courseCompletion: [
          { name: 'مكتمل', value: 65, color: '#4CAF50' },
          { name: 'قيد التقدم', value: 25, color: '#FF9800' },
          { name: 'لم يبدأ', value: 10, color: '#f44336' }
        ],
        systemPerformance: [
          { time: '00:00', cpu: 45, memory: 60, disk: 30 },
          { time: '06:00', cpu: 55, memory: 65, disk: 32 },
          { time: '12:00', cpu: 70, memory: 75, disk: 35 },
          { time: '18:00', cpu: 60, memory: 70, disk: 33 }
        ]
      };

      setSystemStats(statsData);
      setBackups(backupsData);
      setActivities(activitiesData);
      setPermissions(permissionsData);
      setAnalytics(analyticsData);
      
    } catch (error) {
      console.error('خطأ في تحميل بيانات النظام:', error);
      toast.error('خطأ في تحميل بيانات النظام');
    } finally {
      setLoading(false);
    }
  };

  // إنشاء نسخة احتياطية
  const handleCreateBackup = async () => {
    try {
      setLoading(true);
      
      // محاكاة إنشاء النسخة الاحتياطية
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const newBackup = {
        id: Date.now(),
        name: `backup_${new Date().toISOString().split('T')[0]}_${backupForm.type}.zip`,
        type: backupForm.type,
        size: backupForm.type === 'full' ? '300 MB' : '50 MB',
        created: new Date().toISOString(),
        status: 'completed',
        description: backupForm.description || `نسخة احتياطية ${backupForm.type === 'full' ? 'كاملة' : 'تدريجية'}`
      };
      
      setBackups([newBackup, ...backups]);
      setBackupDialog(false);
      setBackupForm({
        type: 'full',
        description: '',
        includeFiles: true,
        includeDatabase: true,
        schedule: false,
        frequency: 'daily'
      });
      
      toast.success('تم إنشاء النسخة الاحتياطية بنجاح');
      
    } catch (error) {
      console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
      toast.error('خطأ في إنشاء النسخة الاحتياطية');
    } finally {
      setLoading(false);
    }
  };

  // حذف نسخة احتياطية
  const handleDeleteBackup = async (backupId) => {
    if (window.confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) {
      try {
        const updatedBackups = backups.filter(backup => backup.id !== backupId);
        setBackups(updatedBackups);
        toast.success('تم حذف النسخة الاحتياطية');
      } catch (error) {
        console.error('خطأ في حذف النسخة الاحتياطية:', error);
        toast.error('خطأ في حذف النسخة الاحتياطية');
      }
    }
  };

  // استعادة نسخة احتياطية
  const handleRestoreBackup = async (backupId) => {
    if (window.confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
      try {
        setLoading(true);
        
        // محاكاة عملية الاستعادة
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        toast.success('تم استعادة النسخة الاحتياطية بنجاح');
        
      } catch (error) {
        console.error('خطأ في استعادة النسخة الاحتياطية:', error);
        toast.error('خطأ في استعادة النسخة الاحتياطية');
      } finally {
        setLoading(false);
      }
    }
  };

  // تحديث الصلاحيات
  const handleUpdatePermissions = async () => {
    try {
      setLoading(true);
      
      // محاكاة تحديث الصلاحيات
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setPermissionDialog(false);
      setPermissionForm({
        userId: '',
        role: 'viewer',
        permissions: [],
        expiryDate: ''
      });
      
      toast.success('تم تحديث الصلاحيات بنجاح');
      
    } catch (error) {
      console.error('خطأ في تحديث الصلاحيات:', error);
      toast.error('خطأ في تحديث الصلاحيات');
    } finally {
      setLoading(false);
    }
  };

  // حفظ إعدادات النظام
  const handleSaveSettings = async () => {
    try {
      setLoading(true);
      
      // محاكاة حفظ الإعدادات
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSettingsDialog(false);
      toast.success('تم حفظ إعدادات النظام بنجاح');
      
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error);
      toast.error('خطأ في حفظ الإعدادات');
    } finally {
      setLoading(false);
    }
  };

  // الحصول على لون حالة النظام
  const getHealthColor = (health) => {
    switch (health) {
      case 'excellent': return 'success';
      case 'good': return 'info';
      case 'warning': return 'warning';
      case 'critical': return 'error';
      default: return 'default';
    }
  };

  // الحصول على نص حالة النظام
  const getHealthText = (health) => {
    switch (health) {
      case 'excellent': return 'ممتاز';
      case 'good': return 'جيد';
      case 'warning': return 'تحذير';
      case 'critical': return 'حرج';
      default: return 'غير معروف';
    }
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // تنسيق الوقت النسبي
  const formatRelativeTime = (dateString) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'منذ أقل من ساعة';
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `منذ ${diffInDays} يوم`;
    return formatDate(dateString);
  };

  if (loading && Object.keys(systemStats).length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <LinearProgress sx={{ width: '50%' }} />
      </Box>
    );
  }

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          إدارة النظام المتقدمة
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadSystemData}
          >
            تحديث البيانات
          </Button>
          <Button
            variant="contained"
            startIcon={<Settings />}
            onClick={() => setSettingsDialog(true)}
            sx={{
              background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)'
              }
            }}
          >
            إعدادات النظام
          </Button>
        </Box>
      </Box>

      {/* إحصائيات النظام */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {systemStats.totalUsers}
                  </Typography>
                  <Typography variant="body2">
                    إجمالي المستخدمين
                  </Typography>
                </Box>
                <People sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {systemStats.systemUptime}
                  </Typography>
                  <Typography variant="body2">
                    وقت تشغيل النظام
                  </Typography>
                </Box>
                <TrendingUp sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {systemStats.usedStorage}
                  </Typography>
                  <Typography variant="body2">
                    التخزين المستخدم
                  </Typography>
                  <Typography variant="caption">
                    من أصل {systemStats.totalStorage}
                  </Typography>
                </Box>
                <Storage sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #E91E63 0%, #C2185B 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {systemStats.avgResponseTime}
                  </Typography>
                  <Typography variant="body2">
                    متوسط وقت الاستجابة
                  </Typography>
                </Box>
                <Speed sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* حالة النظام */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
              حالة النظام
            </Typography>
            <Chip
              label={getHealthText(systemStats.systemHealth)}
              color={getHealthColor(systemStats.systemHealth)}
              icon={<CheckCircle />}
              sx={{ fontWeight: 'bold' }}
            />
          </Box>

          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">استخدام المعالج</Typography>
                  <Typography variant="body2" sx={{ fontWeight: 'bold' }}>65%</Typography>
                </Box>
                <LinearProgress variant="determinate" value={65} sx={{ height: 8, borderRadius: 4 }} />
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">استخدام الذاكرة</Typography>
                  <Typography variant="body2" sx={{ fontWeight: 'bold' }}>72%</Typography>
                </Box>
                <LinearProgress variant="determinate" value={72} sx={{ height: 8, borderRadius: 4 }} color="warning" />
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">استخدام القرص الصلب</Typography>
                  <Typography variant="body2" sx={{ fontWeight: 'bold' }}>45%</Typography>
                </Box>
                <LinearProgress variant="determinate" value={45} sx={{ height: 8, borderRadius: 4 }} color="success" />
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* التبويبات */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={selectedTab}
          onChange={(e, newValue) => setSelectedTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="النسخ الاحتياطية" />
          <Tab label="الإحصائيات المتقدمة" />
          <Tab label="سجل النشاطات" />
          <Tab label="إدارة الصلاحيات" />
        </Tabs>
      </Paper>

      {/* تبويب النسخ الاحتياطية */}
      {selectedTab === 0 && (
        <Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
              النسخ الاحتياطية ({backups.length})
            </Typography>
            <Button
              variant="contained"
              startIcon={<Backup />}
              onClick={() => setBackupDialog(true)}
              sx={{
                background: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)'
                }
              }}
            >
              إنشاء نسخة احتياطية
            </Button>
          </Box>

          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                  <TableCell sx={{ fontWeight: 'bold' }}>اسم الملف</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>النوع</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>الحجم</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>تاريخ الإنشاء</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>الحالة</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {backups.map((backup) => (
                  <TableRow key={backup.id} hover>
                    <TableCell>
                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                          {backup.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {backup.description}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={backup.type === 'full' ? 'كامل' : backup.type === 'incremental' ? 'تدريجي' : 'قاعدة بيانات'}
                        color={backup.type === 'full' ? 'primary' : backup.type === 'incremental' ? 'warning' : 'info'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{backup.size}</TableCell>
                    <TableCell>{formatDate(backup.created)}</TableCell>
                    <TableCell>
                      <Chip
                        label={backup.status === 'completed' ? 'مكتمل' : 'قيد التقدم'}
                        color={backup.status === 'completed' ? 'success' : 'warning'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Tooltip title="تحميل">
                          <IconButton size="small" color="primary">
                            <CloudDownload />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="استعادة">
                          <IconButton
                            size="small"
                            color="info"
                            onClick={() => handleRestoreBackup(backup.id)}
                          >
                            <RestoreFromTrash />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="حذف">
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteBackup(backup.id)}
                          >
                            <Delete />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      )}

      {/* تبويب الإحصائيات المتقدمة */}
      {selectedTab === 1 && (
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 3 }}>
            الإحصائيات والتحليلات
          </Typography>

          <Grid container spacing={3}>
            {/* نمو المستخدمين */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                    نمو المستخدمين
                  </Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={analytics.userGrowth}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <RechartsTooltip />
                      <Area type="monotone" dataKey="users" stroke="#4169E1" fill="#4169E1" fillOpacity={0.3} />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>

            {/* إكمال الكورسات */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                    حالة إكمال الكورسات
                  </Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsPieChart>
                      <RechartsTooltip />
                      <RechartsPieChart data={analytics.courseCompletion}>
                        {analytics.courseCompletion.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </RechartsPieChart>
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>

            {/* أداء النظام */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                    أداء النظام (آخر 24 ساعة)
                  </Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={analytics.systemPerformance}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="time" />
                      <YAxis />
                      <RechartsTooltip />
                      <Line type="monotone" dataKey="cpu" stroke="#f44336" name="المعالج %" />
                      <Line type="monotone" dataKey="memory" stroke="#ff9800" name="الذاكرة %" />
                      <Line type="monotone" dataKey="disk" stroke="#4caf50" name="القرص الصلب %" />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* تبويب سجل النشاطات */}
      {selectedTab === 2 && (
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 3 }}>
            سجل النشاطات الأخيرة
          </Typography>

          <List>
            {activities.map((activity, index) => (
              <React.Fragment key={activity.id}>
                <ListItem>
                  <ListItemIcon>
                    <Avatar
                      sx={{
                        bgcolor: activity.status === 'success' ? 'success.main' :
                                activity.status === 'failed' ? 'error.main' : 'warning.main',
                        width: 40,
                        height: 40
                      }}
                    >
                      {activity.status === 'success' ? <CheckCircle /> :
                       activity.status === 'failed' ? <ErrorIcon /> : <Warning />}
                    </Avatar>
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          {activity.user}
                        </Typography>
                        <Typography variant="body2">
                          {activity.action}
                        </Typography>
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="caption" display="block">
                          {activity.details}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatRelativeTime(activity.timestamp)} • IP: {activity.ip}
                        </Typography>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Chip
                      label={activity.status === 'success' ? 'نجح' : activity.status === 'failed' ? 'فشل' : 'تحذير'}
                      color={activity.status === 'success' ? 'success' : activity.status === 'failed' ? 'error' : 'warning'}
                      size="small"
                    />
                  </ListItemSecondaryAction>
                </ListItem>
                {index < activities.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </Box>
      )}

      {/* تبويب إدارة الصلاحيات */}
      {selectedTab === 3 && (
        <Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
              إدارة الصلاحيات
            </Typography>
            <Button
              variant="contained"
              startIcon={<AdminPanelSettings />}
              onClick={() => setPermissionDialog(true)}
              sx={{
                background: 'linear-gradient(135deg, #9C27B0 0%, #673AB7 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #673AB7 0%, #9C27B0 100%)'
                }
              }}
            >
              إضافة صلاحية
            </Button>
          </Box>

          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                  <TableCell sx={{ fontWeight: 'bold' }}>المستخدم</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>الدور</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>الصلاحيات</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>آخر دخول</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>الحالة</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {permissions.map((permission) => (
                  <TableRow key={permission.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          {permission.user.charAt(0)}
                        </Avatar>
                        <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                          {permission.user}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={
                          permission.role === 'super_admin' ? 'مدير عام' :
                          permission.role === 'admin' ? 'مدير' :
                          permission.role === 'editor' ? 'محرر' : 'مشاهد'
                        }
                        color={
                          permission.role === 'super_admin' ? 'error' :
                          permission.role === 'admin' ? 'primary' :
                          permission.role === 'editor' ? 'warning' : 'default'
                        }
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {permission.permissions.slice(0, 3).map((perm, index) => (
                          <Chip
                            key={index}
                            label={
                              perm === 'read' ? 'قراءة' :
                              perm === 'write' ? 'كتابة' :
                              perm === 'delete' ? 'حذف' :
                              perm === 'manage_users' ? 'إدارة المستخدمين' :
                              perm === 'system_settings' ? 'إعدادات النظام' :
                              perm === 'manage_courses' ? 'إدارة الكورسات' : perm
                            }
                            size="small"
                            variant="outlined"
                            sx={{ fontSize: '0.7rem', height: 20 }}
                          />
                        ))}
                        {permission.permissions.length > 3 && (
                          <Chip
                            label={`+${permission.permissions.length - 3}`}
                            size="small"
                            variant="outlined"
                            sx={{ fontSize: '0.7rem', height: 20 }}
                          />
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="caption">
                        {formatRelativeTime(permission.lastAccess)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={permission.status === 'active' ? 'نشط' : 'غير نشط'}
                        color={permission.status === 'active' ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Tooltip title="تحرير">
                          <IconButton size="small" color="primary">
                            <Edit />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="عرض">
                          <IconButton size="small" color="info">
                            <Visibility />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="حذف">
                          <IconButton size="small" color="error">
                            <Delete />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      )}

      {/* نافذة إنشاء نسخة احتياطية */}
      <Dialog open={backupDialog} onClose={() => setBackupDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Backup sx={{ color: 'primary.main' }} />
            <Typography variant="h6">
              إنشاء نسخة احتياطية
            </Typography>
          </Box>
        </DialogTitle>

        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>نوع النسخة الاحتياطية</InputLabel>
                  <Select
                    value={backupForm.type}
                    onChange={(e) => setBackupForm({ ...backupForm, type: e.target.value })}
                  >
                    <MenuItem value="full">نسخة كاملة</MenuItem>
                    <MenuItem value="incremental">نسخة تدريجية</MenuItem>
                    <MenuItem value="database">قاعدة البيانات فقط</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="وصف النسخة الاحتياطية"
                  value={backupForm.description}
                  onChange={(e) => setBackupForm({ ...backupForm, description: e.target.value })}
                  multiline
                  rows={3}
                />
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={backupForm.includeFiles}
                        onChange={(e) => setBackupForm({ ...backupForm, includeFiles: e.target.checked })}
                      />
                    }
                    label="تضمين الملفات"
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={backupForm.includeDatabase}
                        onChange={(e) => setBackupForm({ ...backupForm, includeDatabase: e.target.checked })}
                      />
                    }
                    label="تضمين قاعدة البيانات"
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={backupForm.schedule}
                        onChange={(e) => setBackupForm({ ...backupForm, schedule: e.target.checked })}
                      />
                    }
                    label="جدولة النسخ الاحتياطية"
                  />
                </Box>
              </Grid>

              {backupForm.schedule && (
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>تكرار النسخ الاحتياطية</InputLabel>
                    <Select
                      value={backupForm.frequency}
                      onChange={(e) => setBackupForm({ ...backupForm, frequency: e.target.value })}
                    >
                      <MenuItem value="hourly">كل ساعة</MenuItem>
                      <MenuItem value="daily">يومياً</MenuItem>
                      <MenuItem value="weekly">أسبوعياً</MenuItem>
                      <MenuItem value="monthly">شهرياً</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              )}
            </Grid>
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setBackupDialog(false)}>
            إلغاء
          </Button>
          <Button
            variant="contained"
            onClick={handleCreateBackup}
            disabled={loading}
            sx={{
              background: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)'
              }
            }}
          >
            {loading ? 'جاري الإنشاء...' : 'إنشاء النسخة الاحتياطية'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* نافذة إدارة الصلاحيات */}
      <Dialog open={permissionDialog} onClose={() => setPermissionDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <AdminPanelSettings sx={{ color: 'primary.main' }} />
            <Typography variant="h6">
              إدارة الصلاحيات
            </Typography>
          </Box>
        </DialogTitle>

        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="معرف المستخدم"
                  value={permissionForm.userId}
                  onChange={(e) => setPermissionForm({ ...permissionForm, userId: e.target.value })}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>الدور</InputLabel>
                  <Select
                    value={permissionForm.role}
                    onChange={(e) => setPermissionForm({ ...permissionForm, role: e.target.value })}
                  >
                    <MenuItem value="viewer">مشاهد</MenuItem>
                    <MenuItem value="editor">محرر</MenuItem>
                    <MenuItem value="admin">مدير</MenuItem>
                    <MenuItem value="super_admin">مدير عام</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
                  الصلاحيات
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {['read', 'write', 'delete', 'manage_users', 'manage_courses', 'system_settings'].map((permission) => (
                    <FormControlLabel
                      key={permission}
                      control={
                        <Switch
                          checked={permissionForm.permissions.includes(permission)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setPermissionForm({
                                ...permissionForm,
                                permissions: [...permissionForm.permissions, permission]
                              });
                            } else {
                              setPermissionForm({
                                ...permissionForm,
                                permissions: permissionForm.permissions.filter(p => p !== permission)
                              });
                            }
                          }}
                        />
                      }
                      label={
                        permission === 'read' ? 'قراءة' :
                        permission === 'write' ? 'كتابة' :
                        permission === 'delete' ? 'حذف' :
                        permission === 'manage_users' ? 'إدارة المستخدمين' :
                        permission === 'manage_courses' ? 'إدارة الكورسات' :
                        permission === 'system_settings' ? 'إعدادات النظام' : permission
                      }
                    />
                  ))}
                </Box>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="تاريخ انتهاء الصلاحية"
                  type="date"
                  value={permissionForm.expiryDate}
                  onChange={(e) => setPermissionForm({ ...permissionForm, expiryDate: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setPermissionDialog(false)}>
            إلغاء
          </Button>
          <Button
            variant="contained"
            onClick={handleUpdatePermissions}
            disabled={loading || !permissionForm.userId}
            sx={{
              background: 'linear-gradient(135deg, #9C27B0 0%, #673AB7 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #673AB7 0%, #9C27B0 100%)'
              }
            }}
          >
            {loading ? 'جاري الحفظ...' : 'حفظ الصلاحيات'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* نافذة إعدادات النظام */}
      <Dialog open={settingsDialog} onClose={() => setSettingsDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Settings sx={{ color: 'primary.main' }} />
            <Typography variant="h6">
              إعدادات النظام
            </Typography>
          </Box>
        </DialogTitle>

        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Accordion defaultExpanded>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography variant="h6">النسخ الاحتياطية</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemSettings.autoBackup}
                          onChange={(e) => setSystemSettings({ ...systemSettings, autoBackup: e.target.checked })}
                        />
                      }
                      label="النسخ الاحتياطية التلقائية"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>تكرار النسخ الاحتياطية</InputLabel>
                      <Select
                        value={systemSettings.backupFrequency}
                        onChange={(e) => setSystemSettings({ ...systemSettings, backupFrequency: e.target.value })}
                      >
                        <MenuItem value="hourly">كل ساعة</MenuItem>
                        <MenuItem value="daily">يومياً</MenuItem>
                        <MenuItem value="weekly">أسبوعياً</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography variant="h6">الملفات والتخزين</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="الحد الأقصى لحجم الملف"
                      value={systemSettings.maxFileSize}
                      onChange={(e) => setSystemSettings({ ...systemSettings, maxFileSize: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="أنواع الملفات المسموحة"
                      value={systemSettings.allowedFileTypes}
                      onChange={(e) => setSystemSettings({ ...systemSettings, allowedFileTypes: e.target.value })}
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography variant="h6">الإشعارات</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemSettings.emailNotifications}
                          onChange={(e) => setSystemSettings({ ...systemSettings, emailNotifications: e.target.checked })}
                        />
                      }
                      label="إشعارات البريد الإلكتروني"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemSettings.smsNotifications}
                          onChange={(e) => setSystemSettings({ ...systemSettings, smsNotifications: e.target.checked })}
                        />
                      }
                      label="إشعارات الرسائل النصية"
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography variant="h6">النظام</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemSettings.maintenanceMode}
                          onChange={(e) => setSystemSettings({ ...systemSettings, maintenanceMode: e.target.checked })}
                        />
                      }
                      label="وضع الصيانة"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemSettings.debugMode}
                          onChange={(e) => setSystemSettings({ ...systemSettings, debugMode: e.target.checked })}
                        />
                      }
                      label="وضع التصحيح"
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setSettingsDialog(false)}>
            إلغاء
          </Button>
          <Button
            variant="contained"
            onClick={handleSaveSettings}
            disabled={loading}
            sx={{
              background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)'
              }
            }}
          >
            {loading ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdvancedSystemManagement;
