import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  LinearProgress,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  StepContent,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Storage,
  Security,
  Settings,
  CheckCircle,
  Error,
  Warning,
  Info,
  CloudQueue as Database,
  Cloud,
  TableChart
} from '@mui/icons-material';
import { initializeCompleteDatabase } from '../../utils/databaseInitializer';
import { useLanguage } from '../../contexts/LanguageContext';

const DatabaseInitializer = () => {
  const { language, t } = useLanguage();
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState({});
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const steps = [
    {
      label: language === 'ar' ? 'تهيئة Firebase Collections' : 'Initialize Firebase Collections',
      description: language === 'ar' ? 'إنشاء المجموعات والإعدادات الأساسية' : 'Create basic collections and settings',
      icon: <Cloud />
    },
    {
      label: language === 'ar' ? 'تهيئة Supabase Tables' : 'Initialize Supabase Tables',
      description: language === 'ar' ? 'إنشاء الجداول وقواعد الأمان' : 'Create tables and security rules',
      icon: <Database />
    },
    {
      label: language === 'ar' ? 'تطبيق قواعد الأمان' : 'Apply Security Rules',
      description: language === 'ar' ? 'تفعيل قواعد الأمان والصلاحيات' : 'Enable security rules and permissions',
      icon: <Security />
    },
    {
      label: language === 'ar' ? 'التحقق النهائي' : 'Final Verification',
      description: language === 'ar' ? 'التأكد من صحة التهيئة' : 'Verify initialization success',
      icon: <CheckCircle />
    }
  ];

  const handleInitializeDatabase = async () => {
    setLoading(true);
    setError(null);
    setSuccess(false);
    setResults({});
    setActiveStep(0);

    try {
      // الخطوة 1: تهيئة Firebase
      setActiveStep(0);
      await new Promise(resolve => setTimeout(resolve, 1000)); // محاكاة التحميل

      const result = await initializeCompleteDatabase();
      
      setResults(result);

      if (result.success) {
        setActiveStep(1);
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setActiveStep(2);
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setActiveStep(3);
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setSuccess(true);
      } else {
        throw new Error(result.error);
      }

    } catch (err) {
      console.error('خطأ في تهيئة قاعدة البيانات:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getStepStatus = (stepIndex) => {
    if (loading && stepIndex === activeStep) return 'loading';
    if (stepIndex < activeStep) return 'completed';
    if (error && stepIndex === activeStep) return 'error';
    return 'pending';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'loading': return 'primary';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <CheckCircle color="success" />;
      case 'loading': return <Settings className="rotating" />;
      case 'error': return <Error color="error" />;
      default: return <Info color="disabled" />;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <TableChart sx={{ mr: 2, fontSize: '2rem', color: 'primary.main' }} />
            <Box>
              <Typography variant="h5" component="h2" gutterBottom>
                {language === 'ar' ? 'مُهيئ قاعدة البيانات' : 'Database Initializer'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {language === 'ar' 
                  ? 'إعداد هيكل قاعدة البيانات الكامل بدون بيانات تجريبية'
                  : 'Setup complete database structure without test data'
                }
              </Typography>
            </Box>
          </Box>

          <Divider sx={{ mb: 3 }} />

          {/* معلومات مهمة */}
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              {language === 'ar' 
                ? '⚠️ هذه العملية ستقوم بإنشاء هيكل قاعدة البيانات الكامل. لن يتم إضافة أي بيانات تجريبية.'
                : '⚠️ This process will create the complete database structure. No test data will be added.'
              }
            </Typography>
          </Alert>

          {/* خطوات التهيئة */}
          <Stepper activeStep={activeStep} orientation="vertical">
            {steps.map((step, index) => (
              <Step key={index}>
                <StepLabel
                  icon={getStatusIcon(getStepStatus(index))}
                  optional={
                    <Typography variant="caption">
                      {step.description}
                    </Typography>
                  }
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {step.label}
                    <Chip 
                      size="small" 
                      label={getStepStatus(index)} 
                      color={getStatusColor(getStepStatus(index))}
                      variant="outlined"
                    />
                  </Box>
                </StepLabel>
                <StepContent>
                  {loading && index === activeStep && (
                    <Box sx={{ mt: 1, mb: 2 }}>
                      <LinearProgress />
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        {language === 'ar' ? 'جاري التنفيذ...' : 'Executing...'}
                      </Typography>
                    </Box>
                  )}
                </StepContent>
              </Step>
            ))}
          </Stepper>

          {/* النتائج */}
          {results.firebase && (
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                {language === 'ar' ? 'نتائج Firebase:' : 'Firebase Results:'}
              </Typography>
              <Alert severity={results.firebase.success ? 'success' : 'error'}>
                {results.firebase.message}
              </Alert>
            </Box>
          )}

          {results.supabase && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                {language === 'ar' ? 'نتائج Supabase:' : 'Supabase Results:'}
              </Typography>
              <Alert severity={results.supabase.success ? 'success' : 'warning'}>
                {results.supabase.message}
              </Alert>
            </Box>
          )}

          {/* رسائل الخطأ */}
          {error && (
            <Alert severity="error" sx={{ mt: 3 }}>
              <Typography variant="body2">
                {language === 'ar' ? 'خطأ: ' : 'Error: '}{error}
              </Typography>
            </Alert>
          )}

          {/* رسالة النجاح */}
          {success && (
            <Alert severity="success" sx={{ mt: 3 }}>
              <Typography variant="body2">
                {language === 'ar' 
                  ? '🎉 تم تهيئة قاعدة البيانات بنجاح! النظام جاهز للاستخدام.'
                  : '🎉 Database initialized successfully! System is ready for use.'
                }
              </Typography>
            </Alert>
          )}

          {/* أزرار التحكم */}
          <Box sx={{ mt: 4, display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              onClick={handleInitializeDatabase}
              disabled={loading}
              startIcon={<Database />}
              size="large"
            >
              {loading 
                ? (language === 'ar' ? 'جاري التهيئة...' : 'Initializing...')
                : (language === 'ar' ? 'بدء التهيئة' : 'Start Initialization')
              }
            </Button>

            {success && (
              <Button
                variant="outlined"
                onClick={() => window.location.reload()}
                startIcon={<CheckCircle />}
              >
                {language === 'ar' ? 'إعادة تحميل الصفحة' : 'Reload Page'}
              </Button>
            )}
          </Box>

          {/* معلومات إضافية */}
          <Box sx={{ mt: 4 }}>
            <Typography variant="h6" gutterBottom>
              {language === 'ar' ? 'ما سيتم إنشاؤه:' : 'What will be created:'}
            </Typography>
            <List dense>
              <ListItem>
                <ListItemIcon><Storage /></ListItemIcon>
                <ListItemText 
                  primary={language === 'ar' ? 'مجموعات Firebase' : 'Firebase Collections'}
                  secondary={language === 'ar' ? 'المستخدمين، الكورسات، التسجيلات، الإعدادات' : 'Users, Courses, Enrollments, Settings'}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><TableChart /></ListItemIcon>
                <ListItemText 
                  primary={language === 'ar' ? 'جداول Supabase' : 'Supabase Tables'}
                  secondary={language === 'ar' ? 'هيكل قاعدة البيانات مع الفهارس' : 'Database structure with indexes'}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Security /></ListItemIcon>
                <ListItemText 
                  primary={language === 'ar' ? 'قواعد الأمان' : 'Security Rules'}
                  secondary={language === 'ar' ? 'صلاحيات المدير والطلاب' : 'Admin and student permissions'}
                />
              </ListItem>
            </List>
          </Box>
        </CardContent>
      </Card>

      <style jsx>{`
        .rotating {
          animation: rotate 2s linear infinite;
        }
        @keyframes rotate {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </Box>
  );
};

export default DatabaseInitializer;
