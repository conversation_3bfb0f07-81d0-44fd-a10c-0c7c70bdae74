/* تصميم حديث وأنيق لمشروع Skills World Academy */
/* Modern and Elegant Design for Skills World Academy */

/* ===== متغيرات التصميم ===== */
:root {
  /* الألوان الأساسية */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  
  /* ألوان الخلفية */
  --bg-primary: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  --bg-secondary: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  --bg-card: rgba(255, 255, 255, 0.95);
  --bg-glass: rgba(255, 255, 255, 0.1);
  
  /* الظلال */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.2);
  --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.3);
  
  /* الحدود */
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 24px;
  
  /* التحولات */
  --transition-fast: all 0.2s ease;
  --transition-normal: all 0.3s ease;
  --transition-slow: all 0.5s ease;
  
  /* المسافات */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
}

/* ===== تأثيرات الخلفية المتقدمة ===== */
.modern-bg-primary {
  background: var(--bg-primary);
  position: relative;
}

.modern-bg-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  pointer-events: none;
}

.modern-bg-glass {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* ===== البطاقات المحسنة ===== */
.modern-card {
  background: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: var(--transition-normal);
}

.modern-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.modern-card:hover::before {
  opacity: 1;
}

.modern-card-gradient {
  background: var(--primary-gradient);
  color: white;
  border: none;
}

.modern-card-gradient::before {
  background: rgba(255, 255, 255, 0.2);
}

/* ===== الأزرار المحسنة ===== */
.modern-button {
  border-radius: var(--border-radius-md);
  font-weight: 600;
  text-transform: none;
  padding: 12px 24px;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
  border: none;
  cursor: pointer;
}

.modern-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-normal);
}

.modern-button:hover::before {
  left: 100%;
}

.modern-button-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-md);
}

.modern-button-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.modern-button-secondary {
  background: var(--secondary-gradient);
  color: white;
  box-shadow: var(--shadow-md);
}

.modern-button-outline {
  background: transparent;
  border: 2px solid;
  border-image: var(--primary-gradient) 1;
  color: #667eea;
}

/* ===== القوائم المحسنة ===== */
.modern-list-item {
  border-radius: var(--border-radius-md);
  margin: var(--spacing-sm) 0;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.modern-list-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: var(--primary-gradient);
  transition: var(--transition-normal);
}

.modern-list-item:hover {
  background: rgba(102, 126, 234, 0.05);
  transform: translateX(-4px);
}

.modern-list-item:hover::before {
  width: 4px;
}

.modern-list-item.selected {
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.modern-list-item.selected::before {
  width: 4px;
}

/* ===== الشريط العلوي المحسن ===== */
.modern-appbar {
  background: var(--primary-gradient);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modern-appbar-glass {
  background: rgba(102, 126, 234, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* ===== القائمة الجانبية المحسنة ===== */
.modern-drawer {
  background: var(--bg-secondary);
  border-right: none;
  box-shadow: var(--shadow-xl);
  position: relative;
}

.modern-drawer::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 1px;
  background: linear-gradient(180deg, transparent, rgba(102, 126, 234, 0.3), transparent);
}

.modern-drawer-header {
  background: var(--primary-gradient);
  color: white;
  padding: var(--spacing-xl);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.modern-drawer-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}

/* ===== التأثيرات البصرية ===== */
.modern-glow {
  box-shadow: var(--shadow-glow);
}

.modern-pulse {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.modern-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.modern-slide-in {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.modern-fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* ===== الشبكة المحسنة ===== */
.modern-grid {
  display: grid;
  gap: var(--spacing-lg);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.modern-grid-item {
  background: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
}

.modern-grid-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

/* ===== النماذج المحسنة ===== */
.modern-input {
  border-radius: var(--border-radius-md);
  border: 2px solid rgba(102, 126, 234, 0.2);
  transition: var(--transition-normal);
  background: rgba(255, 255, 255, 0.9);
}

.modern-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
}

/* ===== الإشعارات المحسنة ===== */
.modern-notification {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-notification-success {
  background: linear-gradient(135deg, rgba(67, 233, 123, 0.9), rgba(56, 249, 215, 0.9));
  color: white;
}

.modern-notification-error {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.9), rgba(238, 90, 36, 0.9));
  color: white;
}

.modern-notification-warning {
  background: linear-gradient(135deg, rgba(250, 112, 154, 0.9), rgba(254, 225, 64, 0.9));
  color: white;
}

.modern-notification-info {
  background: linear-gradient(135deg, rgba(79, 172, 254, 0.9), rgba(0, 242, 254, 0.9));
  color: white;
}

/* ===== تحسينات الاستجابة ===== */
@media (max-width: 768px) {
  .modern-card {
    border-radius: var(--border-radius-md);
    margin: var(--spacing-sm);
  }
  
  .modern-button {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
  
  .modern-drawer-header {
    padding: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .modern-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .modern-card {
    border-radius: var(--border-radius-sm);
  }
}

/* ===== تحسينات الوضع المظلم ===== */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-card: rgba(30, 30, 30, 0.95);
    --bg-glass: rgba(30, 30, 30, 0.1);
  }
  
  .modern-card {
    background: var(--bg-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .modern-input {
    background: rgba(30, 30, 30, 0.9);
    border-color: rgba(102, 126, 234, 0.3);
    color: white;
  }
}

/* ===== تحسينات إمكانية الوصول ===== */
@media (prefers-reduced-motion: reduce) {
  .modern-card,
  .modern-button,
  .modern-list-item {
    transition: none;
  }
  
  .modern-pulse,
  .modern-float,
  .modern-slide-in,
  .modern-fade-in {
    animation: none;
  }
}

/* ===== تحسينات الطباعة ===== */
@media print {
  .modern-card {
    box-shadow: none;
    border: 1px solid #ccc;
    background: white;
  }
  
  .modern-button {
    background: white;
    color: black;
    border: 1px solid #ccc;
  }
}
