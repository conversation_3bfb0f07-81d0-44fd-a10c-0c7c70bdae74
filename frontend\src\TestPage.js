import React, { useState } from 'react';
import { Box, Typography, Button, CircularProgress, Alert } from '@mui/material';
import { createAllTestData } from './utils/createTestData';

const TestPage = () => {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleCreateTestData = async () => {
    setLoading(true);
    setMessage('');

    try {
      const result = await createAllTestData();
      if (result.success) {
        setMessage('✅ تم إنشاء جميع البيانات الافتراضية بنجاح!');
      } else {
        setMessage('❌ فشل في إنشاء البيانات: ' + result.error);
      }
    } catch (error) {
      setMessage('❌ خطأ: ' + error.message);
    } finally {
      setLoading(false);
    }
  };
  return (
    <Box 
      sx={{ 
        minHeight: '100vh', 
        display: 'flex', 
        flexDirection: 'column',
        alignItems: 'center', 
        justifyContent: 'center',
        bgcolor: '#f5f5f5',
        p: 3
      }}
    >
      <Typography variant="h3" component="h1" gutterBottom color="primary">
        🎓 SKILLS WORLD ACADEMY
      </Typography>
      
      <Typography variant="h5" component="h2" gutterBottom color="text.secondary">
        اختبار المشروع - الصفحة تعمل بنجاح!
      </Typography>
      
      <Box sx={{ mt: 3, display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'center' }}>
        <Button
          variant="contained"
          color="primary"
          size="large"
          onClick={() => window.location.href = '/login'}
        >
          الذهاب لصفحة تسجيل الدخول
        </Button>

        <Button
          variant="contained"
          color="success"
          size="large"
          onClick={handleCreateTestData}
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          {loading ? 'جاري الإنشاء...' : 'إنشاء بيانات افتراضية'}
        </Button>

        <Button
          variant="outlined"
          color="info"
          size="large"
          onClick={() => window.location.href = '/system-test'}
        >
          اختبار شامل للنظام
        </Button>
      </Box>

      {message && (
        <Box sx={{ mt: 3, maxWidth: 600 }}>
          <Alert severity={message.includes('✅') ? 'success' : 'error'}>
            {message}
          </Alert>
        </Box>
      )}
      
      <Typography variant="body1" sx={{ mt: 3, textAlign: 'center' }}>
        إذا كنت ترى هذه الصفحة، فإن React و Material-UI يعملان بشكل صحيح
      </Typography>
    </Box>
  );
};

export default TestPage;
