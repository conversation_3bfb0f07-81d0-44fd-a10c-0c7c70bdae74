# إعداد قاعدة البيانات - SKILLS WORLD ACADEMY

## نظرة عامة

هذا الدليل يوضح كيفية إعداد قاعدة البيانات لأكاديمية عالم المهارات باستخدام Supabase.

## المتطلبات

- حساب Supabase (مجاني)
- Node.js 16+ 
- npm أو yarn

## خطوات الإعداد

### 1. إنشاء مشروع Supabase

1. اذهب إلى [supabase.com](https://supabase.com)
2. أنشئ حساب جديد أو سجل دخول
3. أنشئ مشروع جديد
4. احفظ URL المشروع و API Key

### 2. إعداد متغيرات البيئة

أنشئ ملف `.env.local` في مجلد `frontend` وأضف:

```env
REACT_APP_SUPABASE_URL=your_supabase_project_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 3. تشغيل سكريبت الإعداد

```bash
# في مجلد frontend
npm install @supabase/supabase-js

# تشغيل سكريبت الإعداد
node setup-supabase.js
```

### 4. إعداد قاعدة البيانات يدوياً (اختياري)

إذا فشل السكريبت التلقائي، يمكنك تشغيل SQL يدوياً:

1. اذهب إلى Supabase Dashboard
2. افتح SQL Editor
3. انسخ محتوى ملف `setup.sql`
4. شغل الاستعلام

## هيكل قاعدة البيانات

### الجداول الرئيسية

#### 1. students (الطلاب)
- `id`: معرف فريد
- `name`: اسم الطالب
- `email`: البريد الإلكتروني
- `phone`: رقم الهاتف
- `student_code`: كود الطالب (6 أرقام)
- `is_active`: حالة النشاط
- `created_at`: تاريخ الإنشاء

#### 2. course_categories (فئات الكورسات)
- `id`: معرف فريد
- `name`: اسم الفئة
- `description`: وصف الفئة
- `color`: لون الفئة
- `created_at`: تاريخ الإنشاء

#### 3. courses (الكورسات)
- `id`: معرف فريد
- `title`: عنوان الكورس
- `description`: وصف الكورس
- `category_id`: معرف الفئة
- `level`: مستوى الكورس (beginner/intermediate/advanced)
- `duration`: مدة الكورس
- `price`: سعر الكورس
- `status`: حالة الكورس (draft/active/archived)
- `created_at`: تاريخ الإنشاء

#### 4. enrollments (التسجيلات)
- `id`: معرف فريد
- `student_id`: معرف الطالب
- `course_id`: معرف الكورس
- `enrollment_date`: تاريخ التسجيل
- `progress`: نسبة التقدم (0-100)
- `completed_at`: تاريخ الإكمال
- `created_at`: تاريخ الإنشاء

#### 5. course_videos (فيديوهات الكورسات)
- `id`: معرف فريد
- `course_id`: معرف الكورس
- `title`: عنوان الفيديو
- `description`: وصف الفيديو
- `video_url`: رابط الفيديو
- `file_path`: مسار الملف
- `duration`: مدة الفيديو (بالثواني)
- `order_index`: ترتيب الفيديو
- `is_free`: هل الفيديو مجاني
- `created_at`: تاريخ الإنشاء

#### 6. course_materials (مواد الكورسات)
- `id`: معرف فريد
- `course_id`: معرف الكورس
- `title`: عنوان المادة
- `description`: وصف المادة
- `file_path`: مسار الملف
- `file_type`: نوع الملف
- `file_size`: حجم الملف
- `order_index`: ترتيب المادة
- `is_downloadable`: هل يمكن تحميل المادة
- `created_at`: تاريخ الإنشاء

#### 7. certificates (الشهادات)
- `id`: معرف فريد
- `student_id`: معرف الطالب
- `course_id`: معرف الكورس
- `certificate_number`: رقم الشهادة
- `completion_date`: تاريخ الإكمال
- `issue_date`: تاريخ الإصدار
- `grade`: الدرجة
- `status`: حالة الشهادة (pending/issued/revoked)
- `notes`: ملاحظات
- `certificate_url`: رابط الشهادة
- `created_at`: تاريخ الإنشاء

#### 8. activities (النشاطات)
- `id`: معرف فريد
- `user_id`: معرف المستخدم
- `user_name`: اسم المستخدم
- `action`: نوع العملية
- `description`: وصف العملية
- `entity_type`: نوع الكيان
- `entity_id`: معرف الكيان
- `metadata`: بيانات إضافية (JSON)
- `ip_address`: عنوان IP
- `user_agent`: معلومات المتصفح
- `created_at`: تاريخ الإنشاء

#### 9. settings (الإعدادات)
- `id`: معرف فريد
- `key`: مفتاح الإعداد
- `value`: قيمة الإعداد
- `description`: وصف الإعداد
- `category`: فئة الإعداد
- `is_public`: هل الإعداد عام
- `created_at`: تاريخ الإنشاء

### Storage Buckets

#### 1. course-content
- لحفظ فيديوهات ومواد الكورسات
- خاص (يتطلب تصريح)

#### 2. certificates
- لحفظ ملفات الشهادات
- خاص (يتطلب تصريح)

#### 3. avatars
- لحفظ صور المستخدمين
- عام (يمكن الوصول إليه)

## الدوال المساعدة

### 1. generate_student_code()
تولد كود طالب فريد من 6 أرقام

### 2. generate_certificate_number()
تولد رقم شهادة فريد بصيغة CERT-YYYY-XXX

### 3. log_activity()
تسجل نشاط المستخدم في جدول النشاطات

### 4. update_updated_at_column()
تحدث عمود updated_at تلقائياً عند التعديل

## الأمان

- تم تفعيل Row Level Security (RLS) على جميع الجداول
- سياسات أمان للمدراء والطلاب
- حماية Storage buckets
- تشفير البيانات الحساسة

## النسخ الاحتياطي

- نسخ احتياطي تلقائي يومي
- إمكانية إنشاء نسخ احتياطية يدوية
- استعادة البيانات من النسخ الاحتياطية

## الصيانة

### تنظيف قاعدة البيانات
```sql
-- حذف البيانات القديمة (أكثر من سنة)
DELETE FROM activities WHERE created_at < NOW() - INTERVAL '1 year';

-- تحسين الجداول
VACUUM ANALYZE;
```

### مراقبة الأداء
```sql
-- فحص حجم الجداول
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

## استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في الاتصال**
   - تأكد من صحة URL و API Key
   - تأكد من تفعيل المشروع في Supabase

2. **خطأ في الصلاحيات**
   - تأكد من إعداد RLS policies بشكل صحيح
   - تأكد من صحة JWT token

3. **خطأ في رفع الملفات**
   - تأكد من إنشاء Storage buckets
   - تأكد من صحة سياسات Storage

### سجلات الأخطاء

يمكن مراجعة سجلات الأخطاء في:
- Supabase Dashboard > Logs
- Browser Console
- Server logs

## الدعم

للحصول على المساعدة:
- راجع [وثائق Supabase](https://supabase.com/docs)
- تواصل مع فريق التطوير
- افتح issue في المستودع

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
