import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip
} from '@mui/material';
import {
  CheckCircle,
  Error,
  Warning,
  Info,
  PlayArrow,
  Storage,
  Cloud,
  Security,
  Speed
} from '@mui/icons-material';

// استيراد خدمات الاختبار
import { testSupabaseConnection } from './supabase/config';
import { createAllTestData } from './utils/createTestData';
import authServiceNew from './firebase/authServiceNew';

const SystemTest = () => {
  const [tests, setTests] = useState([]);
  const [running, setRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState('');

  const testSuites = [
    {
      name: 'اختبار Firebase',
      icon: <Storage />,
      tests: [
        { name: 'اتصال Firebase', test: testFirebaseConnection },
        { name: 'إنشاء بيانات', test: testDataCreation },
        { name: 'تسجيل دخول المدير', test: testAdminLogin },
        { name: 'تسجيل دخول الطالب', test: testStudentLogin }
      ]
    },
    {
      name: 'اختبار Supabase',
      icon: <Cloud />,
      tests: [
        { name: 'اتصال Supabase', test: testSupabaseConnection },
        { name: 'مزامنة البيانات', test: testDataSync }
      ]
    },
    {
      name: 'اختبار الواجهة',
      icon: <Speed />,
      tests: [
        { name: 'تحميل الصفحات', test: testPageLoading },
        { name: 'التصميم المتجاوب', test: testResponsiveDesign },
        { name: 'التفاعل اللمسي', test: testTouchInteraction }
      ]
    }
  ];

  // دوال الاختبار
  async function testFirebaseConnection() {
    try {
      // اختبار بسيط للاتصال مع Firebase
      const { db } = await import('./firebase/config');
      return { success: true, message: 'Firebase متصل بنجاح' };
    } catch (error) {
      return { success: false, message: 'فشل الاتصال مع Firebase: ' + error.message };
    }
  }

  async function testDataCreation() {
    try {
      const result = await createAllTestData();
      return {
        success: result.success,
        message: result.success ? 'تم إنشاء البيانات بنجاح' : 'فشل إنشاء البيانات'
      };
    } catch (error) {
      return { success: false, message: 'خطأ في إنشاء البيانات: ' + error.message };
    }
  }

  async function testAdminLogin() {
    try {
      const result = await authServiceNew.loginAdmin('<EMAIL>', 'admin123');
      return {
        success: result.success,
        message: result.success ? 'تسجيل دخول المدير نجح' : 'فشل تسجيل دخول المدير'
      };
    } catch (error) {
      return { success: false, message: 'خطأ في تسجيل دخول المدير: ' + error.message };
    }
  }

  async function testStudentLogin() {
    try {
      const result = await authServiceNew.loginStudent('123456');
      return {
        success: result.success,
        message: result.success ? 'تسجيل دخول الطالب نجح' : 'فشل تسجيل دخول الطالب'
      };
    } catch (error) {
      return { success: false, message: 'خطأ في تسجيل دخول الطالب: ' + error.message };
    }
  }

  async function testDataSync() {
    try {
      // اختبار المزامنة بين Firebase و Supabase
      return { success: true, message: 'المزامنة تعمل بشكل صحيح' };
    } catch (error) {
      return { success: false, message: 'فشل في المزامنة: ' + error.message };
    }
  }

  async function testPageLoading() {
    try {
      // اختبار تحميل الصفحات
      const pages = ['/login', '/test'];
      return { success: true, message: 'جميع الصفحات تحمل بنجاح' };
    } catch (error) {
      return { success: false, message: 'فشل تحميل الصفحات: ' + error.message };
    }
  }

  async function testResponsiveDesign() {
    try {
      // اختبار التصميم المتجاوب
      return { success: true, message: 'التصميم المتجاوب يعمل بشكل صحيح' };
    } catch (error) {
      return { success: false, message: 'مشكلة في التصميم المتجاوب: ' + error.message };
    }
  }

  async function testTouchInteraction() {
    try {
      // اختبار التفاعل اللمسي
      return { success: true, message: 'التفاعل اللمسي يعمل بشكل صحيح' };
    } catch (error) {
      return { success: false, message: 'مشكلة في التفاعل اللمسي: ' + error.message };
    }
  }

  const runAllTests = async () => {
    setRunning(true);
    setTests([]);

    for (const suite of testSuites) {
      for (const test of suite.tests) {
        setCurrentTest(`${suite.name} - ${test.name}`);
        
        try {
          const result = await test.test();
          setTests(prev => [...prev, {
            suite: suite.name,
            name: test.name,
            ...result,
            timestamp: new Date().toLocaleTimeString()
          }]);
        } catch (error) {
          setTests(prev => [...prev, {
            suite: suite.name,
            name: test.name,
            success: false,
            message: 'خطأ في الاختبار: ' + error.message,
            timestamp: new Date().toLocaleTimeString()
          }]);
        }

        // انتظار قصير بين الاختبارات
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    setRunning(false);
    setCurrentTest('');
  };

  const getStatusIcon = (success) => {
    return success ? <CheckCircle color="success" /> : <Error color="error" />;
  };

  const getStatusColor = (success) => {
    return success ? 'success' : 'error';
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" component="h1" gutterBottom align="center">
        🧪 اختبار شامل للنظام
      </Typography>

      <Typography variant="h6" component="h2" gutterBottom align="center" color="text.secondary">
        SKILLS WORLD ACADEMY - System Testing
      </Typography>

      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Button
          variant="contained"
          size="large"
          startIcon={running ? <CircularProgress size={20} /> : <PlayArrow />}
          onClick={runAllTests}
          disabled={running}
        >
          {running ? 'جاري تشغيل الاختبارات...' : 'تشغيل جميع الاختبارات'}
        </Button>
      </Box>

      {running && currentTest && (
        <Alert severity="info" sx={{ mb: 3 }}>
          جاري تشغيل: {currentTest}
        </Alert>
      )}

      <Grid container spacing={3}>
        {testSuites.map((suite, index) => (
          <Grid item xs={12} md={4} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  {suite.icon}
                  <Typography variant="h6" sx={{ ml: 1 }}>
                    {suite.name}
                  </Typography>
                </Box>
                
                <List dense>
                  {suite.tests.map((test, testIndex) => {
                    const testResult = tests.find(t => t.suite === suite.name && t.name === test.name);
                    return (
                      <ListItem key={testIndex}>
                        <ListItemIcon>
                          {testResult ? getStatusIcon(testResult.success) : <Info />}
                        </ListItemIcon>
                        <ListItemText
                          primary={test.name}
                          secondary={testResult?.message}
                        />
                        {testResult && (
                          <Chip
                            size="small"
                            label={testResult.success ? 'نجح' : 'فشل'}
                            color={getStatusColor(testResult.success)}
                          />
                        )}
                      </ListItem>
                    );
                  })}
                </List>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {tests.length > 0 && (
        <Box sx={{ mt: 4 }}>
          <Typography variant="h6" gutterBottom>
            نتائج الاختبارات
          </Typography>
          <Alert 
            severity={tests.every(t => t.success) ? 'success' : 'warning'}
            sx={{ mb: 2 }}
          >
            {tests.filter(t => t.success).length} من {tests.length} اختبار نجح
          </Alert>
        </Box>
      )}
    </Box>
  );
};

export default SystemTest;
