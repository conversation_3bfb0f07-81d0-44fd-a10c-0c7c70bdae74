import React, { useState, useCallback } from 'react';
import {
  Box,
  Typography,
  Button,
  LinearProgress,
  Alert,
  Card,
  CardContent,
  IconButton,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Divider
} from '@mui/material';
import {
  CloudUpload,
  VideoFile,
  PictureAsPdf,
  InsertDriveFile,
  Delete,
  Preview,
  PlayArrow,
  Download,
  Edit,
  DragIndicator,
  Lock,
  LockOpen,
  Visibility,
  VisibilityOff
} from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';
import toast from 'react-hot-toast';

const FileUploader = ({ 
  courseId, 
  onFilesUploaded, 
  acceptedTypes = {
    'video/*': ['.mp4', '.avi', '.mov', '.wmv'],
    'application/pdf': ['.pdf'],
    'image/*': ['.jpg', '.jpeg', '.png', '.gif']
  },
  maxSize = 100 * 1024 * 1024, // 100MB
  multiple = true 
}) => {
  const [files, setFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});
  const [previewDialog, setPreviewDialog] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [editDialog, setEditDialog] = useState(false);
  const [editingFile, setEditingFile] = useState(null);

  // معلومات الملف للتحرير
  const [fileInfo, setFileInfo] = useState({
    title: '',
    description: '',
    order: 0,
    isFree: false,
    isVisible: true,
    category: 'lesson'
  });

  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {
    // معالجة الملفات المرفوضة
    if (rejectedFiles.length > 0) {
      rejectedFiles.forEach(({ file, errors }) => {
        errors.forEach(error => {
          if (error.code === 'file-too-large') {
            toast.error(`الملف ${file.name} كبير جداً. الحد الأقصى ${maxSize / 1024 / 1024}MB`);
          } else if (error.code === 'file-invalid-type') {
            toast.error(`نوع الملف ${file.name} غير مدعوم`);
          }
        });
      });
    }

    // إضافة الملفات المقبولة
    const newFiles = acceptedFiles.map(file => ({
      id: Date.now() + Math.random(),
      file,
      name: file.name,
      size: file.size,
      type: file.type,
      preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : null,
      status: 'pending',
      progress: 0,
      title: file.name.replace(/\.[^/.]+$/, ''), // إزالة الامتداد
      description: '',
      order: files.length,
      isFree: false,
      isVisible: true,
      category: file.type.startsWith('video/') ? 'lesson' : 'material'
    }));

    setFiles(prev => [...prev, ...newFiles]);
  }, [files.length, maxSize]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedTypes,
    maxSize,
    multiple
  });

  // رفع الملفات
  const handleUpload = async () => {
    if (files.length === 0) {
      toast.error('يرجى اختيار ملفات للرفع');
      return;
    }

    setUploading(true);
    const uploadedFiles = [];

    try {
      for (const fileItem of files) {
        if (fileItem.status === 'uploaded') continue;

        // تحديث حالة الملف
        setFiles(prev => prev.map(f => 
          f.id === fileItem.id ? { ...f, status: 'uploading' } : f
        ));

        // محاكاة رفع الملف مع تقدم
        for (let progress = 0; progress <= 100; progress += 10) {
          await new Promise(resolve => setTimeout(resolve, 100));
          setUploadProgress(prev => ({ ...prev, [fileItem.id]: progress }));
          setFiles(prev => prev.map(f => 
            f.id === fileItem.id ? { ...f, progress } : f
          ));
        }

        // محاكاة حفظ في قاعدة البيانات
        const uploadedFile = {
          ...fileItem,
          status: 'uploaded',
          url: `https://example.com/files/${fileItem.file.name}`,
          uploadedAt: new Date().toISOString()
        };

        uploadedFiles.push(uploadedFile);

        setFiles(prev => prev.map(f => 
          f.id === fileItem.id ? uploadedFile : f
        ));

        toast.success(`تم رفع ${fileItem.name} بنجاح`);
      }

      if (onFilesUploaded) {
        onFilesUploaded(uploadedFiles);
      }

    } catch (error) {
      console.error('خطأ في رفع الملفات:', error);
      toast.error('حدث خطأ أثناء رفع الملفات');
    } finally {
      setUploading(false);
      setUploadProgress({});
    }
  };

  // حذف ملف
  const handleDeleteFile = (fileId) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
    toast.success('تم حذف الملف');
  };

  // معاينة ملف
  const handlePreviewFile = (file) => {
    setSelectedFile(file);
    setPreviewDialog(true);
  };

  // تحرير معلومات الملف
  const handleEditFile = (file) => {
    setEditingFile(file);
    setFileInfo({
      title: file.title,
      description: file.description,
      order: file.order,
      isFree: file.isFree,
      isVisible: file.isVisible,
      category: file.category
    });
    setEditDialog(true);
  };

  // حفظ تعديل الملف
  const handleSaveFileInfo = () => {
    setFiles(prev => prev.map(f => 
      f.id === editingFile.id ? { ...f, ...fileInfo } : f
    ));
    setEditDialog(false);
    toast.success('تم تحديث معلومات الملف');
  };

  // الحصول على أيقونة الملف
  const getFileIcon = (type) => {
    if (type.startsWith('video/')) return <VideoFile color="error" />;
    if (type === 'application/pdf') return <PictureAsPdf color="error" />;
    if (type.startsWith('image/')) return <Preview color="primary" />;
    return <InsertDriveFile color="action" />;
  };

  // تنسيق حجم الملف
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Box>
      {/* منطقة السحب والإفلات */}
      <Card 
        sx={{ 
          mb: 3,
          border: isDragActive ? '2px dashed #4169E1' : '2px dashed #ccc',
          backgroundColor: isDragActive ? 'rgba(65, 105, 225, 0.1)' : 'transparent',
          transition: 'all 0.3s ease'
        }}
      >
        <CardContent>
          <Box
            {...getRootProps()}
            sx={{
              textAlign: 'center',
              py: 4,
              cursor: 'pointer',
              '&:hover': {
                backgroundColor: 'rgba(65, 105, 225, 0.05)'
              }
            }}
          >
            <input {...getInputProps()} />
            <CloudUpload sx={{ fontSize: 48, color: '#4169E1', mb: 2 }} />
            <Typography variant="h6" sx={{ mb: 1 }}>
              {isDragActive ? 'أفلت الملفات هنا...' : 'اسحب الملفات هنا أو انقر للاختيار'}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              الأنواع المدعومة: فيديو، PDF، صور
            </Typography>
            <Typography variant="caption" color="text.secondary">
              الحد الأقصى: {maxSize / 1024 / 1024}MB لكل ملف
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {/* قائمة الملفات */}
      {files.length > 0 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                الملفات المحددة ({files.length})
              </Typography>
              <Button
                variant="contained"
                startIcon={<CloudUpload />}
                onClick={handleUpload}
                disabled={uploading || files.every(f => f.status === 'uploaded')}
                sx={{
                  background: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)'
                  }
                }}
              >
                {uploading ? 'جاري الرفع...' : 'رفع الملفات'}
              </Button>
            </Box>

            <List>
              {files.map((file, index) => (
                <React.Fragment key={file.id}>
                  <ListItem>
                    <ListItemIcon>
                      <DragIndicator sx={{ cursor: 'grab', mr: 1 }} />
                      {getFileIcon(file.type)}
                    </ListItemIcon>
                    
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle2">
                            {file.title || file.name}
                          </Typography>
                          <Chip 
                            size="small" 
                            label={file.isFree ? 'مجاني' : 'مدفوع'}
                            color={file.isFree ? 'success' : 'warning'}
                            icon={file.isFree ? <LockOpen /> : <Lock />}
                          />
                          {!file.isVisible && (
                            <Chip 
                              size="small" 
                              label="مخفي"
                              color="default"
                              icon={<VisibilityOff />}
                            />
                          )}
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="caption" display="block">
                            {formatFileSize(file.size)} • {file.category === 'lesson' ? 'درس' : 'مادة تعليمية'}
                          </Typography>
                          {file.description && (
                            <Typography variant="caption" color="text.secondary">
                              {file.description}
                            </Typography>
                          )}
                          {file.status === 'uploading' && (
                            <LinearProgress 
                              variant="determinate" 
                              value={file.progress} 
                              sx={{ mt: 1 }}
                            />
                          )}
                          {file.status === 'uploaded' && (
                            <Chip 
                              size="small" 
                              label="تم الرفع" 
                              color="success" 
                              sx={{ mt: 1 }}
                            />
                          )}
                        </Box>
                      }
                    />

                    <ListItemSecondaryAction>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <IconButton 
                          size="small" 
                          onClick={() => handleEditFile(file)}
                          title="تحرير"
                        >
                          <Edit />
                        </IconButton>
                        <IconButton 
                          size="small" 
                          onClick={() => handlePreviewFile(file)}
                          title="معاينة"
                        >
                          {file.type.startsWith('video/') ? <PlayArrow /> : <Preview />}
                        </IconButton>
                        <IconButton 
                          size="small" 
                          color="error"
                          onClick={() => handleDeleteFile(file.id)}
                          title="حذف"
                        >
                          <Delete />
                        </IconButton>
                      </Box>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < files.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      )}

      {/* نافذة المعاينة */}
      <Dialog 
        open={previewDialog} 
        onClose={() => setPreviewDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>معاينة الملف</DialogTitle>
        <DialogContent>
          {selectedFile && (
            <Box sx={{ textAlign: 'center' }}>
              {selectedFile.type.startsWith('video/') && (
                <video 
                  controls 
                  style={{ width: '100%', maxHeight: '400px' }}
                  src={selectedFile.preview || URL.createObjectURL(selectedFile.file)}
                />
              )}
              {selectedFile.type.startsWith('image/') && (
                <img 
                  src={selectedFile.preview || URL.createObjectURL(selectedFile.file)}
                  alt={selectedFile.name}
                  style={{ width: '100%', maxHeight: '400px', objectFit: 'contain' }}
                />
              )}
              {selectedFile.type === 'application/pdf' && (
                <Box sx={{ p: 2 }}>
                  <PictureAsPdf sx={{ fontSize: 64, color: '#f44336' }} />
                  <Typography variant="h6" sx={{ mt: 2 }}>
                    {selectedFile.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {formatFileSize(selectedFile.size)}
                  </Typography>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialog(false)}>
            إغلاق
          </Button>
        </DialogActions>
      </Dialog>

      {/* نافذة تحرير معلومات الملف */}
      <Dialog 
        open={editDialog} 
        onClose={() => setEditDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>تحرير معلومات الملف</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="عنوان الملف"
              value={fileInfo.title}
              onChange={(e) => setFileInfo({ ...fileInfo, title: e.target.value })}
              sx={{ mb: 2 }}
            />
            
            <TextField
              fullWidth
              multiline
              rows={3}
              label="وصف الملف"
              value={fileInfo.description}
              onChange={(e) => setFileInfo({ ...fileInfo, description: e.target.value })}
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              type="number"
              label="ترتيب العرض"
              value={fileInfo.order}
              onChange={(e) => setFileInfo({ ...fileInfo, order: parseInt(e.target.value) })}
              sx={{ mb: 2 }}
            />

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>نوع المحتوى</InputLabel>
              <Select
                value={fileInfo.category}
                onChange={(e) => setFileInfo({ ...fileInfo, category: e.target.value })}
              >
                <MenuItem value="lesson">درس</MenuItem>
                <MenuItem value="material">مادة تعليمية</MenuItem>
                <MenuItem value="assignment">واجب</MenuItem>
                <MenuItem value="quiz">اختبار</MenuItem>
              </Select>
            </FormControl>

            <FormControlLabel
              control={
                <Switch
                  checked={fileInfo.isFree}
                  onChange={(e) => setFileInfo({ ...fileInfo, isFree: e.target.checked })}
                />
              }
              label="محتوى مجاني"
              sx={{ mb: 1 }}
            />

            <FormControlLabel
              control={
                <Switch
                  checked={fileInfo.isVisible}
                  onChange={(e) => setFileInfo({ ...fileInfo, isVisible: e.target.checked })}
                />
              }
              label="مرئي للطلاب"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog(false)}>
            إلغاء
          </Button>
          <Button 
            variant="contained" 
            onClick={handleSaveFileInfo}
            sx={{
              background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)'
              }
            }}
          >
            حفظ
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FileUploader;
