import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Alert,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  PhoneAndroid,
  Tablet,
  Computer,
  Tv,
  CheckCircle,
  Error,
  Warning,
  PlayArrow,
  Refresh,
  Visibility,
  BugReport,
  Speed
} from '@mui/icons-material';

/**
 * مكون اختبار التجاوب الحقيقي للوحة التحكم الإدارية
 */
const TrueResponsivenessTest = () => {
  const [screenInfo, setScreenInfo] = useState({});
  const [deviceCategory, setDeviceCategory] = useState('');
  const [testResults, setTestResults] = useState({});
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState(0);

  // نقاط التوقف الحقيقية
  const breakpoints = {
    xs: { min: 0, max: 599, name: 'محمول صغير', icon: <PhoneAndroid /> },
    sm: { min: 600, max: 899, name: 'لوحي صغير', icon: <Tablet /> },
    md: { min: 900, max: 1199, name: 'لوحي كبير', icon: <Tablet /> },
    lg: { min: 1200, max: 1535, name: 'كمبيوتر محمول', icon: <Computer /> },
    xl: { min: 1536, max: Infinity, name: 'شاشة كبيرة', icon: <Tv /> }
  };

  useEffect(() => {
    updateScreenInfo();
    window.addEventListener('resize', updateScreenInfo);
    return () => window.removeEventListener('resize', updateScreenInfo);
  }, []);

  const updateScreenInfo = () => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    // تحديد فئة الجهاز
    let category = '';
    for (const [key, bp] of Object.entries(breakpoints)) {
      if (width >= bp.min && width <= bp.max) {
        category = key;
        break;
      }
    }

    setScreenInfo({
      width,
      height,
      ratio: (width / height).toFixed(2),
      orientation: width > height ? 'landscape' : 'portrait',
      pixelRatio: window.devicePixelRatio || 1,
      userAgent: navigator.userAgent
    });
    
    setDeviceCategory(category);
  };

  const runComprehensiveTests = async () => {
    setIsRunning(true);
    setProgress(0);
    const results = {};

    const tests = [
      { name: 'appBarWidth', test: testAppBarWidth },
      { name: 'drawerVisibility', test: testDrawerVisibility },
      { name: 'mainContentLayout', test: testMainContentLayout },
      { name: 'buttonSizes', test: testButtonSizes },
      { name: 'textReadability', test: testTextReadability },
      { name: 'touchTargets', test: testTouchTargets },
      { name: 'scrollBehavior', test: testScrollBehavior },
      { name: 'zIndexLayers', test: testZIndexLayers },
      { name: 'rtlSupport', test: testRTLSupport },
      { name: 'performance', test: testPerformance }
    ];

    for (let i = 0; i < tests.length; i++) {
      const test = tests[i];
      try {
        results[test.name] = await test.test();
      } catch (error) {
        results[test.name] = { success: false, error: error.message };
      }
      setProgress(((i + 1) / tests.length) * 100);
      await new Promise(resolve => setTimeout(resolve, 200)); // تأخير صغير للعرض
    }

    setTestResults(results);
    setIsRunning(false);
  };

  // اختبار عرض الشريط العلوي
  const testAppBarWidth = () => {
    const appBar = document.querySelector('.admin-dashboard .MuiAppBar-root');
    if (!appBar) return { success: false, message: 'الشريط العلوي غير موجود' };
    
    const isFullWidth = appBar.offsetWidth >= window.innerWidth * 0.98;
    const hasCorrectHeight = appBar.offsetHeight >= 56;
    
    return {
      success: isFullWidth && hasCorrectHeight,
      message: isFullWidth && hasCorrectHeight ? 
        'الشريط العلوي يعمل بشكل صحيح' : 
        `العرض: ${appBar.offsetWidth}px، الارتفاع: ${appBar.offsetHeight}px`,
      details: {
        width: appBar.offsetWidth,
        expectedWidth: window.innerWidth,
        height: appBar.offsetHeight,
        isFullWidth,
        hasCorrectHeight
      }
    };
  };

  // اختبار رؤية الدرج الجانبي
  const testDrawerVisibility = () => {
    const drawer = document.querySelector('.admin-dashboard nav');
    const temporaryDrawer = document.querySelector('.admin-dashboard .MuiDrawer-temporary');
    
    if (deviceCategory === 'xs') {
      // يجب أن يكون الدرج مؤقت على الأجهزة المحمولة
      return {
        success: !drawer || window.getComputedStyle(drawer).display === 'none',
        message: 'الدرج الجانبي مخفي بشكل صحيح على الأجهزة المحمولة'
      };
    } else {
      // يجب أن يكون الدرج ثابت على الأجهزة الأخرى
      const isVisible = drawer && window.getComputedStyle(drawer).display !== 'none';
      return {
        success: isVisible,
        message: isVisible ? 'الدرج الجانبي ظاهر بشكل صحيح' : 'الدرج الجانبي مخفي',
        details: {
          drawerExists: !!drawer,
          drawerWidth: drawer ? drawer.offsetWidth : 0
        }
      };
    }
  };

  // اختبار تخطيط المحتوى الرئيسي
  const testMainContentLayout = () => {
    const main = document.querySelector('.admin-dashboard main');
    if (!main) return { success: false, message: 'المحتوى الرئيسي غير موجود' };
    
    const hasCorrectMargin = main.style.marginTop || window.getComputedStyle(main).marginTop;
    const hasCorrectWidth = main.offsetWidth > 0;
    const noOverflow = main.scrollWidth <= main.offsetWidth + 10; // هامش صغير للخطأ
    
    return {
      success: hasCorrectMargin && hasCorrectWidth && noOverflow,
      message: 'تخطيط المحتوى الرئيسي صحيح',
      details: {
        width: main.offsetWidth,
        scrollWidth: main.scrollWidth,
        marginTop: hasCorrectMargin,
        noOverflow
      }
    };
  };

  // اختبار أحجام الأزرار
  const testButtonSizes = () => {
    const buttons = document.querySelectorAll('.admin-dashboard .MuiButton-root, .admin-dashboard .MuiIconButton-root');
    const minSize = deviceCategory === 'xs' ? 48 : deviceCategory === 'sm' ? 52 : 44;
    
    const correctSizes = Array.from(buttons).filter(btn => 
      btn.offsetHeight >= minSize && btn.offsetWidth >= minSize
    );
    
    const success = correctSizes.length === buttons.length;
    
    return {
      success,
      message: success ? 
        'جميع الأزرار بالحجم المناسب' : 
        `${correctSizes.length}/${buttons.length} أزرار بالحجم الصحيح`,
      details: {
        totalButtons: buttons.length,
        correctSizeButtons: correctSizes.length,
        minRequiredSize: minSize
      }
    };
  };

  // اختبار وضوح النصوص
  const testTextReadability = () => {
    const texts = document.querySelectorAll('.admin-dashboard .MuiTypography-root');
    const minFontSize = deviceCategory === 'xs' ? 14 : 16;
    
    const readableTexts = Array.from(texts).filter(text => {
      const style = window.getComputedStyle(text);
      return parseFloat(style.fontSize) >= minFontSize;
    });
    
    const success = readableTexts.length >= texts.length * 0.9; // 90% من النصوص يجب أن تكون مقروءة
    
    return {
      success,
      message: success ? 'النصوص واضحة ومقروءة' : 'بعض النصوص صغيرة جداً',
      details: {
        totalTexts: texts.length,
        readableTexts: readableTexts.length,
        minFontSize
      }
    };
  };

  // اختبار أهداف اللمس
  const testTouchTargets = () => {
    const touchElements = document.querySelectorAll('.admin-dashboard .MuiListItemButton-root, .admin-dashboard .MuiButton-root');
    const minTouchSize = 44;
    
    const correctTouchTargets = Array.from(touchElements).filter(el => 
      el.offsetHeight >= minTouchSize && el.offsetWidth >= minTouchSize
    );
    
    const success = correctTouchTargets.length === touchElements.length;
    
    return {
      success,
      message: success ? 'جميع أهداف اللمس بالحجم المناسب' : 'بعض أهداف اللمس صغيرة',
      details: {
        totalElements: touchElements.length,
        correctElements: correctTouchTargets.length,
        minTouchSize
      }
    };
  };

  // اختبار سلوك التمرير
  const testScrollBehavior = () => {
    const scrollableElements = document.querySelectorAll('.admin-dashboard nav, .admin-dashboard main');
    
    const hasCorrectScrolling = Array.from(scrollableElements).every(el => {
      const style = window.getComputedStyle(el);
      return style.overflowY === 'auto' || style.overflowY === 'scroll' || style.overflow === 'auto';
    });
    
    return {
      success: hasCorrectScrolling,
      message: hasCorrectScrolling ? 'التمرير يعمل بشكل صحيح' : 'مشاكل في التمرير'
    };
  };

  // اختبار طبقات z-index
  const testZIndexLayers = () => {
    const appBar = document.querySelector('.admin-dashboard .MuiAppBar-root');
    const drawer = document.querySelector('.admin-dashboard nav');
    const main = document.querySelector('.admin-dashboard main');
    
    if (!appBar) return { success: false, message: 'عناصر مفقودة' };
    
    const appBarZ = parseInt(window.getComputedStyle(appBar).zIndex) || 0;
    const drawerZ = drawer ? parseInt(window.getComputedStyle(drawer).zIndex) || 0 : 0;
    const mainZ = main ? parseInt(window.getComputedStyle(main).zIndex) || 0 : 0;
    
    const correctLayering = appBarZ > drawerZ && drawerZ >= mainZ;
    
    return {
      success: correctLayering,
      message: correctLayering ? 'طبقات العناصر صحيحة' : 'مشاكل في ترتيب الطبقات',
      details: {
        appBarZ,
        drawerZ,
        mainZ
      }
    };
  };

  // اختبار دعم RTL
  const testRTLSupport = () => {
    const html = document.documentElement;
    const isRTL = html.dir === 'rtl';
    const drawer = document.querySelector('.admin-dashboard nav');
    
    if (!drawer) return { success: false, message: 'الدرج غير موجود' };
    
    const drawerStyle = window.getComputedStyle(drawer);
    const correctPosition = isRTL ? 
      drawerStyle.right !== 'auto' : 
      drawerStyle.left !== 'auto';
    
    return {
      success: correctPosition,
      message: correctPosition ? 'دعم RTL يعمل بشكل صحيح' : 'مشاكل في دعم RTL',
      details: {
        isRTL,
        drawerRight: drawerStyle.right,
        drawerLeft: drawerStyle.left
      }
    };
  };

  // اختبار الأداء
  const testPerformance = () => {
    const startTime = performance.now();
    
    // محاكاة عملية معقدة
    const elements = document.querySelectorAll('.admin-dashboard *');
    let computedStyles = 0;
    
    for (let i = 0; i < Math.min(elements.length, 100); i++) {
      window.getComputedStyle(elements[i]);
      computedStyles++;
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    const isPerformant = duration < 100; // أقل من 100ms
    
    return {
      success: isPerformant,
      message: isPerformant ? 'الأداء جيد' : 'الأداء بطيء',
      details: {
        duration: duration.toFixed(2),
        elementsChecked: computedStyles
      }
    };
  };

  const getResultIcon = (result) => {
    if (!result) return <Warning sx={{ color: '#FF9800' }} />;
    if (result.success) return <CheckCircle sx={{ color: '#4CAF50' }} />;
    return <Error sx={{ color: '#F44336' }} />;
  };

  const getResultColor = (result) => {
    if (!result) return '#FF9800';
    return result.success ? '#4CAF50' : '#F44336';
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1400, mx: 'auto' }}>
      <Typography variant="h4" sx={{ mb: 3, textAlign: 'center', color: '#0000FF' }}>
        اختبار التجاوب الحقيقي - لوحة التحكم الإدارية
      </Typography>

      {/* معلومات الجهاز الحالي */}
      <Paper sx={{ p: 3, mb: 4, backgroundColor: '#f8f9fa' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {breakpoints[deviceCategory]?.icon}
          <Typography variant="h6" sx={{ ml: 2, color: '#0000FF' }}>
            الجهاز الحالي: {breakpoints[deviceCategory]?.name || 'غير محدد'}
          </Typography>
        </Box>
        
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Chip 
              label={`العرض: ${screenInfo.width}px`}
              color="primary"
              sx={{ width: '100%' }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Chip 
              label={`الارتفاع: ${screenInfo.height}px`}
              color="secondary"
              sx={{ width: '100%' }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Chip 
              label={`النسبة: ${screenInfo.ratio}`}
              color="info"
              sx={{ width: '100%' }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Chip 
              label={`الدقة: ${screenInfo.pixelRatio}x`}
              color="success"
              sx={{ width: '100%' }}
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button
            variant="contained"
            startIcon={<PlayArrow />}
            onClick={runComprehensiveTests}
            disabled={isRunning}
            sx={{ backgroundColor: '#0000FF' }}
          >
            {isRunning ? 'جاري الاختبار...' : 'تشغيل الاختبارات الشاملة'}
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={updateScreenInfo}
          >
            تحديث معلومات الجهاز
          </Button>
        </Box>

        {isRunning && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              التقدم: {progress.toFixed(0)}%
            </Typography>
            <LinearProgress variant="determinate" value={progress} />
          </Box>
        )}
      </Paper>

      {/* نتائج الاختبارات */}
      {Object.keys(testResults).length > 0 && (
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h6" sx={{ mb: 3, color: '#0000FF' }}>
            نتائج الاختبارات الشاملة
          </Typography>
          
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>الاختبار</TableCell>
                  <TableCell align="center">النتيجة</TableCell>
                  <TableCell>الرسالة</TableCell>
                  <TableCell align="center">التفاصيل</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {Object.entries(testResults).map(([testName, result]) => (
                  <TableRow key={testName}>
                    <TableCell>
                      <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                        {testName}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      {getResultIcon(result)}
                    </TableCell>
                    <TableCell>
                      <Typography 
                        variant="body2" 
                        sx={{ color: getResultColor(result) }}
                      >
                        {result?.message || 'لا توجد رسالة'}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      {result?.details && (
                        <Tooltip title={JSON.stringify(result.details, null, 2)}>
                          <IconButton size="small">
                            <Visibility />
                          </IconButton>
                        </Tooltip>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* نقاط التوقف */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 3, color: '#0000FF' }}>
          نقاط التوقف المستخدمة
        </Typography>
        
        <Grid container spacing={2}>
          {Object.entries(breakpoints).map(([key, bp]) => (
            <Grid item xs={12} sm={6} md={4} key={key}>
              <Card sx={{ 
                border: deviceCategory === key ? '2px solid #0000FF' : '1px solid #e0e0e0',
                backgroundColor: deviceCategory === key ? '#e3f2fd' : 'white'
              }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    {bp.icon}
                    <Typography variant="h6" sx={{ ml: 1 }}>
                      {bp.name}
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {bp.min}px - {bp.max === Infinity ? '∞' : bp.max + 'px'}
                  </Typography>
                  <Typography variant="caption" display="block">
                    الكود: {key}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Paper>

      {/* تعليمات */}
      <Alert severity="info" sx={{ mt: 4 }}>
        <Typography variant="body2">
          <strong>كيفية الاختبار:</strong><br />
          1. قم بتغيير حجم نافذة المتصفح لاختبار نقاط التوقف المختلفة<br />
          2. اختبر على أجهزة حقيقية للحصول على نتائج دقيقة<br />
          3. تأكد من أن جميع الاختبارات تنجح في كل حجم شاشة<br />
          4. اختبر التبديل بين الاتجاهين الأفقي والعمودي<br />
          5. تحقق من سهولة الوصول لجميع العناصر
        </Typography>
      </Alert>
    </Box>
  );
};

export default TrueResponsivenessTest;
