import { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Alert
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  PersonAdd,
  School
} from '@mui/icons-material';
import { useLanguage } from '../../contexts/LanguageContext';

const EnrollmentManagement = () => {
  const { language } = useLanguage();
  
  // حالات المكون
  const [enrollments, setEnrollments] = useState([
    {
      id: 1,
      studentName: 'أحمد محمد',
      studentCode: '123456',
      courseName: 'تطوير المواقع',
      enrollmentDate: '2024-01-15',
      status: 'active'
    },
    {
      id: 2,
      studentName: 'فاطمة علي',
      studentCode: '234567',
      courseName: 'التصميم الجرافيكي',
      enrollmentDate: '2024-01-20',
      status: 'completed'
    }
  ]);
  
  const [open, setOpen] = useState(false);
  const [editingEnrollment, setEditingEnrollment] = useState(null);
  const [formData, setFormData] = useState({
    studentCode: '',
    courseName: '',
    status: 'active'
  });
  
  const handleSubmit = () => {
    // منطق إضافة/تعديل التسجيل
    setOpen(false);
    setEditingEnrollment(null);
    setFormData({ studentCode: '', courseName: '', status: 'active' });
  };
  
  return (
    <Box className="modern-fade-in">
      <Typography 
        variant="h4" 
        className="arabic-heading arabic-heading-2 arabic-text-gradient"
        sx={{ mb: 3 }}
      >
        {language === 'ar' ? 'إدارة التسجيلات' : 'Enrollment Management'}
      </Typography>
      
      <Card className="modern-card" sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" className="arabic-text-semibold">
              {language === 'ar' ? 'قائمة التسجيلات' : 'Enrollments List'}
            </Typography>
            <Button
              variant="contained"
              startIcon={<PersonAdd />}
              onClick={() => setOpen(true)}
              className="modern-button modern-button-primary"
            >
              {language === 'ar' ? 'تسجيل جديد' : 'New Enrollment'}
            </Button>
          </Box>
          
          <TableContainer component={Paper} className="modern-card">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell className="arabic-text-semibold">
                    {language === 'ar' ? 'اسم الطالب' : 'Student Name'}
                  </TableCell>
                  <TableCell className="arabic-text-semibold">
                    {language === 'ar' ? 'كود الطالب' : 'Student Code'}
                  </TableCell>
                  <TableCell className="arabic-text-semibold">
                    {language === 'ar' ? 'اسم الكورس' : 'Course Name'}
                  </TableCell>
                  <TableCell className="arabic-text-semibold">
                    {language === 'ar' ? 'تاريخ التسجيل' : 'Enrollment Date'}
                  </TableCell>
                  <TableCell className="arabic-text-semibold">
                    {language === 'ar' ? 'الحالة' : 'Status'}
                  </TableCell>
                  <TableCell className="arabic-text-semibold">
                    {language === 'ar' ? 'الإجراءات' : 'Actions'}
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {enrollments.map((enrollment) => (
                  <TableRow key={enrollment.id}>
                    <TableCell className="arabic-text">
                      {enrollment.studentName}
                    </TableCell>
                    <TableCell className="arabic-text">
                      {enrollment.studentCode}
                    </TableCell>
                    <TableCell className="arabic-text">
                      {enrollment.courseName}
                    </TableCell>
                    <TableCell className="arabic-text">
                      {enrollment.enrollmentDate}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={enrollment.status === 'active' 
                          ? (language === 'ar' ? 'نشط' : 'Active')
                          : (language === 'ar' ? 'مكتمل' : 'Completed')
                        }
                        color={enrollment.status === 'active' ? 'success' : 'primary'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <IconButton size="small" color="primary">
                        <Edit />
                      </IconButton>
                      <IconButton size="small" color="error">
                        <Delete />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
      
      {/* نافذة إضافة/تعديل التسجيل */}
      <Dialog open={open} onClose={() => setOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle className="arabic-text-semibold">
          {editingEnrollment 
            ? (language === 'ar' ? 'تعديل التسجيل' : 'Edit Enrollment')
            : (language === 'ar' ? 'تسجيل جديد' : 'New Enrollment')
          }
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <TextField
              label={language === 'ar' ? 'كود الطالب' : 'Student Code'}
              value={formData.studentCode}
              onChange={(e) => setFormData({ ...formData, studentCode: e.target.value })}
              fullWidth
              className="modern-input"
            />
            <TextField
              label={language === 'ar' ? 'اسم الكورس' : 'Course Name'}
              value={formData.courseName}
              onChange={(e) => setFormData({ ...formData, courseName: e.target.value })}
              fullWidth
              className="modern-input"
            />
            <TextField
              select
              label={language === 'ar' ? 'الحالة' : 'Status'}
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value })}
              fullWidth
              className="modern-input"
            >
              <MenuItem value="active">
                {language === 'ar' ? 'نشط' : 'Active'}
              </MenuItem>
              <MenuItem value="completed">
                {language === 'ar' ? 'مكتمل' : 'Completed'}
              </MenuItem>
            </TextField>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)}>
            {language === 'ar' ? 'إلغاء' : 'Cancel'}
          </Button>
          <Button onClick={handleSubmit} variant="contained" className="modern-button">
            {language === 'ar' ? 'حفظ' : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EnrollmentManagement;
