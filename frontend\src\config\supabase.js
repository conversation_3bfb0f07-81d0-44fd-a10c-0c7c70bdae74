import { createClient } from '@supabase/supabase-js';

// إعدادات Supabase
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'your-anon-key';

// إنشاء عميل Supabase
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// دوال مساعدة لإدارة قاعدة البيانات
export const dbHelpers = {
  // إنشاء الجداول الأساسية
  async createTables() {
    try {
      console.log('🔧 إنشاء الجداول الأساسية...');
      
      // جدول الطلاب
      await supabase.rpc('create_students_table');
      
      // جدول فئات الكورسات
      await supabase.rpc('create_course_categories_table');
      
      // جدول الكورسات
      await supabase.rpc('create_courses_table');
      
      // جدول التسجيلات
      await supabase.rpc('create_enrollments_table');
      
      // جدول فيديوهات الكورسات
      await supabase.rpc('create_course_videos_table');
      
      // جدول مواد الكورسات
      await supabase.rpc('create_course_materials_table');
      
      // جدول الشهادات
      await supabase.rpc('create_certificates_table');
      
      // جدول النشاطات
      await supabase.rpc('create_activities_table');
      
      // جدول الإعدادات
      await supabase.rpc('create_settings_table');
      
      console.log('✅ تم إنشاء جميع الجداول بنجاح');
      return { success: true };
      
    } catch (error) {
      console.error('❌ خطأ في إنشاء الجداول:', error);
      return { success: false, error };
    }
  },

  // إدراج البيانات التجريبية
  async insertSampleData() {
    try {
      console.log('📝 إدراج البيانات التجريبية...');
      
      // فئات الكورسات
      const { data: categories } = await supabase
        .from('course_categories')
        .insert([
          {
            name: 'البرمجة',
            description: 'كورسات البرمجة وتطوير البرمجيات',
            color: '#4169E1'
          },
          {
            name: 'التصميم',
            description: 'كورسات التصميم الجرافيكي وتصميم المواقع',
            color: '#FF6B35'
          },
          {
            name: 'التسويق',
            description: 'كورسات التسويق الرقمي والإلكتروني',
            color: '#32CD32'
          }
        ])
        .select();

      // الكورسات
      if (categories && categories.length > 0) {
        await supabase
          .from('courses')
          .insert([
            {
              title: 'أساسيات البرمجة',
              description: 'تعلم أساسيات البرمجة من الصفر',
              category_id: categories[0].id,
              level: 'beginner',
              duration: '40 ساعة',
              price: 299,
              status: 'active'
            },
            {
              title: 'تطوير المواقع',
              description: 'تعلم تطوير المواقع باستخدام HTML, CSS, JavaScript',
              category_id: categories[0].id,
              level: 'intermediate',
              duration: '60 ساعة',
              price: 499,
              status: 'active'
            },
            {
              title: 'التصميم الجرافيكي',
              description: 'تعلم أساسيات التصميم الجرافيكي',
              category_id: categories[1].id,
              level: 'beginner',
              duration: '30 ساعة',
              price: 199,
              status: 'active'
            }
          ]);
      }

      // طلاب تجريبيون
      await supabase
        .from('students')
        .insert([
          {
            name: 'أحمد محمد',
            email: '<EMAIL>',
            phone: '0501234567',
            student_code: '123456',
            is_active: true
          },
          {
            name: 'فاطمة علي',
            email: '<EMAIL>',
            phone: '0507654321',
            student_code: '654321',
            is_active: true
          },
          {
            name: 'محمد سالم',
            email: '<EMAIL>',
            phone: '**********',
            student_code: '789012',
            is_active: true
          }
        ]);

      console.log('✅ تم إدراج البيانات التجريبية بنجاح');
      return { success: true };
      
    } catch (error) {
      console.error('❌ خطأ في إدراج البيانات التجريبية:', error);
      return { success: false, error };
    }
  },

  // فحص حالة قاعدة البيانات
  async checkDatabaseHealth() {
    try {
      console.log('🔍 فحص حالة قاعدة البيانات...');
      
      const tables = [
        'students',
        'course_categories',
        'courses',
        'enrollments',
        'course_videos',
        'course_materials',
        'certificates',
        'activities',
        'settings'
      ];

      const results = {};
      
      for (const table of tables) {
        try {
          const { count, error } = await supabase
            .from(table)
            .select('*', { count: 'exact', head: true });
          
          if (error) {
            results[table] = { exists: false, count: 0, error: error.message };
          } else {
            results[table] = { exists: true, count: count || 0 };
          }
        } catch (err) {
          results[table] = { exists: false, count: 0, error: err.message };
        }
      }

      console.log('📊 نتائج فحص قاعدة البيانات:', results);
      return { success: true, results };
      
    } catch (error) {
      console.error('❌ خطأ في فحص قاعدة البيانات:', error);
      return { success: false, error };
    }
  },

  // تنظيف قاعدة البيانات
  async cleanDatabase() {
    try {
      console.log('🧹 تنظيف قاعدة البيانات...');
      
      // حذف البيانات من جميع الجداول
      const tables = [
        'activities',
        'certificates',
        'course_materials',
        'course_videos',
        'enrollments',
        'courses',
        'course_categories',
        'students'
      ];

      for (const table of tables) {
        await supabase.from(table).delete().neq('id', 0);
      }

      console.log('✅ تم تنظيف قاعدة البيانات بنجاح');
      return { success: true };
      
    } catch (error) {
      console.error('❌ خطأ في تنظيف قاعدة البيانات:', error);
      return { success: false, error };
    }
  },

  // إعداد قاعدة البيانات الكاملة
  async setupDatabase() {
    try {
      console.log('🚀 بدء إعداد قاعدة البيانات...');
      
      // فحص الحالة الحالية
      const healthCheck = await this.checkDatabaseHealth();
      
      // إنشاء الجداول إذا لم تكن موجودة
      const createResult = await this.createTables();
      if (!createResult.success) {
        throw new Error('فشل في إنشاء الجداول');
      }

      // إدراج البيانات التجريبية
      const sampleDataResult = await this.insertSampleData();
      if (!sampleDataResult.success) {
        console.warn('⚠️ تحذير: فشل في إدراج البيانات التجريبية');
      }

      console.log('🎉 تم إعداد قاعدة البيانات بنجاح!');
      return { success: true };
      
    } catch (error) {
      console.error('❌ خطأ في إعداد قاعدة البيانات:', error);
      return { success: false, error };
    }
  }
};

// تصدير الدوال الأساسية
export default supabase;
