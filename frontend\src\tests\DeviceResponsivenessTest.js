import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Alert,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import {
  PhoneAndroid,
  Tablet,
  Computer,
  CheckCircle,
  Error,
  PlayArrow,
  Refresh
} from '@mui/icons-material';

/**
 * مكون اختبار التجاوب مع الأجهزة المختلفة
 */
const DeviceResponsivenessTest = () => {
  const [currentDevice, setCurrentDevice] = useState('');
  const [screenInfo, setScreenInfo] = useState({});
  const [testResults, setTestResults] = useState({});
  const [activeStep, setActiveStep] = useState(0);
  const [completedTests, setCompletedTests] = useState({});

  useEffect(() => {
    updateDeviceInfo();
    window.addEventListener('resize', updateDeviceInfo);
    return () => window.removeEventListener('resize', updateDeviceInfo);
  }, []);

  const updateDeviceInfo = () => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    let device = '';
    if (width < 768) {
      device = 'mobile';
    } else if (width >= 768 && width <= 1024) {
      device = 'tablet';
    } else {
      device = 'desktop';
    }

    setCurrentDevice(device);
    setScreenInfo({
      width,
      height,
      ratio: (width / height).toFixed(2),
      orientation: width > height ? 'landscape' : 'portrait'
    });
  };

  const testSteps = [
    {
      label: 'اختبار تسجيل الدخول',
      description: 'تحقق من سهولة تسجيل الدخول على الجهاز الحالي',
      tests: [
        'حقول الإدخال واضحة ومقروءة',
        'الأزرار بحجم مناسب للمس',
        'الشعار والنصوص محاذاة بشكل صحيح',
        'لا يوجد تداخل في العناصر',
        'التبديل بين العربية والإنجليزية يعمل'
      ]
    },
    {
      label: 'اختبار لوحة التحكم',
      description: 'تحقق من تخطيط لوحة التحكم الإدارية',
      tests: [
        'الشريط العلوي يمتد عبر كامل العرض',
        'الدرج الجانبي يظهر/يختفي حسب حجم الشاشة',
        'المحتوى الرئيسي لا يتداخل مع العناصر الأخرى',
        'عناصر القائمة سهلة النقر/اللمس',
        'النصوص واضحة ومقروءة'
      ]
    },
    {
      label: 'اختبار التنقل',
      description: 'تحقق من سهولة التنقل بين الأقسام',
      tests: [
        'التنقل بين أقسام لوحة التحكم سلس',
        'الأزرار تستجيب للنقر/اللمس',
        'القوائم المنسدلة تعمل بشكل صحيح',
        'التمرير يعمل بسلاسة',
        'لا توجد عناصر مخفية أو غير قابلة للوصول'
      ]
    },
    {
      label: 'اختبار إدارة المحتوى',
      description: 'تحقق من وظائف إدارة الدورات والطلاب',
      tests: [
        'الجداول تعرض بشكل صحيح',
        'النماذج سهلة الملء',
        'أزرار الحفظ والإلغاء واضحة',
        'رسائل التأكيد تظهر بوضوح',
        'تحميل الملفات يعمل بشكل صحيح'
      ]
    },
    {
      label: 'اختبار الأداء',
      description: 'تحقق من سرعة التحميل والاستجابة',
      tests: [
        'الصفحات تحمل بسرعة',
        'الانتقالات سلسة',
        'لا توجد تأخيرات في الاستجابة',
        'الذاكرة لا تتراكم',
        'البطارية لا تستنزف بسرعة (للأجهزة المحمولة)'
      ]
    }
  ];

  const runAutomaticTests = () => {
    const results = {};
    
    // اختبار الشريط العلوي
    const appBar = document.querySelector('.admin-dashboard .MuiAppBar-root');
    results.appBarWidth = appBar ? appBar.offsetWidth >= window.innerWidth * 0.95 : false;
    
    // اختبار الدرج الجانبي
    const drawer = document.querySelector('.admin-dashboard nav');
    results.drawerVisible = drawer ? window.getComputedStyle(drawer).display !== 'none' : false;
    
    // اختبار المحتوى الرئيسي
    const main = document.querySelector('.admin-dashboard main');
    results.mainContentVisible = main ? main.offsetWidth > 0 : false;
    
    // اختبار أحجام الأزرار
    const buttons = document.querySelectorAll('.admin-dashboard .MuiButton-root');
    results.buttonSizes = Array.from(buttons).every(btn => btn.offsetHeight >= 44);
    
    // اختبار النصوص
    const texts = document.querySelectorAll('.admin-dashboard .MuiTypography-root');
    results.textReadability = Array.from(texts).every(text => {
      const style = window.getComputedStyle(text);
      return parseFloat(style.fontSize) >= 14;
    });

    setTestResults(results);
  };

  const handleTestComplete = (stepIndex, testIndex) => {
    const key = `${stepIndex}-${testIndex}`;
    setCompletedTests(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const getDeviceIcon = () => {
    switch (currentDevice) {
      case 'mobile': return <PhoneAndroid sx={{ color: '#4CAF50' }} />;
      case 'tablet': return <Tablet sx={{ color: '#2196F3' }} />;
      case 'desktop': return <Computer sx={{ color: '#FF9800' }} />;
      default: return null;
    }
  };

  const getDeviceName = () => {
    switch (currentDevice) {
      case 'mobile': return 'هاتف ذكي';
      case 'tablet': return 'جهاز لوحي';
      case 'desktop': return 'سطح مكتب';
      default: return 'غير محدد';
    }
  };

  const getStepProgress = (stepIndex) => {
    const stepTests = testSteps[stepIndex].tests;
    const completedCount = stepTests.filter((_, testIndex) => 
      completedTests[`${stepIndex}-${testIndex}`]
    ).length;
    return (completedCount / stepTests.length) * 100;
  };

  const getTotalProgress = () => {
    const totalTests = testSteps.reduce((sum, step) => sum + step.tests.length, 0);
    const completedCount = Object.keys(completedTests).filter(key => completedTests[key]).length;
    return (completedCount / totalTests) * 100;
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" sx={{ mb: 3, textAlign: 'center', color: '#0000FF' }}>
        اختبار التجاوب مع الأجهزة المختلفة
      </Typography>

      {/* معلومات الجهاز الحالي */}
      <Paper sx={{ p: 3, mb: 4, backgroundColor: '#f8f9fa' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {getDeviceIcon()}
          <Typography variant="h6" sx={{ ml: 2, color: '#0000FF' }}>
            الجهاز الحالي: {getDeviceName()}
          </Typography>
        </Box>
        
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Chip 
              label={`العرض: ${screenInfo.width}px`}
              color="primary"
              sx={{ width: '100%' }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Chip 
              label={`الارتفاع: ${screenInfo.height}px`}
              color="secondary"
              sx={{ width: '100%' }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Chip 
              label={`النسبة: ${screenInfo.ratio}`}
              color="info"
              sx={{ width: '100%' }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Chip 
              label={`الاتجاه: ${screenInfo.orientation === 'landscape' ? 'أفقي' : 'عمودي'}`}
              color="success"
              sx={{ width: '100%' }}
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button
            variant="contained"
            startIcon={<PlayArrow />}
            onClick={runAutomaticTests}
            sx={{ backgroundColor: '#0000FF' }}
          >
            تشغيل الاختبارات التلقائية
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={updateDeviceInfo}
          >
            تحديث معلومات الجهاز
          </Button>
        </Box>
      </Paper>

      {/* نتائج الاختبارات التلقائية */}
      {Object.keys(testResults).length > 0 && (
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h6" sx={{ mb: 2, color: '#0000FF' }}>
            نتائج الاختبارات التلقائية
          </Typography>
          
          <Grid container spacing={2}>
            {Object.entries(testResults).map(([test, result]) => (
              <Grid item xs={12} sm={6} md={4} key={test}>
                <Card sx={{ 
                  border: `2px solid ${result ? '#4CAF50' : '#F44336'}`,
                  backgroundColor: result ? '#E8F5E8' : '#FFEBEE'
                }}>
                  <CardContent sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      {result ? 
                        <CheckCircle sx={{ color: '#4CAF50', mr: 1 }} /> :
                        <Error sx={{ color: '#F44336', mr: 1 }} />
                      }
                      <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                        {test}
                      </Typography>
                    </Box>
                    <Typography variant="caption" sx={{ color: '#666' }}>
                      {result ? 'نجح الاختبار' : 'فشل الاختبار'}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Paper>
      )}

      {/* شريط التقدم الإجمالي */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" sx={{ mb: 2, color: '#0000FF' }}>
          التقدم الإجمالي: {getTotalProgress().toFixed(0)}%
        </Typography>
        <Box sx={{ 
          width: '100%', 
          height: 10, 
          backgroundColor: '#e0e0e0', 
          borderRadius: 5,
          overflow: 'hidden'
        }}>
          <Box sx={{ 
            width: `${getTotalProgress()}%`, 
            height: '100%', 
            backgroundColor: '#4CAF50',
            transition: 'width 0.3s ease'
          }} />
        </Box>
      </Paper>

      {/* خطوات الاختبار */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 3, color: '#0000FF' }}>
          خطوات الاختبار اليدوي
        </Typography>
        
        <Stepper activeStep={activeStep} orientation="vertical">
          {testSteps.map((step, stepIndex) => (
            <Step key={stepIndex}>
              <StepLabel>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Typography variant="h6">{step.label}</Typography>
                  <Chip 
                    label={`${getStepProgress(stepIndex).toFixed(0)}%`}
                    size="small"
                    color={getStepProgress(stepIndex) === 100 ? 'success' : 'default'}
                  />
                </Box>
              </StepLabel>
              <StepContent>
                <Typography variant="body2" sx={{ mb: 2, color: '#666' }}>
                  {step.description}
                </Typography>
                
                <List>
                  {step.tests.map((test, testIndex) => (
                    <ListItem key={testIndex} sx={{ pl: 0 }}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={completedTests[`${stepIndex}-${testIndex}`] || false}
                            onChange={() => handleTestComplete(stepIndex, testIndex)}
                            color="primary"
                          />
                        }
                        label={test}
                        sx={{ width: '100%' }}
                      />
                    </ListItem>
                  ))}
                </List>
                
                <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    onClick={() => setActiveStep(stepIndex + 1)}
                    disabled={stepIndex === testSteps.length - 1}
                    size="small"
                  >
                    التالي
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => setActiveStep(stepIndex - 1)}
                    disabled={stepIndex === 0}
                    size="small"
                  >
                    السابق
                  </Button>
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>
      </Paper>

      {/* تعليمات إضافية */}
      <Alert severity="info" sx={{ mt: 4 }}>
        <Typography variant="body2">
          <strong>نصائح للاختبار:</strong><br />
          • اختبر على أجهزة حقيقية عندما أمكن<br />
          • جرب تدوير الجهاز (أفقي/عمودي)<br />
          • اختبر بسرعات إنترنت مختلفة<br />
          • تأكد من إمكانية الوصول لجميع الوظائف<br />
          • اختبر مع مستخدمين حقيقيين
        </Typography>
      </Alert>
    </Box>
  );
};

export default DeviceResponsivenessTest;
