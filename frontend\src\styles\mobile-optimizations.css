/* تحسينات الأداء للأجهزة المحمولة */

/* تحسينات شاملة للأجهزة المحمولة */
@media (max-width: 767px) {
  /* تحسين الخلفيات */
  .mobile-optimized,
  .admin-dashboard {
    background-attachment: scroll !important;
    background-size: 100% 100% !important;
    will-change: opacity !important;
    transform: none !important;

    /* تحسين التفاعل للأجهزة المحمولة */
    touch-action: manipulation !important;
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain !important;

    /* إصلاح التخطيط للأجهزة المحمولة */
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
    min-height: 100vh !important;
    overflow-x: hidden !important;
  }

  /* إصلاح الشريط العلوي للأجهزة المحمولة */
  .admin-dashboard .MuiAppBar-root {
    width: 100% !important;
    height: 56px !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1300 !important;
  }

  /* إصلاح شريط الأدوات */
  .admin-dashboard .MuiToolbar-root {
    min-height: 56px !important;
    padding: 0 16px !important;
    justify-content: space-between !important;
  }

  /* إصلاح الدرج الجانبي للأجهزة المحمولة */
  .admin-dashboard .MuiDrawer-paper {
    width: 280px !important;
    max-width: 85vw !important;
    height: 100vh !important;
    top: 0 !important;
    z-index: 1400 !important;
  }

  /* إصلاح المحتوى الرئيسي للأجهزة المحمولة */
  .admin-dashboard main {
    margin-top: 56px !important;
    margin-right: 0 !important;
    margin-left: 0 !important;
    width: 100% !important;
    padding: 16px !important;
    min-height: calc(100vh - 56px) !important;
    overflow-x: hidden !important;
  }

  /* تحسين الأزرار للأجهزة المحمولة */
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root,
  .admin-dashboard .MuiButton-root {
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 12px !important;
    font-size: 1rem !important;
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: rgba(0, 0, 255, 0.1) !important;
  }

  /* تحسين النصوص للأجهزة المحمولة */
  .admin-dashboard .MuiTypography-h6 {
    font-size: 1.1rem !important;
    line-height: 1.4 !important;
  }

  .admin-dashboard .MuiListItemText-primary {
    font-size: 0.95rem !important;
    line-height: 1.5 !important;
  }

  /* تحسين عناصر القائمة للأجهزة المحمولة */
  .admin-dashboard .MuiListItem-root {
    min-height: 52px !important;
    padding: 8px 16px !important;
    margin-bottom: 4px !important;
  }

  /* تحسين الأيقونات */
  .admin-dashboard .MuiSvgIcon-root {
    font-size: 1.3rem !important;
  }

  /* تحسين الحاويات */
  .admin-dashboard .MuiContainer-root {
    padding: 0 16px !important;
    max-width: 100% !important;
  }

  /* تحسين الجداول للأجهزة المحمولة */
  .admin-dashboard .MuiTable-root {
    font-size: 0.85rem !important;
  }

  .admin-dashboard .MuiTableCell-root {
    padding: 8px 12px !important;
    font-size: 0.85rem !important;
  }

  /* تحسين الحقول */
  .admin-dashboard .MuiTextField-root {
    margin-bottom: 16px !important;
    width: 100% !important;
  }

  .admin-dashboard .MuiInputBase-root {
    font-size: 1rem !important;
    padding: 12px 16px !important;
  }

  /* تحسين البطاقات */
  .admin-dashboard .MuiCard-root {
    margin-bottom: 16px !important;
    border-radius: 12px !important;
  }

  .admin-dashboard .MuiCardContent-root {
    padding: 16px !important;
  }

  /* إخفاء الدرج الثابت على الأجهزة المحمولة */
  .admin-dashboard nav > div:not(.MuiDrawer-paper) {
    display: none !important;
  }
}

/* تحسينات إضافية للأجهزة المحمولة */
@media (max-width: 899px) {
  /* تحسين الانتقالات */
  .mobile-optimized * {
    transition-duration: 0.2s !important;
    transition-timing-function: ease-out !important;
  }

  /* تحسين الصور */
  .mobile-optimized img {
    image-rendering: auto !important;
    will-change: auto !important;
  }

  /* تحسين الفلاتر */
  .mobile-optimized [style*="blur"] {
    filter: none !important;
    backdrop-filter: blur(4px) !important;
  }

  /* تحسين الظلال */
  .mobile-optimized [style*="box-shadow"] {
    box-shadow: 0 4px 12px rgba(0, 0, 255, 0.15) !important;
  }
}

/* تحسينات للأجهزة الصغيرة جداً */
@media (max-width: 480px) {
  .mobile-optimized {
    background: linear-gradient(135deg, rgba(0, 0, 255, 0.02) 0%, rgba(65, 105, 225, 0.03) 100%) !important;
  }
  
  .mobile-optimized [style*="backdrop-filter"] {
    backdrop-filter: blur(2px) !important;
  }
  
  .mobile-optimized [style*="box-shadow"] {
    box-shadow: 0 2px 8px rgba(0, 0, 255, 0.1) !important;
  }
}

/* تحسينات شاملة للأجهزة اللوحية */
@media (min-width: 768px) and (max-width: 1024px) {
  .background-loaded {
    background-attachment: scroll !important;
    background-size: cover !important;
  }

  .background-loaded [style*="backdrop-filter"] {
    backdrop-filter: blur(8px) saturate(1.2) !important;
  }

  /* إصلاحات التخطيط للأجهزة اللوحية */
  .admin-dashboard {
    display: flex !important;
    flex-direction: row !important;
    width: 100% !important;
    min-height: 100vh !important;
    overflow-x: hidden !important;
  }

  /* إصلاح الشريط العلوي للأجهزة اللوحية */
  .admin-dashboard .MuiAppBar-root {
    width: 100% !important;
    height: 64px !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1300 !important;
  }

  /* إصلاح شريط الأدوات للأجهزة اللوحية */
  .admin-dashboard .MuiToolbar-root {
    min-height: 64px !important;
    padding: 0 24px !important;
    justify-content: space-between !important;
  }

  /* إصلاح الدرج الجانبي للأجهزة اللوحية */
  .admin-dashboard nav {
    width: 300px !important;
    position: fixed !important;
    top: 64px !important;
    right: 0 !important;
    height: calc(100vh - 64px) !important;
    z-index: 1200 !important;
  }

  .admin-dashboard .MuiDrawer-paper {
    width: 300px !important;
    height: 100% !important;
    position: relative !important;
    top: 0 !important;
  }

  /* إصلاح المحتوى الرئيسي للأجهزة اللوحية */
  .admin-dashboard main {
    margin-top: 64px !important;
    margin-right: 300px !important;
    margin-left: 0 !important;
    width: calc(100% - 300px) !important;
    padding: 24px !important;
    min-height: calc(100vh - 64px) !important;
    overflow-x: hidden !important;
  }

  /* تحسينات خاصة للوحة التحكم الإدارية على الأجهزة اللوحية */
  .admin-dashboard .MuiButton-root {
    min-height: 48px !important;
    padding: 12px 20px !important;
    font-size: 0.95rem !important;
    border-radius: 8px !important;
    touch-action: manipulation !important;
  }

  /* تحسين عناصر القائمة للأجهزة اللوحية */
  .admin-dashboard .MuiListItem-root {
    min-height: 56px !important;
    padding: 12px 20px !important;
  }

  /* تحسين الأيقونات للأجهزة اللوحية */
  .admin-dashboard .MuiSvgIcon-root {
    font-size: 1.4rem !important;
  }

  /* تحسين النصوص للأجهزة اللوحية */
  .admin-dashboard .MuiTypography-body1 {
    font-size: 1rem !important;
  }

  /* تحسين الجداول للأجهزة اللوحية */
  .admin-dashboard .MuiTableCell-root {
    padding: 16px !important;
    font-size: 0.95rem !important;
  }

  /* تحسين الحقول للأجهزة اللوحية */
  .admin-dashboard .MuiTextField-root {
    margin-bottom: 16px !important;
  }

  .admin-dashboard .MuiInputBase-root {
    font-size: 1rem !important;
    padding: 12px 16px !important;
  }

  /* تحسين البطاقات للأجهزة اللوحية */
  .admin-dashboard .MuiCard-root {
    border-radius: 12px !important;
    margin-bottom: 20px !important;
  }

  .admin-dashboard .MuiCardContent-root {
    padding: 20px !important;
  }

  /* تحسين الحاويات للأجهزة اللوحية */
  .admin-dashboard .MuiContainer-root {
    padding: 0 24px !important;
    max-width: 100% !important;
  }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1025px) {
  /* إصلاحات التخطيط للشاشات الكبيرة */
  .admin-dashboard {
    display: flex !important;
    flex-direction: row !important;
    width: 100% !important;
    min-height: 100vh !important;
    overflow-x: hidden !important;
  }

  /* إصلاح الشريط العلوي للشاشات الكبيرة */
  .admin-dashboard .MuiAppBar-root {
    width: 100% !important;
    height: 68px !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1300 !important;
  }

  /* إصلاح شريط الأدوات للشاشات الكبيرة */
  .admin-dashboard .MuiToolbar-root {
    min-height: 68px !important;
    padding: 0 32px !important;
    justify-content: space-between !important;
  }

  /* إصلاح الدرج الجانبي للشاشات الكبيرة */
  .admin-dashboard nav {
    width: 320px !important;
    position: fixed !important;
    top: 68px !important;
    right: 0 !important;
    height: calc(100vh - 68px) !important;
    z-index: 1200 !important;
  }

  .admin-dashboard .MuiDrawer-paper {
    width: 320px !important;
    height: 100% !important;
    position: relative !important;
    top: 0 !important;
  }

  /* إصلاح المحتوى الرئيسي للشاشات الكبيرة */
  .admin-dashboard main {
    margin-top: 68px !important;
    margin-right: 320px !important;
    margin-left: 0 !important;
    width: calc(100% - 320px) !important;
    padding: 32px !important;
    min-height: calc(100vh - 68px) !important;
    overflow-x: hidden !important;
  }

  /* تحسينات للشاشات الكبيرة */
  .admin-dashboard .MuiButton-root {
    min-height: 44px !important;
    padding: 10px 20px !important;
    font-size: 0.9rem !important;
    border-radius: 8px !important;
  }

  .admin-dashboard .MuiListItem-root {
    min-height: 52px !important;
    padding: 10px 16px !important;
  }

  .admin-dashboard .MuiSvgIcon-root {
    font-size: 1.3rem !important;
  }

  .admin-dashboard .MuiContainer-root {
    padding: 0 32px !important;
    max-width: 100% !important;
  }
}

/* تحسينات الأداء العامة */
.performance-optimized {
  /* تحسين الرسم */
  contain: layout style paint;
  
  /* تحسين التمرير */
  -webkit-overflow-scrolling: touch;
  
  /* تحسين الخطوط */
  text-rendering: optimizeSpeed;
  
  /* تحسين الصور */
  image-rendering: -webkit-optimize-contrast;
}

/* تحسين التحميل السريع */
.instant-load {
  opacity: 1 !important;
  transition: none !important;
  animation: none !important;
}

/* تحسين الذاكرة */
.memory-optimized {
  will-change: auto !important;
  transform: none !important;
  filter: none !important;
}

/* خلفية بسيطة للأجهزة المحمولة */
.mobile-simple-bg {
  background: linear-gradient(135deg, 
    rgba(0, 0, 255, 0.02) 0%, 
    rgba(65, 105, 225, 0.03) 25%,
    rgba(99, 102, 241, 0.02) 50%,
    rgba(65, 105, 225, 0.03) 75%,
    rgba(0, 0, 255, 0.02) 100%) !important;
  background-attachment: scroll !important;
  background-size: 100% 100% !important;
  background-repeat: no-repeat !important;
}

/* تحسين النصوص للأجهزة المحمولة */
@media (max-width: 768px) {
  .mobile-text-optimized {
    font-smooth: never !important;
    -webkit-font-smoothing: subpixel-antialiased !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
  }
}

/* تحسين الأزرار للأجهزة اللمسية */
@media (hover: none) and (pointer: coarse) {
  .touch-optimized,
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root,
  .admin-dashboard .MuiButton-root {
    min-height: 44px !important;
    min-width: 44px !important;
    padding: 12px 16px !important;
    touch-action: manipulation !important;
    user-select: none !important;
    cursor: pointer !important;
  }

  .touch-optimized:hover,
  .admin-dashboard .MuiIconButton-root:hover,
  .admin-dashboard .MuiListItemButton-root:hover,
  .admin-dashboard .MuiButton-root:hover {
    transform: none !important;
    box-shadow: none !important;
  }

  .touch-optimized:active,
  .admin-dashboard .MuiIconButton-root:active,
  .admin-dashboard .MuiListItemButton-root:active,
  .admin-dashboard .MuiButton-root:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease-out !important;
    background-color: rgba(0, 0, 255, 0.1) !important;
  }
}

/* تحسينات خاصة للوحة التحكم الإدارية */
.admin-dashboard {
  /* تحسين التفاعل العام */
  * {
    -webkit-tap-highlight-color: transparent !important;
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
  }

  /* تحسين الأزرار */
  .MuiIconButton-root,
  .MuiListItemButton-root,
  .MuiButton-root {
    touch-action: manipulation !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
  }

  /* تحسين النصوص القابلة للتحديد */
  .MuiTypography-root,
  .MuiListItemText-root {
    user-select: text !important;
    -webkit-user-select: text !important;
  }
}

/* تحسين الانتقالات للأجهزة البطيئة */
@media (prefers-reduced-motion: reduce) {
  .reduced-motion {
    animation: none !important;
    transition: none !important;
  }
}

/* تحسين استهلاك البطارية */
@media (prefers-reduced-motion: reduce), (max-width: 768px) {
  .battery-optimized {
    animation-play-state: paused !important;
    transition-duration: 0.1s !important;
    will-change: auto !important;
    transform: none !important;
    filter: none !important;
    backdrop-filter: none !important;
  }
}

/* تحسين الشبكة البطيئة */
@media (max-width: 768px) {
  .slow-network-optimized {
    background-image: none !important;
    background: linear-gradient(135deg, rgba(0, 0, 255, 0.02) 0%, rgba(65, 105, 225, 0.03) 100%) !important;
  }
  
  .slow-network-optimized img {
    display: none !important;
  }
  
  .slow-network-optimized [style*="url("] {
    background-image: none !important;
  }
}

/* تحسين الذاكرة المنخفضة */
@media (max-width: 480px) {
  .low-memory-optimized {
    background: #f8f9fa !important;
    box-shadow: none !important;
    border: 1px solid #e9ecef !important;
    backdrop-filter: none !important;
    filter: none !important;
  }
}

/* تحسين الأداء للشاشات عالية الكثافة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .retina-optimized {
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
  }
}

/* تحسين التمرير */
.scroll-optimized {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: auto;
  overscroll-behavior: contain;
}

/* تحسين الإدخال للأجهزة اللمسية */
.input-optimized {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* تحسين الخطوط */
.font-optimized {
  font-display: swap;
  font-variant-ligatures: none;
  font-feature-settings: "kern" 0;
}

/* تحسين الرسوم المتحركة */
@keyframes fadeInFast {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fast-fade-in {
  animation: fadeInFast 0.2s ease-out forwards;
}

/* تحسين التحميل التدريجي */
.progressive-load {
  opacity: 0;
  transition: opacity 0.2s ease-out;
}

.progressive-load.loaded {
  opacity: 1;
}

/* تحسين الشبكة */
@media (max-width: 768px) and (max-bandwidth: 1mbps) {
  .bandwidth-optimized {
    background: #ffffff !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    backdrop-filter: none !important;
    filter: none !important;
  }
}
