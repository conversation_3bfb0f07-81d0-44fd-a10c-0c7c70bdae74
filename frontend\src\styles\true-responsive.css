/* التجاوب الحقيقي للوحة التحكم الإدارية */
/* True Responsive Design for Admin Dashboard */

/* ===== إعادة تعيين أساسية ===== */
* {
  box-sizing: border-box !important;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none !important;
}

html {
  font-size: 16px !important;
  -webkit-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
}

body {
  margin: 0 !important;
  padding: 0 !important;
  overflow-x: hidden !important;
  touch-action: manipulation !important;
  -webkit-overflow-scrolling: touch !important;
}

/* ===== متغيرات CSS للتجاوب الحقيقي ===== */
:root {
  /* نقاط التوقف الحقيقية */
  --xs-max: 599px;
  --sm-min: 600px;
  --sm-max: 899px;
  --md-min: 900px;
  --md-max: 1199px;
  --lg-min: 1200px;
  --lg-max: 1535px;
  --xl-min: 1536px;

  /* أحجام الدرج الجانبي */
  --drawer-xs: 280px;
  --drawer-sm: 300px;
  --drawer-md: 320px;
  --drawer-lg: 340px;
  --drawer-xl: 360px;

  /* ارتفاع الشريط العلوي */
  --appbar-xs: 56px;
  --appbar-sm: 60px;
  --appbar-md: 64px;
  --appbar-lg: 68px;
  --appbar-xl: 72px;

  /* أحجام اللمس */
  --touch-xs: 48px;
  --touch-sm: 52px;
  --touch-md: 48px;
  --touch-lg: 44px;
  --touch-xl: 44px;

  /* المسافات */
  --spacing-xs: 16px;
  --spacing-sm: 20px;
  --spacing-md: 24px;
  --spacing-lg: 32px;
  --spacing-xl: 40px;
}

/* ===== الأجهزة المحمولة الصغيرة (< 600px) ===== */
@media (max-width: 599px) {
  .admin-dashboard {
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
    min-height: 100vh !important;
    overflow-x: hidden !important;
  }

  /* الشريط العلوي */
  .admin-dashboard .MuiAppBar-root {
    width: 100% !important;
    height: var(--appbar-xs) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1300 !important;
  }

  .admin-dashboard .MuiToolbar-root {
    min-height: var(--appbar-xs) !important;
    padding: 0 var(--spacing-xs) !important;
    justify-content: space-between !important;
  }

  /* الدرج الجانبي */
  .admin-dashboard .MuiDrawer-paper {
    width: var(--drawer-xs) !important;
    max-width: 85vw !important;
    height: 100vh !important;
    top: 0 !important;
    z-index: 1400 !important;
  }

  /* إخفاء الدرج الثابت */
  .admin-dashboard nav > div:not(.MuiDrawer-paper) {
    display: none !important;
  }

  /* المحتوى الرئيسي */
  .admin-dashboard main {
    margin-top: var(--appbar-xs) !important;
    margin-right: 0 !important;
    margin-left: 0 !important;
    width: 100% !important;
    padding: var(--spacing-xs) !important;
    min-height: calc(100vh - var(--appbar-xs)) !important;
  }

  /* الأزرار والعناصر التفاعلية */
  .admin-dashboard .MuiButton-root,
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root {
    min-height: var(--touch-xs) !important;
    min-width: var(--touch-xs) !important;
    padding: 12px !important;
    font-size: 0.9rem !important;
  }

  /* النصوص */
  .admin-dashboard .MuiTypography-h6 {
    font-size: 1.1rem !important;
  }

  .admin-dashboard .MuiListItemText-primary {
    font-size: 0.95rem !important;
  }

  /* الجداول */
  .admin-dashboard .MuiTable-root {
    font-size: 0.85rem !important;
  }

  .admin-dashboard .MuiTableCell-root {
    padding: 8px 12px !important;
    font-size: 0.85rem !important;
  }

  /* الحقول */
  .admin-dashboard .MuiTextField-root {
    width: 100% !important;
    margin-bottom: 16px !important;
  }

  /* البطاقات */
  .admin-dashboard .MuiCard-root {
    margin-bottom: 16px !important;
    border-radius: 12px !important;
  }
}

/* ===== الأجهزة اللوحية الصغيرة (600px - 899px) ===== */
@media (min-width: 600px) and (max-width: 899px) {
  .admin-dashboard {
    display: flex !important;
    flex-direction: row !important;
    width: 100% !important;
    min-height: 100vh !important;
  }

  /* الشريط العلوي */
  .admin-dashboard .MuiAppBar-root {
    width: 100% !important;
    height: var(--appbar-sm) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1300 !important;
  }

  .admin-dashboard .MuiToolbar-root {
    min-height: var(--appbar-sm) !important;
    padding: 0 var(--spacing-sm) !important;
  }

  /* الدرج الجانبي الثابت */
  .admin-dashboard nav {
    width: var(--drawer-sm) !important;
    position: fixed !important;
    top: var(--appbar-sm) !important;
    right: 0 !important;
    height: calc(100vh - var(--appbar-sm)) !important;
    z-index: 1200 !important;
  }

  .admin-dashboard .MuiDrawer-paper {
    width: var(--drawer-sm) !important;
    height: 100% !important;
    position: relative !important;
    top: 0 !important;
  }

  /* إخفاء الدرج المؤقت */
  .admin-dashboard .MuiDrawer-temporary {
    display: none !important;
  }

  /* المحتوى الرئيسي */
  .admin-dashboard main {
    margin-top: var(--appbar-sm) !important;
    margin-right: var(--drawer-sm) !important;
    margin-left: 0 !important;
    width: calc(100% - var(--drawer-sm)) !important;
    padding: var(--spacing-sm) !important;
    min-height: calc(100vh - var(--appbar-sm)) !important;
  }

  /* الأزرار */
  .admin-dashboard .MuiButton-root,
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root {
    min-height: var(--touch-sm) !important;
    min-width: var(--touch-sm) !important;
    padding: 12px 20px !important;
    font-size: 1rem !important;
  }
}

/* ===== الأجهزة اللوحية الكبيرة (900px - 1199px) ===== */
@media (min-width: 900px) and (max-width: 1199px) {
  .admin-dashboard {
    display: flex !important;
    flex-direction: row !important;
  }

  /* الشريط العلوي */
  .admin-dashboard .MuiAppBar-root {
    width: 100% !important;
    height: var(--appbar-md) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1300 !important;
  }

  .admin-dashboard .MuiToolbar-root {
    min-height: var(--appbar-md) !important;
    padding: 0 var(--spacing-md) !important;
  }

  /* الدرج الجانبي */
  .admin-dashboard nav {
    width: var(--drawer-md) !important;
    position: fixed !important;
    top: var(--appbar-md) !important;
    right: 0 !important;
    height: calc(100vh - var(--appbar-md)) !important;
    z-index: 1200 !important;
  }

  .admin-dashboard .MuiDrawer-paper {
    width: var(--drawer-md) !important;
    height: 100% !important;
    position: relative !important;
    top: 0 !important;
  }

  /* المحتوى الرئيسي */
  .admin-dashboard main {
    margin-top: var(--appbar-md) !important;
    margin-right: var(--drawer-md) !important;
    margin-left: 0 !important;
    width: calc(100% - var(--drawer-md)) !important;
    padding: var(--spacing-md) !important;
    min-height: calc(100vh - var(--appbar-md)) !important;
  }

  /* الأزرار */
  .admin-dashboard .MuiButton-root,
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root {
    min-height: var(--touch-md) !important;
    min-width: var(--touch-md) !important;
    padding: 12px 24px !important;
    font-size: 1rem !important;
  }
}

/* ===== أجهزة الكمبيوتر المحمولة (1200px - 1535px) ===== */
@media (min-width: 1200px) and (max-width: 1535px) {
  .admin-dashboard {
    display: flex !important;
    flex-direction: row !important;
  }

  /* الشريط العلوي */
  .admin-dashboard .MuiAppBar-root {
    width: 100% !important;
    height: var(--appbar-lg) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1300 !important;
  }

  .admin-dashboard .MuiToolbar-root {
    min-height: var(--appbar-lg) !important;
    padding: 0 var(--spacing-lg) !important;
  }

  /* الدرج الجانبي */
  .admin-dashboard nav {
    width: var(--drawer-lg) !important;
    position: fixed !important;
    top: var(--appbar-lg) !important;
    right: 0 !important;
    height: calc(100vh - var(--appbar-lg)) !important;
    z-index: 1200 !important;
  }

  .admin-dashboard .MuiDrawer-paper {
    width: var(--drawer-lg) !important;
    height: 100% !important;
    position: relative !important;
    top: 0 !important;
  }

  /* المحتوى الرئيسي */
  .admin-dashboard main {
    margin-top: var(--appbar-lg) !important;
    margin-right: var(--drawer-lg) !important;
    margin-left: 0 !important;
    width: calc(100% - var(--drawer-lg)) !important;
    padding: var(--spacing-lg) !important;
    min-height: calc(100vh - var(--appbar-lg)) !important;
  }

  /* الأزرار */
  .admin-dashboard .MuiButton-root,
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root {
    min-height: var(--touch-lg) !important;
    min-width: var(--touch-lg) !important;
    padding: 10px 20px !important;
    font-size: 1rem !important;
  }
}

/* ===== الشاشات الكبيرة (> 1536px) ===== */
@media (min-width: 1536px) {
  .admin-dashboard {
    display: flex !important;
    flex-direction: row !important;
  }

  /* الشريط العلوي */
  .admin-dashboard .MuiAppBar-root {
    width: 100% !important;
    height: var(--appbar-xl) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1300 !important;
  }

  .admin-dashboard .MuiToolbar-root {
    min-height: var(--appbar-xl) !important;
    padding: 0 var(--spacing-xl) !important;
  }

  /* الدرج الجانبي */
  .admin-dashboard nav {
    width: var(--drawer-xl) !important;
    position: fixed !important;
    top: var(--appbar-xl) !important;
    right: 0 !important;
    height: calc(100vh - var(--appbar-xl)) !important;
    z-index: 1200 !important;
  }

  .admin-dashboard .MuiDrawer-paper {
    width: var(--drawer-xl) !important;
    height: 100% !important;
    position: relative !important;
    top: 0 !important;
  }

  /* المحتوى الرئيسي */
  .admin-dashboard main {
    margin-top: var(--appbar-xl) !important;
    margin-right: var(--drawer-xl) !important;
    margin-left: 0 !important;
    width: calc(100% - var(--drawer-xl)) !important;
    padding: var(--spacing-xl) !important;
    min-height: calc(100vh - var(--appbar-xl)) !important;
  }

  /* الأزرار */
  .admin-dashboard .MuiButton-root,
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root {
    min-height: var(--touch-xl) !important;
    min-width: var(--touch-xl) !important;
    padding: 10px 24px !important;
    font-size: 1rem !important;
  }
}

/* ===== تحسينات عامة للتجاوب ===== */

/* تحسينات اللمس */
.admin-dashboard .MuiListItem-root {
  touch-action: manipulation !important;
  -webkit-tap-highlight-color: rgba(0, 0, 255, 0.1) !important;
}

/* تحسينات التمرير */
.admin-dashboard nav,
.admin-dashboard main {
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
}

/* تحسينات الأداء */
.admin-dashboard {
  will-change: auto !important;
  transform: translateZ(0) !important;
  backface-visibility: hidden !important;
}

/* تحسينات النصوص */
.admin-dashboard .MuiTypography-root {
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* تحسينات الحاويات */
.admin-dashboard .MuiContainer-root {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
}

/* تحسينات البطاقات */
.admin-dashboard .MuiCard-root {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* تحسينات الجداول للأجهزة الصغيرة */
@media (max-width: 899px) {
  .admin-dashboard .MuiTable-root {
    display: block !important;
    overflow-x: auto !important;
    white-space: nowrap !important;
  }

  .admin-dashboard .MuiTableRow-root {
    display: table-row !important;
  }

  .admin-dashboard .MuiTableCell-root {
    display: table-cell !important;
    min-width: 120px !important;
  }
}

/* تحسينات RTL */
[dir="rtl"] .admin-dashboard nav {
  right: 0 !important;
  left: auto !important;
}

[dir="rtl"] .admin-dashboard main {
  margin-right: var(--drawer-width) !important;
  margin-left: 0 !important;
}

[dir="ltr"] .admin-dashboard nav {
  left: 0 !important;
  right: auto !important;
}

[dir="ltr"] .admin-dashboard main {
  margin-left: var(--drawer-width) !important;
  margin-right: 0 !important;
}

/* تحسينات الطباعة */
@media print {
  .admin-dashboard nav,
  .admin-dashboard .MuiAppBar-root {
    display: none !important;
  }

  .admin-dashboard main {
    margin: 0 !important;
    width: 100% !important;
    padding: 20px !important;
  }
}

/* تحسينات إمكانية الوصول */
@media (prefers-reduced-motion: reduce) {
  .admin-dashboard * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* تحسينات الألوان عالية التباين */
@media (prefers-contrast: high) {
  .admin-dashboard .MuiButton-root {
    border: 2px solid currentColor !important;
  }

  .admin-dashboard .MuiCard-root {
    border: 1px solid #000 !important;
  }
}

/* تحسينات الشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .admin-dashboard .MuiSvgIcon-root {
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
  }
}
