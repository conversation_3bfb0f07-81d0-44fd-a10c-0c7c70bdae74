import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Paper,
  LinearProgress
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  VideoLibrary,
  PictureAsPdf,
  Upload,
  Visibility,
  School,
  Category,
  PlayArrow,
  GetApp,
  CloudUpload,
  CheckCircle,
  Error as ErrorIcon
} from '@mui/icons-material';
import { useLanguage } from '../../contexts/LanguageContext';
// import { supabase } from '../../config/supabase';
import toast from 'react-hot-toast';

const CourseManagement = () => {
  const { language, t } = useLanguage();
  const [loading, setLoading] = useState(true);
  const [courses, setCourses] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedTab, setSelectedTab] = useState(0);
  
  // حالات النوافذ المنبثقة
  const [courseDialog, setCourseDialog] = useState(false);
  const [categoryDialog, setCategoryDialog] = useState(false);
  const [uploadDialog, setUploadDialog] = useState(false);
  
  // حالات النماذج
  const [courseForm, setCourseForm] = useState({
    title: '',
    description: '',
    category_id: '',
    level: 'beginner',
    duration: '',
    price: '',
    thumbnail: null
  });
  
  const [categoryForm, setCategoryForm] = useState({
    name: '',
    description: '',
    color: '#4169E1'
  });
  
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // محاكاة تحميل البيانات من قاعدة البيانات
      // في التطبيق الحقيقي، ستكون هذه استعلامات Supabase

      const categoriesData = [
        { id: 1, name: 'البرمجة', description: 'كورسات البرمجة', color: '#4169E1' },
        { id: 2, name: 'التصميم', description: 'كورسات التصميم', color: '#FF6B35' }
      ];

      const coursesData = [
        {
          id: 1,
          title: 'أساسيات البرمجة',
          description: 'تعلم أساسيات البرمجة من الصفر',
          level: 'beginner',
          duration: '40 ساعة',
          price: '299',
          course_categories: { name: 'البرمجة', color: '#4169E1' },
          course_videos: [{ count: 25 }],
          course_materials: [{ count: 15 }]
        }
      ];
      
      setCategories(categoriesData);
      setCourses(coursesData);
      
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      toast.error('خطأ في تحميل البيانات');
      
      // بيانات تجريبية
      setCategories([
        { id: 1, name: 'البرمجة', description: 'كورسات البرمجة', color: '#4169E1' },
        { id: 2, name: 'التصميم', description: 'كورسات التصميم', color: '#FF6B35' }
      ]);
      
      setCourses([
        {
          id: 1,
          title: 'أساسيات البرمجة',
          description: 'تعلم أساسيات البرمجة من الصفر',
          level: 'beginner',
          duration: '40 ساعة',
          price: '299',
          course_categories: { name: 'البرمجة', color: '#4169E1' },
          course_videos: [{ count: 25 }],
          course_materials: [{ count: 15 }]
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCourse = async () => {
    try {
      // محاكاة إنشاء كورس جديد
      const newCourse = {
        id: Date.now(),
        ...courseForm,
        course_categories: categories.find(c => c.id === parseInt(courseForm.category_id)),
        course_videos: [{ count: 0 }],
        course_materials: [{ count: 0 }],
        created_at: new Date().toISOString()
      };

      setCourses([newCourse, ...courses]);
      setCourseDialog(false);
      resetCourseForm();
      toast.success('تم إنشاء الكورس بنجاح');

    } catch (error) {
      console.error('خطأ في إنشاء الكورس:', error);
      toast.error('خطأ في إنشاء الكورس');
    }
  };

  const handleCreateCategory = async () => {
    try {
      // محاكاة إنشاء فئة جديدة
      const newCategory = {
        id: Date.now(),
        ...categoryForm
      };

      setCategories([...categories, newCategory]);
      setCategoryDialog(false);
      resetCategoryForm();
      toast.success('تم إنشاء الفئة بنجاح');

    } catch (error) {
      console.error('خطأ في إنشاء الفئة:', error);
      toast.error('خطأ في إنشاء الفئة');
    }
  };

  const handleFileUpload = async (file, type) => {
    try {
      setUploading(true);
      setUploadProgress(0);
      
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}.${fileExt}`;
      const filePath = `${type}s/${fileName}`;
      
      // محاكاة رفع الملف
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast.success(`تم رفع ${type === 'video' ? 'الفيديو' : 'الملف'} بنجاح`);
      setUploadDialog(false);
      loadData();
      
    } catch (error) {
      console.error('خطأ في رفع الملف:', error);
      toast.error('خطأ في رفع الملف');
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const resetCourseForm = () => {
    setCourseForm({
      title: '',
      description: '',
      category_id: '',
      level: 'beginner',
      duration: '',
      price: '',
      thumbnail: null
    });
  };

  const resetCategoryForm = () => {
    setCategoryForm({
      name: '',
      description: '',
      color: '#4169E1'
    });
  };

  // مكون بطاقة الكورس
  const CourseCard = ({ course }) => (
    <Card
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
        }
      }}
    >
      <Box
        sx={{
          height: 200,
          background: `linear-gradient(135deg, ${course.course_categories?.color || '#4169E1'}15 0%, ${course.course_categories?.color || '#4169E1'}05 100%)`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative'
        }}
      >
        <School sx={{ fontSize: 60, color: course.course_categories?.color || '#4169E1', opacity: 0.7 }} />
        <Chip
          label={course.course_categories?.name || 'عام'}
          size="small"
          sx={{
            position: 'absolute',
            top: 12,
            right: 12,
            bgcolor: course.course_categories?.color || '#4169E1',
            color: 'white'
          }}
        />
      </Box>
      
      <CardContent sx={{ flex: 1 }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
          {course.title}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {course.description}
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
          <Chip
            label={course.level === 'beginner' ? 'مبتدئ' : course.level === 'intermediate' ? 'متوسط' : 'متقدم'}
            size="small"
            color={course.level === 'beginner' ? 'success' : course.level === 'intermediate' ? 'warning' : 'error'}
          />
          <Chip label={course.duration} size="small" variant="outlined" />
          <Chip label={`${course.price} ريال`} size="small" color="primary" />
        </Box>
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <VideoLibrary sx={{ fontSize: 16, color: 'text.secondary' }} />
              <Typography variant="caption">
                {course.course_videos?.[0]?.count || 0}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <PictureAsPdf sx={{ fontSize: 16, color: 'text.secondary' }} />
              <Typography variant="caption">
                {course.course_materials?.[0]?.count || 0}
              </Typography>
            </Box>
          </Box>
        </Box>
      </CardContent>
      
      <CardActions>
        <Button
          size="small"
          startIcon={<Edit />}
          onClick={() => {
            setSelectedCourse(course);
            setCourseForm(course);
            setCourseDialog(true);
          }}
        >
          تعديل
        </Button>
        <Button
          size="small"
          startIcon={<Upload />}
          onClick={() => {
            setSelectedCourse(course);
            setUploadDialog(true);
          }}
        >
          رفع محتوى
        </Button>
        <Button
          size="small"
          startIcon={<Visibility />}
          color="primary"
        >
          عرض
        </Button>
      </CardActions>
    </Card>
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          إدارة الكورسات
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<Category />}
            onClick={() => setCategoryDialog(true)}
          >
            إدارة الفئات
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setCourseDialog(true)}
            sx={{
              background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)'
              }
            }}
          >
            إضافة كورس جديد
          </Button>
        </Box>
      </Box>

      {/* التبويبات */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={selectedTab}
          onChange={(e, newValue) => setSelectedTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="جميع الكورسات" />
          <Tab label="الكورسات النشطة" />
          <Tab label="الكورسات المسودة" />
        </Tabs>
      </Paper>

      {/* قائمة الكورسات */}
      <Grid container spacing={3}>
        {courses.map((course) => (
          <Grid item xs={12} sm={6} md={4} key={course.id}>
            <CourseCard course={course} />
          </Grid>
        ))}
      </Grid>

      {courses.length === 0 && (
        <Box sx={{ textAlign: 'center', mt: 8 }}>
          <School sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            لا توجد كورسات حالياً
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            ابدأ بإنشاء أول كورس لك
          </Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setCourseDialog(true)}
          >
            إنشاء كورس جديد
          </Button>
        </Box>
      )}

      {/* نافذة إنشاء/تعديل الكورس */}
      <Dialog
        open={courseDialog}
        onClose={() => setCourseDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedCourse ? 'تعديل الكورس' : 'إنشاء كورس جديد'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="عنوان الكورس"
                value={courseForm.title}
                onChange={(e) => setCourseForm({ ...courseForm, title: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="وصف الكورس"
                value={courseForm.description}
                onChange={(e) => setCourseForm({ ...courseForm, description: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>الفئة</InputLabel>
                <Select
                  value={courseForm.category_id}
                  onChange={(e) => setCourseForm({ ...courseForm, category_id: e.target.value })}
                >
                  {categories.map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>المستوى</InputLabel>
                <Select
                  value={courseForm.level}
                  onChange={(e) => setCourseForm({ ...courseForm, level: e.target.value })}
                >
                  <MenuItem value="beginner">مبتدئ</MenuItem>
                  <MenuItem value="intermediate">متوسط</MenuItem>
                  <MenuItem value="advanced">متقدم</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="مدة الكورس"
                value={courseForm.duration}
                onChange={(e) => setCourseForm({ ...courseForm, duration: e.target.value })}
                placeholder="مثال: 40 ساعة"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="السعر (ريال)"
                type="number"
                value={courseForm.price}
                onChange={(e) => setCourseForm({ ...courseForm, price: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCourseDialog(false)}>
            إلغاء
          </Button>
          <Button
            variant="contained"
            onClick={handleCreateCourse}
            disabled={!courseForm.title || !courseForm.category_id}
          >
            {selectedCourse ? 'تحديث' : 'إنشاء'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* نافذة إدارة الفئات */}
      <Dialog
        open={categoryDialog}
        onClose={() => setCategoryDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>إدارة فئات الكورسات</DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 3 }}>
            <TextField
              fullWidth
              label="اسم الفئة"
              value={categoryForm.name}
              onChange={(e) => setCategoryForm({ ...categoryForm, name: e.target.value })}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="وصف الفئة"
              value={categoryForm.description}
              onChange={(e) => setCategoryForm({ ...categoryForm, description: e.target.value })}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="لون الفئة"
              type="color"
              value={categoryForm.color}
              onChange={(e) => setCategoryForm({ ...categoryForm, color: e.target.value })}
            />
            <Button
              variant="contained"
              onClick={handleCreateCategory}
              disabled={!categoryForm.name}
              sx={{ mt: 2 }}
            >
              إضافة فئة
            </Button>
          </Box>
          
          <Divider sx={{ mb: 2 }} />
          
          <List>
            {categories.map((category) => (
              <ListItem key={category.id}>
                <Box
                  sx={{
                    width: 20,
                    height: 20,
                    borderRadius: '50%',
                    bgcolor: category.color,
                    mr: 2
                  }}
                />
                <ListItemText
                  primary={category.name}
                  secondary={category.description}
                />
                <ListItemSecondaryAction>
                  <IconButton edge="end">
                    <Delete />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCategoryDialog(false)}>
            إغلاق
          </Button>
        </DialogActions>
      </Dialog>

      {/* نافذة رفع المحتوى */}
      <Dialog
        open={uploadDialog}
        onClose={() => setUploadDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>رفع محتوى الكورس</DialogTitle>
        <DialogContent>
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <CloudUpload sx={{ fontSize: 80, color: 'primary.main', mb: 2 }} />
            <Typography variant="h6" sx={{ mb: 2 }}>
              اختر نوع المحتوى
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
              <Button
                variant="outlined"
                startIcon={<VideoLibrary />}
                component="label"
                disabled={uploading}
              >
                رفع فيديو
                <input
                  type="file"
                  hidden
                  accept="video/*"
                  onChange={(e) => {
                    if (e.target.files[0]) {
                      handleFileUpload(e.target.files[0], 'video');
                    }
                  }}
                />
              </Button>
              
              <Button
                variant="outlined"
                startIcon={<PictureAsPdf />}
                component="label"
                disabled={uploading}
              >
                رفع PDF
                <input
                  type="file"
                  hidden
                  accept=".pdf"
                  onChange={(e) => {
                    if (e.target.files[0]) {
                      handleFileUpload(e.target.files[0], 'material');
                    }
                  }}
                />
              </Button>
            </Box>
            
            {uploading && (
              <Box sx={{ mt: 3 }}>
                <LinearProgress
                  variant="determinate"
                  value={uploadProgress}
                  sx={{ mb: 1 }}
                />
                <Typography variant="body2">
                  جاري الرفع... {Math.round(uploadProgress)}%
                </Typography>
              </Box>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialog(false)} disabled={uploading}>
            إغلاق
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CourseManagement;
