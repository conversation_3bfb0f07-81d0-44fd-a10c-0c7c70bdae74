/* إصلاحات تخطيط لوحة التحكم الإدارية */
/* Admin Dashboard Layout Fixes */

/* ===== إصلاحات الشريط العلوي ===== */
.admin-dashboard .MuiAppBar-root {
  /* تثبيت الموضع في أعلى الشاشة */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  
  /* رفع z-index ليكون فوق جميع العناصر */
  z-index: 1300 !important;
  
  /* ضمان الامتداد الكامل */
  margin: 0 !important;
  padding: 0 !important;
  
  /* تحسين الظهور */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  background-color: #0000FF !important;
}

/* إصلاح شريط الأدوات داخل AppBar */
.admin-dashboard .MuiAppBar-root .MuiToolbar-root {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding-left: 16px !important;
  padding-right: 16px !important;
  justify-content: space-between !important;
  align-items: center !important;
}

/* ===== إصلاحات الدرج الجانبي ===== */
.admin-dashboard .MuiDrawer-paper,
.admin-dashboard nav > div {
  /* موضع الدرج تحت الشريط العلوي */
  position: fixed !important;
  top: 64px !important; /* ارتفاع الشريط العلوي */
  right: 0 !important;
  height: calc(100vh - 64px) !important;
  
  /* z-index أقل من الشريط العلوي */
  z-index: 1200 !important;
  
  /* تحسين الظهور */
  border-left: 1px solid #e0e0e0 !important;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1) !important;
}

/* إصلاحات للأجهزة اللوحية الكبيرة */
@media (min-width: 1024px) {
  .admin-dashboard .MuiAppBar-root {
    height: 68px !important;
  }
  
  .admin-dashboard .MuiDrawer-paper,
  .admin-dashboard nav > div {
    top: 68px !important;
    height: calc(100vh - 68px) !important;
    width: 320px !important;
  }
  
  .admin-dashboard main {
    margin-top: 68px !important;
    padding-top: 0 !important;
  }
}

/* ===== إصلاحات المحتوى الرئيسي ===== */
.admin-dashboard main {
  /* موضع المحتوى */
  position: relative !important;
  margin-top: 64px !important;
  padding-top: 0 !important;
  
  /* تحديد العرض والهوامش */
  margin-right: 300px !important;
  margin-left: 0 !important;
  width: calc(100% - 300px) !important;
  
  /* ارتفاع كامل */
  min-height: calc(100vh - 64px) !important;
  
  /* تحسين التمرير */
  overflow-x: hidden !important;
  overflow-y: auto !important;
  
  /* خلفية */
  background-color: #f8f9fa !important;
}

/* إصلاحات للشاشات الصغيرة */
@media (max-width: 767px) {
  .admin-dashboard .MuiDrawer-paper,
  .admin-dashboard nav > div {
    top: 56px !important;
    height: calc(100vh - 56px) !important;
  }
  
  .admin-dashboard main {
    margin-top: 56px !important;
    margin-right: 0 !important;
    width: 100% !important;
  }
  
  .admin-dashboard .MuiAppBar-root {
    height: 56px !important;
  }
}

/* ===== إصلاحات RTL ===== */
[dir="rtl"] .admin-dashboard .MuiAppBar-root {
  left: 0 !important;
  right: 0 !important;
}

[dir="rtl"] .admin-dashboard .MuiDrawer-paper,
[dir="rtl"] .admin-dashboard nav > div {
  right: 0 !important;
  left: auto !important;
}

[dir="rtl"] .admin-dashboard main {
  margin-right: 300px !important;
  margin-left: 0 !important;
}

/* ===== إصلاحات LTR ===== */
[dir="ltr"] .admin-dashboard .MuiDrawer-paper,
[dir="ltr"] .admin-dashboard nav > div {
  left: 0 !important;
  right: auto !important;
}

[dir="ltr"] .admin-dashboard main {
  margin-left: 300px !important;
  margin-right: 0 !important;
}

/* ===== إصلاحات عامة للتخطيط ===== */
.admin-dashboard {
  /* تخطيط flex */
  display: flex !important;
  flex-direction: row !important;
  
  /* ارتفاع كامل */
  min-height: 100vh !important;
  height: 100vh !important;
  
  /* عرض كامل */
  width: 100% !important;
  max-width: 100vw !important;
  
  /* إزالة التمرير الأفقي */
  overflow-x: hidden !important;
  
  /* موضع نسبي */
  position: relative !important;
}

/* إصلاح حاوي المحتوى */
.admin-dashboard .MuiContainer-root {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 24px !important;
}

/* إصلاح عنوان الصفحة في الشريط العلوي */
.admin-dashboard .MuiAppBar-root .MuiTypography-h6 {
  flex-grow: 1 !important;
  text-align: center !important;
  margin: 0 16px !important;
  color: white !important;
  font-weight: 600 !important;
}

/* إصلاح أزرار الشريط العلوي */
.admin-dashboard .MuiAppBar-root .MuiIconButton-root {
  color: white !important;
  padding: 12px !important;
  margin: 0 4px !important;
}

/* إصلاح زر تسجيل الخروج */
.admin-dashboard .MuiAppBar-root .MuiIconButton-root:last-child {
  color: #FFD700 !important;
}

/* إصلاح قائمة الدرج الجانبي */
.admin-dashboard .MuiList-root {
  padding: 8px !important;
}

.admin-dashboard .MuiListItem-root {
  margin-bottom: 4px !important;
  border-radius: 8px !important;
}

.admin-dashboard .MuiListItemButton-root {
  border-radius: 8px !important;
  padding: 12px 16px !important;
  min-height: 48px !important;
}

/* إصلاح رأس الدرج الجانبي */
.admin-dashboard nav .MuiBox-root:first-child {
  padding: 24px !important;
  background: linear-gradient(135deg, #0000FF 0%, #4169E1 100%) !important;
  color: white !important;
  text-align: center !important;
}

/* إصلاح معلومات المستخدم في الدرج */
.admin-dashboard nav .MuiBox-root:last-child {
  padding: 16px !important;
  border-top: 1px solid #e0e0e0 !important;
  margin-top: auto !important;
}

/* إصلاح التمرير في الدرج */
.admin-dashboard nav {
  overflow-y: auto !important;
  -webkit-overflow-scrolling: touch !important;
}

/* إصلاح للتأكد من عدم تداخل العناصر */
.admin-dashboard * {
  box-sizing: border-box !important;
}

/* إصلاح خاص للتأكد من ظهور المحتوى بشكل صحيح */
.admin-dashboard main > div {
  width: 100% !important;
  max-width: 100% !important;
}

/* إصلاح نهائي للتخطيط */
@media screen {
  .admin-dashboard {
    position: relative !important;
    overflow: hidden !important;
  }
  
  .admin-dashboard .MuiAppBar-root {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100vw !important;
    z-index: 1300 !important;
  }
  
  .admin-dashboard nav {
    position: fixed !important;
    z-index: 1200 !important;
  }
  
  .admin-dashboard main {
    position: relative !important;
    z-index: 1100 !important;
  }
}
