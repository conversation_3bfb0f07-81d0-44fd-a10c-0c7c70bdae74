import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Button,
  TextField,
  Avatar,
  Divider,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Switch,
  FormControlLabel,
  Paper,
  Tab,
  Tabs
} from '@mui/material';
import {
  AccountCircle,
  Edit,
  Save,
  Cancel,
  Security,
  Notifications,
  Language,
  Visibility,
  VisibilityOff,
  PhotoCamera,
  Email,
  Phone,
  LocationOn,
  CalendarToday,
  Shield,
  Key,
  Settings,
  History,
  Lock
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import toast from 'react-hot-toast';

const AdminProfile = () => {
  const { user, updateUser } = useAuth();
  const { language, t } = useLanguage();
  
  const [loading, setLoading] = useState(false);
  const [editing, setEditing] = useState(false);
  const [selectedTab, setSelectedTab] = useState(0);
  
  // حالات النوافذ المنبثقة
  const [passwordDialog, setPasswordDialog] = useState(false);
  const [securityDialog, setSecurityDialog] = useState(false);
  
  // حالات النماذج
  const [profileForm, setProfileForm] = useState({
    name: '',
    email: '',
    phone: '',
    bio: '',
    location: '',
    avatar: null
  });
  
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });
  
  const [settings, setSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    twoFactorAuth: false,
    autoLogout: true,
    darkMode: false
  });

  const [activityLog, setActivityLog] = useState([]);

  useEffect(() => {
    loadProfileData();
    loadActivityLog();
  }, [user]);

  const loadProfileData = () => {
    if (user) {
      setProfileForm({
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || '',
        bio: user.bio || '',
        location: user.location || '',
        avatar: user.avatar || null
      });
    }
  };

  const loadActivityLog = () => {
    // محاكاة تحميل سجل النشاط
    setActivityLog([
      {
        id: 1,
        action: 'تسجيل دخول',
        timestamp: new Date().toISOString(),
        ip: '***********',
        device: 'Chrome on Windows'
      },
      {
        id: 2,
        action: 'تحديث الملف الشخصي',
        timestamp: new Date(Date.now() - 86400000).toISOString(),
        ip: '***********',
        device: 'Chrome on Windows'
      },
      {
        id: 3,
        action: 'إنشاء كورس جديد',
        timestamp: new Date(Date.now() - 172800000).toISOString(),
        ip: '***********',
        device: 'Chrome on Windows'
      }
    ]);
  };

  const handleSaveProfile = async () => {
    try {
      setLoading(true);
      
      // تحديث بيانات المستخدم
      await updateUser(profileForm);
      
      setEditing(false);
      toast.success('تم تحديث الملف الشخصي بنجاح');
      
    } catch (error) {
      console.error('خطأ في تحديث الملف الشخصي:', error);
      toast.error('خطأ في تحديث الملف الشخصي');
    } finally {
      setLoading(false);
    }
  };

  const handleChangePassword = async () => {
    try {
      if (passwordForm.newPassword !== passwordForm.confirmPassword) {
        toast.error('كلمة المرور الجديدة غير متطابقة');
        return;
      }
      
      if (passwordForm.newPassword.length < 6) {
        toast.error('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        return;
      }
      
      setLoading(true);
      
      // محاكاة تغيير كلمة المرور
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setPasswordDialog(false);
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      toast.success('تم تغيير كلمة المرور بنجاح');
      
    } catch (error) {
      console.error('خطأ في تغيير كلمة المرور:', error);
      toast.error('خطأ في تغيير كلمة المرور');
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfileForm({ ...profileForm, avatar: e.target.result });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSettingChange = (setting) => (event) => {
    setSettings({
      ...settings,
      [setting]: event.target.checked
    });
  };

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          الملف الشخصي
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          {editing ? (
            <>
              <Button
                variant="outlined"
                startIcon={<Cancel />}
                onClick={() => {
                  setEditing(false);
                  loadProfileData();
                }}
              >
                إلغاء
              </Button>
              <Button
                variant="contained"
                startIcon={<Save />}
                onClick={handleSaveProfile}
                disabled={loading}
              >
                حفظ التغييرات
              </Button>
            </>
          ) : (
            <Button
              variant="contained"
              startIcon={<Edit />}
              onClick={() => setEditing(true)}
              sx={{
                background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)'
                }
              }}
            >
              تعديل الملف الشخصي
            </Button>
          )}
        </Box>
      </Box>

      {/* التبويبات */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={selectedTab}
          onChange={(e, newValue) => setSelectedTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="المعلومات الشخصية" />
          <Tab label="الأمان" />
          <Tab label="الإعدادات" />
          <Tab label="سجل النشاط" />
        </Tabs>
      </Paper>

      {/* محتوى التبويبات */}
      {selectedTab === 0 && (
        <Grid container spacing={3}>
          {/* بطاقة الصورة الشخصية */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent sx={{ textAlign: 'center', p: 4 }}>
                <Box sx={{ position: 'relative', display: 'inline-block', mb: 3 }}>
                  <Avatar
                    src={profileForm.avatar}
                    sx={{
                      width: 120,
                      height: 120,
                      fontSize: '3rem',
                      bgcolor: 'primary.main'
                    }}
                  >
                    {profileForm.name.charAt(0)}
                  </Avatar>
                  {editing && (
                    <IconButton
                      component="label"
                      sx={{
                        position: 'absolute',
                        bottom: 0,
                        right: 0,
                        bgcolor: 'primary.main',
                        color: 'white',
                        '&:hover': { bgcolor: 'primary.dark' }
                      }}
                    >
                      <PhotoCamera />
                      <input
                        type="file"
                        hidden
                        accept="image/*"
                        onChange={handleAvatarChange}
                      />
                    </IconButton>
                  )}
                </Box>
                
                <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1 }}>
                  {profileForm.name || 'المدير'}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  مدير الأكاديمية
                </Typography>
                
                <Chip
                  label="نشط"
                  color="success"
                  size="small"
                  icon={<Shield />}
                />
              </CardContent>
            </Card>
          </Grid>

          {/* بطاقة المعلومات الشخصية */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                  المعلومات الشخصية
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="الاسم الكامل"
                      value={profileForm.name}
                      onChange={(e) => setProfileForm({ ...profileForm, name: e.target.value })}
                      disabled={!editing}
                      InputProps={{
                        startAdornment: <AccountCircle sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="البريد الإلكتروني"
                      type="email"
                      value={profileForm.email}
                      onChange={(e) => setProfileForm({ ...profileForm, email: e.target.value })}
                      disabled={!editing}
                      InputProps={{
                        startAdornment: <Email sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="رقم الهاتف"
                      value={profileForm.phone}
                      onChange={(e) => setProfileForm({ ...profileForm, phone: e.target.value })}
                      disabled={!editing}
                      InputProps={{
                        startAdornment: <Phone sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="الموقع"
                      value={profileForm.location}
                      onChange={(e) => setProfileForm({ ...profileForm, location: e.target.value })}
                      disabled={!editing}
                      InputProps={{
                        startAdornment: <LocationOn sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>
                  
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      multiline
                      rows={3}
                      label="نبذة شخصية"
                      value={profileForm.bio}
                      onChange={(e) => setProfileForm({ ...profileForm, bio: e.target.value })}
                      disabled={!editing}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {selectedTab === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                  إعدادات الأمان
                </Typography>
                
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <Key />
                    </ListItemIcon>
                    <ListItemText
                      primary="تغيير كلمة المرور"
                      secondary="آخر تغيير منذ 30 يوماً"
                    />
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setPasswordDialog(true)}
                    >
                      تغيير
                    </Button>
                  </ListItem>
                  
                  <Divider />
                  
                  <ListItem>
                    <ListItemIcon>
                      <Shield />
                    </ListItemIcon>
                    <ListItemText
                      primary="المصادقة الثنائية"
                      secondary="حماية إضافية لحسابك"
                    />
                    <Switch
                      checked={settings.twoFactorAuth}
                      onChange={handleSettingChange('twoFactorAuth')}
                    />
                  </ListItem>
                  
                  <Divider />
                  
                  <ListItem>
                    <ListItemIcon>
                      <Lock />
                    </ListItemIcon>
                    <ListItemText
                      primary="تسجيل الخروج التلقائي"
                      secondary="بعد 30 دقيقة من عدم النشاط"
                    />
                    <Switch
                      checked={settings.autoLogout}
                      onChange={handleSettingChange('autoLogout')}
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                  معلومات الحساب
                </Typography>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    تاريخ إنشاء الحساب
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    15 يناير 2024
                  </Typography>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    آخر تسجيل دخول
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    اليوم في 10:30 صباحاً
                  </Typography>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    عدد مرات تسجيل الدخول
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    247 مرة
                  </Typography>
                </Box>
                
                <Alert severity="info" sx={{ mt: 2 }}>
                  حسابك محمي بأحدث معايير الأمان
                </Alert>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {selectedTab === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                  إعدادات الإشعارات
                </Typography>
                
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <Email />
                    </ListItemIcon>
                    <ListItemText
                      primary="إشعارات البريد الإلكتروني"
                      secondary="تلقي إشعارات عبر البريد الإلكتروني"
                    />
                    <Switch
                      checked={settings.emailNotifications}
                      onChange={handleSettingChange('emailNotifications')}
                    />
                  </ListItem>
                  
                  <Divider />
                  
                  <ListItem>
                    <ListItemIcon>
                      <Phone />
                    </ListItemIcon>
                    <ListItemText
                      primary="إشعارات الرسائل النصية"
                      secondary="تلقي إشعارات عبر الرسائل النصية"
                    />
                    <Switch
                      checked={settings.smsNotifications}
                      onChange={handleSettingChange('smsNotifications')}
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                  إعدادات العرض
                </Typography>
                
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <Language />
                    </ListItemIcon>
                    <ListItemText
                      primary="اللغة"
                      secondary="العربية"
                    />
                    <Button variant="outlined" size="small">
                      تغيير
                    </Button>
                  </ListItem>
                  
                  <Divider />
                  
                  <ListItem>
                    <ListItemIcon>
                      <Settings />
                    </ListItemIcon>
                    <ListItemText
                      primary="الوضع الليلي"
                      secondary="تفعيل الوضع الليلي"
                    />
                    <Switch
                      checked={settings.darkMode}
                      onChange={handleSettingChange('darkMode')}
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {selectedTab === 3 && (
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
              سجل النشاط
            </Typography>
            
            <List>
              {activityLog.map((activity, index) => (
                <React.Fragment key={activity.id}>
                  <ListItem>
                    <ListItemIcon>
                      <History />
                    </ListItemIcon>
                    <ListItemText
                      primary={activity.action}
                      secondary={
                        <Box>
                          <Typography variant="caption" display="block">
                            {new Date(activity.timestamp).toLocaleString('ar')}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {activity.device} - {activity.ip}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < activityLog.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      )}

      {/* نافذة تغيير كلمة المرور */}
      <Dialog
        open={passwordDialog}
        onClose={() => setPasswordDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>تغيير كلمة المرور</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="كلمة المرور الحالية"
                type={showPasswords.current ? 'text' : 'password'}
                value={passwordForm.currentPassword}
                onChange={(e) => setPasswordForm({ ...passwordForm, currentPassword: e.target.value })}
                InputProps={{
                  endAdornment: (
                    <IconButton
                      onClick={() => setShowPasswords({ ...showPasswords, current: !showPasswords.current })}
                    >
                      {showPasswords.current ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  )
                }}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="كلمة المرور الجديدة"
                type={showPasswords.new ? 'text' : 'password'}
                value={passwordForm.newPassword}
                onChange={(e) => setPasswordForm({ ...passwordForm, newPassword: e.target.value })}
                InputProps={{
                  endAdornment: (
                    <IconButton
                      onClick={() => setShowPasswords({ ...showPasswords, new: !showPasswords.new })}
                    >
                      {showPasswords.new ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  )
                }}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="تأكيد كلمة المرور الجديدة"
                type={showPasswords.confirm ? 'text' : 'password'}
                value={passwordForm.confirmPassword}
                onChange={(e) => setPasswordForm({ ...passwordForm, confirmPassword: e.target.value })}
                InputProps={{
                  endAdornment: (
                    <IconButton
                      onClick={() => setShowPasswords({ ...showPasswords, confirm: !showPasswords.confirm })}
                    >
                      {showPasswords.confirm ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  )
                }}
              />
            </Grid>
          </Grid>
          
          <Alert severity="info" sx={{ mt: 2 }}>
            كلمة المرور يجب أن تكون 6 أحرف على الأقل وتحتوي على أرقام وحروف
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPasswordDialog(false)}>
            إلغاء
          </Button>
          <Button
            variant="contained"
            onClick={handleChangePassword}
            disabled={loading || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword}
          >
            {loading ? <CircularProgress size={20} /> : 'تغيير كلمة المرور'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdminProfile;
