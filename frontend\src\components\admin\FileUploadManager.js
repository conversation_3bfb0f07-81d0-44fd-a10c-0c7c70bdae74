import { useState, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  LinearProgress,
  Alert,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Paper,
  Tooltip,
  Fade,
  CircularProgress
} from '@mui/material';
import {
  CloudUpload,
  VideoFile,
  PictureAsPdf,
  Delete,
  Preview,
  Download,
  CheckCircle,
  Error as ErrorIcon,
  Info,
  Folder,
  InsertDriveFile
} from '@mui/icons-material';
import { useLanguage } from '../../contexts/LanguageContext';

/**
 * مكون إدارة رفع الملفات المتقدم
 */
const FileUploadManager = () => {
  const { language } = useLanguage();
  
  // حالات المكون
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [previewFile, setPreviewFile] = useState(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  
  // مراجع
  const fileInputRef = useRef(null);
  
  // أنواع الملفات المدعومة
  const supportedTypes = {
    video: {
      extensions: ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'],
      mimeTypes: ['video/mp4', 'video/avi', 'video/quicktime', 'video/x-ms-wmv', 'video/x-flv', 'video/webm'],
      maxSize: 500 * 1024 * 1024, // 500MB
      icon: <VideoFile />,
      color: '#f44336'
    },
    pdf: {
      extensions: ['.pdf'],
      mimeTypes: ['application/pdf'],
      maxSize: 50 * 1024 * 1024, // 50MB
      icon: <PictureAsPdf />,
      color: '#ff5722'
    }
  };
  
  // التحقق من نوع الملف
  const getFileType = (file) => {
    const extension = '.' + file.name.split('.').pop().toLowerCase();
    
    if (supportedTypes.video.extensions.includes(extension)) {
      return 'video';
    } else if (supportedTypes.pdf.extensions.includes(extension)) {
      return 'pdf';
    }
    return null;
  };
  
  // التحقق من صحة الملف
  const validateFile = (file) => {
    const fileType = getFileType(file);
    
    if (!fileType) {
      return {
        valid: false,
        error: language === 'ar' 
          ? 'نوع الملف غير مدعوم. يرجى رفع ملفات فيديو أو PDF فقط.'
          : 'Unsupported file type. Please upload video or PDF files only.'
      };
    }
    
    const typeConfig = supportedTypes[fileType];
    
    if (file.size > typeConfig.maxSize) {
      const maxSizeMB = typeConfig.maxSize / (1024 * 1024);
      return {
        valid: false,
        error: language === 'ar'
          ? `حجم الملف كبير جداً. الحد الأقصى ${maxSizeMB}MB`
          : `File size too large. Maximum ${maxSizeMB}MB allowed`
      };
    }
    
    return { valid: true, type: fileType };
  };
  
  // معالج رفع الملفات
  const handleFileUpload = useCallback(async (files) => {
    setError(null);
    setSuccess(null);
    
    const fileArray = Array.from(files);
    const validFiles = [];
    
    // التحقق من جميع الملفات
    for (const file of fileArray) {
      const validation = validateFile(file);
      if (!validation.valid) {
        setError(validation.error);
        return;
      }
      validFiles.push({ file, type: validation.type });
    }
    
    // رفع الملفات
    setUploading(true);
    setUploadProgress(0);
    
    try {
      for (let i = 0; i < validFiles.length; i++) {
        const { file, type } = validFiles[i];
        
        // محاكاة رفع الملف
        await simulateFileUpload(file, type, (progress) => {
          const totalProgress = ((i / validFiles.length) + (progress / validFiles.length)) * 100;
          setUploadProgress(totalProgress);
        });
        
        // إضافة الملف إلى القائمة
        const newFile = {
          id: Date.now() + i,
          name: file.name,
          size: file.size,
          type: type,
          uploadDate: new Date(),
          url: URL.createObjectURL(file), // في التطبيق الحقيقي، سيكون هذا رابط من الخادم
          status: 'uploaded'
        };
        
        setUploadedFiles(prev => [...prev, newFile]);
      }
      
      setSuccess(
        language === 'ar'
          ? `تم رفع ${validFiles.length} ملف بنجاح`
          : `Successfully uploaded ${validFiles.length} file(s)`
      );
      
    } catch (error) {
      setError(
        language === 'ar'
          ? 'حدث خطأ أثناء رفع الملفات'
          : 'An error occurred while uploading files'
      );
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  }, [language]);
  
  // محاكاة رفع الملف
  const simulateFileUpload = (file, type, onProgress) => {
    return new Promise((resolve) => {
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress >= 100) {
          progress = 100;
          clearInterval(interval);
          resolve();
        }
        onProgress(progress);
      }, 200);
    });
  };
  
  // معالج السحب والإفلات
  const handleDrop = useCallback((e) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files);
    }
  }, [handleFileUpload]);
  
  const handleDragOver = useCallback((e) => {
    e.preventDefault();
  }, []);
  
  // معالج حذف الملف
  const handleDeleteFile = (fileId) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
    setSuccess(
      language === 'ar'
        ? 'تم حذف الملف بنجاح'
        : 'File deleted successfully'
    );
  };
  
  // معالج معاينة الملف
  const handlePreviewFile = (file) => {
    setPreviewFile(file);
    setPreviewOpen(true);
  };
  
  // تنسيق حجم الملف
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  return (
    <Box className="modern-fade-in">
      <Typography 
        variant="h4" 
        className="arabic-heading arabic-heading-2 arabic-text-gradient"
        sx={{ mb: 3 }}
      >
        {language === 'ar' ? 'إدارة رفع الملفات' : 'File Upload Manager'}
      </Typography>
      
      {/* منطقة رفع الملفات */}
      <Card className="modern-card" sx={{ mb: 3 }}>
        <CardContent>
          <Box
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            sx={{
              border: '2px dashed #667eea',
              borderRadius: 2,
              p: 4,
              textAlign: 'center',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#764ba2',
                backgroundColor: 'rgba(102, 126, 234, 0.05)'
              }
            }}
            onClick={() => fileInputRef.current?.click()}
          >
            <CloudUpload sx={{ fontSize: 64, color: '#667eea', mb: 2 }} />
            <Typography 
              variant="h6" 
              className="arabic-text-semibold"
              sx={{ mb: 1 }}
            >
              {language === 'ar' 
                ? 'اسحب الملفات هنا أو انقر للاختيار'
                : 'Drag files here or click to select'
              }
            </Typography>
            <Typography 
              variant="body2" 
              className="arabic-text-secondary"
              sx={{ mb: 2 }}
            >
              {language === 'ar'
                ? 'ملفات الفيديو (MP4, AVI, MOV) وملفات PDF مدعومة'
                : 'Video files (MP4, AVI, MOV) and PDF files supported'
              }
            </Typography>
            
            {/* أحجام الملفات المدعومة */}
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, flexWrap: 'wrap' }}>
              <Chip
                icon={<VideoFile />}
                label={language === 'ar' ? 'فيديو: حتى 500MB' : 'Video: up to 500MB'}
                variant="outlined"
                color="primary"
              />
              <Chip
                icon={<PictureAsPdf />}
                label={language === 'ar' ? 'PDF: حتى 50MB' : 'PDF: up to 50MB'}
                variant="outlined"
                color="secondary"
              />
            </Box>
          </Box>
          
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept=".mp4,.avi,.mov,.wmv,.flv,.webm,.pdf"
            style={{ display: 'none' }}
            onChange={(e) => handleFileUpload(e.target.files)}
          />
          
          {/* شريط التقدم */}
          {uploading && (
            <Box sx={{ mt: 3 }}>
              <Typography variant="body2" sx={{ mb: 1 }}>
                {language === 'ar' ? 'جاري الرفع...' : 'Uploading...'}
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={uploadProgress}
                sx={{ height: 8, borderRadius: 4 }}
              />
              <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
                {Math.round(uploadProgress)}%
              </Typography>
            </Box>
          )}
          
          {/* رسائل النجاح والخطأ */}
          {error && (
            <Alert severity="error" sx={{ mt: 2 }} className="modern-notification-error">
              {error}
            </Alert>
          )}
          
          {success && (
            <Alert severity="success" sx={{ mt: 2 }} className="modern-notification-success">
              {success}
            </Alert>
          )}
        </CardContent>
      </Card>
      
      {/* قائمة الملفات المرفوعة */}
      {uploadedFiles.length > 0 && (
        <Card className="modern-card">
          <CardContent>
            <Typography 
              variant="h6" 
              className="arabic-text-semibold"
              sx={{ mb: 2 }}
            >
              {language === 'ar' ? 'الملفات المرفوعة' : 'Uploaded Files'}
              <Chip 
                label={uploadedFiles.length} 
                size="small" 
                color="primary" 
                sx={{ ml: 1 }}
              />
            </Typography>
            
            <List>
              {uploadedFiles.map((file, index) => (
                <Fade key={file.id} in={true} timeout={300 + (index * 100)}>
                  <Box>
                    <ListItem className="modern-list-item">
                      <ListItemIcon>
                        {supportedTypes[file.type].icon}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography className="arabic-text-medium">
                            {file.name}
                          </Typography>
                        }
                        secondary={
                          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center', mt: 1 }}>
                            <Chip
                              label={file.type.toUpperCase()}
                              size="small"
                              sx={{ 
                                backgroundColor: supportedTypes[file.type].color,
                                color: 'white'
                              }}
                            />
                            <Typography variant="caption">
                              {formatFileSize(file.size)}
                            </Typography>
                            <Typography variant="caption">
                              {file.uploadDate.toLocaleDateString()}
                            </Typography>
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title={language === 'ar' ? 'معاينة' : 'Preview'}>
                            <IconButton
                              size="small"
                              onClick={() => handlePreviewFile(file)}
                              className="modern-button"
                            >
                              <Preview />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title={language === 'ar' ? 'تحميل' : 'Download'}>
                            <IconButton
                              size="small"
                              href={file.url}
                              download={file.name}
                              className="modern-button"
                            >
                              <Download />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title={language === 'ar' ? 'حذف' : 'Delete'}>
                            <IconButton
                              size="small"
                              onClick={() => handleDeleteFile(file.id)}
                              color="error"
                            >
                              <Delete />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < uploadedFiles.length - 1 && <Divider />}
                  </Box>
                </Fade>
              ))}
            </List>
          </CardContent>
        </Card>
      )}
      
      {/* نافذة معاينة الملف */}
      <Dialog
        open={previewOpen}
        onClose={() => setPreviewOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle className="arabic-text-semibold">
          {language === 'ar' ? 'معاينة الملف' : 'File Preview'}
        </DialogTitle>
        <DialogContent>
          {previewFile && (
            <Box sx={{ textAlign: 'center' }}>
              {previewFile.type === 'video' ? (
                <video
                  controls
                  style={{ width: '100%', maxHeight: '400px' }}
                  src={previewFile.url}
                >
                  {language === 'ar' 
                    ? 'متصفحك لا يدعم تشغيل الفيديو'
                    : 'Your browser does not support video playback'
                  }
                </video>
              ) : (
                <iframe
                  src={previewFile.url}
                  style={{ width: '100%', height: '500px', border: 'none' }}
                  title="PDF Preview"
                />
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewOpen(false)} className="modern-button">
            {language === 'ar' ? 'إغلاق' : 'Close'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FileUploadManager;
