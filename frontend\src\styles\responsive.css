/* تحسينات التجاوب لمشروع Skills World Academy */
/* Responsive improvements for Skills World Academy */

/* ===== متغيرات CSS للتجاوب ===== */
:root {
  /* نقاط التوقف */
  --mobile-max: 767px;
  --tablet-min: 768px;
  --tablet-max: 1024px;
  --laptop-min: 1025px;
  --laptop-max: 1440px;
  --desktop-min: 1441px;

  /* أحجام الخطوط المتجاوبة */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;

  /* المسافات المتجاوبة */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* أحجام اللمس */
  --touch-target-mobile: 44px;
  --touch-target-tablet: 48px;
  --touch-target-desktop: 40px;

  /* عرض الدرج الجانبي */
  --drawer-width-mobile: 280px;
  --drawer-width-tablet: 300px;
  --drawer-width-laptop: 320px;
  --drawer-width-desktop: 340px;

  /* ارتفاع الشريط العلوي */
  --appbar-height-mobile: 56px;
  --appbar-height-tablet: 64px;
  --appbar-height-desktop: 68px;
}

/* ===== تحسينات الخطوط العربية ===== */
.arabic-text {
  font-family: 'Cairo', 'Tajawal', 'Amiri', 'Segoe UI', 'Roboto', sans-serif;
  font-weight: 400;
  line-height: 1.7;
  letter-spacing: 0.02em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  word-spacing: 0.1em;
  direction: rtl;
  text-align: right;
}

.arabic-text-bold {
  font-weight: 600;
}

.arabic-text-light {
  font-weight: 300;
}

/* تحسين التباين للنصوص */
.high-contrast-text {
  color: #1a1a1a;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
}

.medium-contrast-text {
  color: #4a4a4a;
}

.low-contrast-text {
  color: #757575;
}

/* ===== تحسينات RTL ===== */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .MuiDrawer-paper {
  right: 0;
  left: auto;
}

[dir="rtl"] .MuiAppBar-root {
  right: 0;
  left: auto;
}

[dir="rtl"] .MuiListItemIcon-root {
  margin-left: 16px;
  margin-right: 0;
  min-width: auto;
}

[dir="rtl"] .MuiListItemText-root {
  text-align: right;
}

/* ===== تحسينات الأجهزة المحمولة ===== */
@media (max-width: 767px) {
  /* الخطوط */
  .responsive-text-xs { font-size: var(--font-size-xs); }
  .responsive-text-sm { font-size: var(--font-size-sm); }
  .responsive-text-base { font-size: 0.9rem; }
  .responsive-text-lg { font-size: var(--font-size-base); }
  .responsive-text-xl { font-size: var(--font-size-lg); }

  /* المسافات */
  .responsive-spacing-xs { padding: var(--spacing-xs); }
  .responsive-spacing-sm { padding: var(--spacing-sm); }
  .responsive-spacing-md { padding: var(--spacing-sm); }
  .responsive-spacing-lg { padding: var(--spacing-md); }

  /* أزرار اللمس */
  .touch-target {
    min-width: var(--touch-target-mobile);
    min-height: var(--touch-target-mobile);
    padding: 8px;
  }

  /* تحسينات الأداء للأجهزة المحمولة */
  .mobile-optimized {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* إخفاء العناصر غير الضرورية */
  .hide-on-mobile {
    display: none !important;
  }

  /* تحسين التمرير */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
}

/* ===== تحسينات الأجهزة اللوحية ===== */
@media (min-width: 768px) and (max-width: 1024px) {
  /* الخطوط */
  .responsive-text-xs { font-size: var(--font-size-sm); }
  .responsive-text-sm { font-size: var(--font-size-base); }
  .responsive-text-base { font-size: var(--font-size-base); }
  .responsive-text-lg { font-size: var(--font-size-lg); }
  .responsive-text-xl { font-size: var(--font-size-xl); }

  /* المسافات */
  .responsive-spacing-xs { padding: var(--spacing-sm); }
  .responsive-spacing-sm { padding: var(--spacing-md); }
  .responsive-spacing-md { padding: var(--spacing-md); }
  .responsive-spacing-lg { padding: var(--spacing-lg); }

  /* أزرار اللمس */
  .touch-target {
    min-width: var(--touch-target-tablet);
    min-height: var(--touch-target-tablet);
    padding: 12px;
  }

  /* تحسينات خاصة بالأجهزة اللوحية */
  .tablet-optimized {
    touch-action: manipulation;
    user-select: none;
  }

  /* تحسين التفاعل */
  .tablet-hover:hover {
    background-color: rgba(0, 0, 0, 0.04);
    transition: background-color 0.2s ease;
  }
}

/* ===== تحسينات أجهزة الكمبيوتر المحمولة ===== */
@media (min-width: 1025px) and (max-width: 1440px) {
  /* الخطوط */
  .responsive-text-xs { font-size: var(--font-size-sm); }
  .responsive-text-sm { font-size: var(--font-size-base); }
  .responsive-text-base { font-size: var(--font-size-base); }
  .responsive-text-lg { font-size: var(--font-size-lg); }
  .responsive-text-xl { font-size: var(--font-size-xl); }

  /* المسافات */
  .responsive-spacing-xs { padding: var(--spacing-sm); }
  .responsive-spacing-sm { padding: var(--spacing-md); }
  .responsive-spacing-md { padding: var(--spacing-lg); }
  .responsive-spacing-lg { padding: var(--spacing-xl); }

  /* أزرار اللمس */
  .touch-target {
    min-width: var(--touch-target-desktop);
    min-height: var(--touch-target-desktop);
    padding: 10px;
  }
}

/* ===== تحسينات الشاشات الكبيرة ===== */
@media (min-width: 1441px) {
  /* الخطوط */
  .responsive-text-xs { font-size: var(--font-size-base); }
  .responsive-text-sm { font-size: var(--font-size-lg); }
  .responsive-text-base { font-size: var(--font-size-lg); }
  .responsive-text-lg { font-size: var(--font-size-xl); }
  .responsive-text-xl { font-size: var(--font-size-2xl); }

  /* المسافات */
  .responsive-spacing-xs { padding: var(--spacing-md); }
  .responsive-spacing-sm { padding: var(--spacing-lg); }
  .responsive-spacing-md { padding: var(--spacing-xl); }
  .responsive-spacing-lg { padding: var(--spacing-2xl); }

  /* أزرار اللمس */
  .touch-target {
    min-width: var(--touch-target-desktop);
    min-height: var(--touch-target-desktop);
    padding: 12px;
  }
}

/* ===== تحسينات الأداء العامة ===== */
.performance-optimized {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.no-transition {
  transition: none !important;
}

/* تحسين التمرير */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* ===== تحسينات إمكانية الوصول ===== */
@media (prefers-reduced-motion: reduce) {
  .smooth-transition,
  .performance-optimized {
    transition: none !important;
    animation: none !important;
  }
}

@media (prefers-color-scheme: dark) {
  .high-contrast-text {
    color: #ffffff;
  }
  
  .medium-contrast-text {
    color: #e0e0e0;
  }
  
  .low-contrast-text {
    color: #b0b0b0;
  }
}

/* ===== تحسينات خاصة بالمكونات ===== */

/* تحسين قوائم الأجهزة اللوحية */
.tablet-list-item {
  padding: 12px 16px;
  min-height: 48px;
  border-radius: 8px;
  margin: 4px 8px;
  transition: all 0.2s ease;
}

.tablet-list-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
  transform: translateX(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* تحسين الأزرار للأجهزة اللوحية */
.tablet-button {
  padding: 12px 24px;
  min-height: 48px;
  font-size: 1rem;
  font-weight: 500;
}

/* تحسين النصوص للشاشات الصغيرة */
.mobile-text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

/* تحسين الشبكات المتجاوبة */
.responsive-grid {
  display: grid;
  gap: var(--spacing-md);
}

@media (max-width: 767px) {
  .responsive-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* ===== تحسينات التحميل ===== */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* إيقاف الرسوم المتحركة للأجهزة التي تفضل تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
  .loading-skeleton {
    animation: none;
    background: #f0f0f0;
  }
}
