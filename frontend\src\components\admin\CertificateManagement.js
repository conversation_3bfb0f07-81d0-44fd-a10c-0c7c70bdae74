import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  Avatar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  WorkspacePremium,
  Add,
  Edit,
  Delete,
  Download,
  Send,
  Visibility,
  CheckCircle,
  Pending,
  Error as ErrorIcon,
  Print,
  Email,
  Share,
  Star,
  School,
  Person,
  CalendarToday,
  Refresh
} from '@mui/icons-material';
import { useLanguage } from '../../contexts/LanguageContext';
// import { supabase } from '../../config/supabase';
import toast from 'react-hot-toast';

const CertificateManagement = () => {
  const { language, t } = useLanguage();
  const [loading, setLoading] = useState(true);
  const [certificates, setCertificates] = useState([]);
  const [students, setStudents] = useState([]);
  const [courses, setCourses] = useState([]);
  const [selectedTab, setSelectedTab] = useState(0);
  
  // حالات النوافذ المنبثقة
  const [certificateDialog, setCertificateDialog] = useState(false);
  const [templateDialog, setTemplateDialog] = useState(false);
  const [previewDialog, setPreviewDialog] = useState(false);
  
  // حالات النماذج
  const [certificateForm, setCertificateForm] = useState({
    student_id: '',
    course_id: '',
    completion_date: new Date().toISOString().split('T')[0],
    grade: 'A',
    notes: ''
  });
  
  const [selectedCertificate, setSelectedCertificate] = useState(null);
  
  const [stats, setStats] = useState({
    totalCertificates: 0,
    pendingCertificates: 0,
    issuedThisMonth: 0,
    averageGrade: 'A'
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // محاكاة تحميل البيانات من Supabase
      // في التطبيق الحقيقي، ستكون هذه استعلامات Supabase
      
      // بيانات تجريبية للشهادات
      const certificatesData = [
        {
          id: 1,
          student_name: 'أحمد محمد',
          course_title: 'أساسيات البرمجة',
          completion_date: '2024-01-15',
          issue_date: '2024-01-16',
          grade: 'A',
          status: 'issued',
          certificate_number: 'CERT-2024-001',
          student_id: 1,
          course_id: 1
        },
        {
          id: 2,
          student_name: 'فاطمة علي',
          course_title: 'تطوير المواقع',
          completion_date: '2024-01-20',
          issue_date: null,
          grade: 'B+',
          status: 'pending',
          certificate_number: 'CERT-2024-002',
          student_id: 2,
          course_id: 2
        }
      ];
      
      // بيانات تجريبية للطلاب
      const studentsData = [
        { id: 1, name: 'أحمد محمد', email: '<EMAIL>' },
        { id: 2, name: 'فاطمة علي', email: '<EMAIL>' },
        { id: 3, name: 'محمد سالم', email: '<EMAIL>' }
      ];
      
      // بيانات تجريبية للكورسات
      const coursesData = [
        { id: 1, title: 'أساسيات البرمجة', level: 'beginner' },
        { id: 2, title: 'تطوير المواقع', level: 'intermediate' },
        { id: 3, title: 'قواعد البيانات', level: 'advanced' }
      ];
      
      setCertificates(certificatesData);
      setStudents(studentsData);
      setCourses(coursesData);
      
      // حساب الإحصائيات
      setStats({
        totalCertificates: certificatesData.length,
        pendingCertificates: certificatesData.filter(c => c.status === 'pending').length,
        issuedThisMonth: certificatesData.filter(c => {
          if (!c.issue_date) return false;
          const issueDate = new Date(c.issue_date);
          const now = new Date();
          return issueDate.getMonth() === now.getMonth() && 
                 issueDate.getFullYear() === now.getFullYear();
        }).length,
        averageGrade: 'A-'
      });
      
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      toast.error('خطأ في تحميل البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleIssueCertificate = async () => {
    try {
      const newCertificate = {
        id: Date.now(),
        ...certificateForm,
        student_name: students.find(s => s.id === parseInt(certificateForm.student_id))?.name,
        course_title: courses.find(c => c.id === parseInt(certificateForm.course_id))?.title,
        issue_date: new Date().toISOString().split('T')[0],
        status: 'issued',
        certificate_number: `CERT-${new Date().getFullYear()}-${String(certificates.length + 1).padStart(3, '0')}`
      };
      
      setCertificates([newCertificate, ...certificates]);
      setCertificateDialog(false);
      resetCertificateForm();
      toast.success('تم إصدار الشهادة بنجاح');
      
    } catch (error) {
      console.error('خطأ في إصدار الشهادة:', error);
      toast.error('خطأ في إصدار الشهادة');
    }
  };

  const handleSendCertificate = async (certificate) => {
    try {
      // محاكاة إرسال الشهادة عبر البريد الإلكتروني
      toast.success(`تم إرسال الشهادة إلى ${certificate.student_name} بنجاح`);
    } catch (error) {
      console.error('خطأ في إرسال الشهادة:', error);
      toast.error('خطأ في إرسال الشهادة');
    }
  };

  const resetCertificateForm = () => {
    setCertificateForm({
      student_id: '',
      course_id: '',
      completion_date: new Date().toISOString().split('T')[0],
      grade: 'A',
      notes: ''
    });
  };

  // مكون بطاقة الإحصائيات
  const StatCard = ({ title, value, icon, color, subtitle }) => (
    <Card
      sx={{
        background: `linear-gradient(135deg, ${color}15 0%, ${color}05 100%)`,
        border: `1px solid ${color}30`,
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: `0 8px 25px ${color}25`
        }
      }}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar sx={{ bgcolor: color, mr: 2 }}>
            {icon}
          </Avatar>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 'bold', color }}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
          </Box>
        </Box>
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );

  // مكون صف الشهادة في الجدول
  const CertificateRow = ({ certificate }) => (
    <TableRow>
      <TableCell>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
            <WorkspacePremium />
          </Avatar>
          <Box>
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              {certificate.certificate_number}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {certificate.issue_date || 'لم يتم الإصدار'}
            </Typography>
          </Box>
        </Box>
      </TableCell>
      <TableCell>{certificate.student_name}</TableCell>
      <TableCell>{certificate.course_title}</TableCell>
      <TableCell>
        <Chip
          label={certificate.grade}
          size="small"
          color={
            certificate.grade.startsWith('A') ? 'success' :
            certificate.grade.startsWith('B') ? 'primary' :
            certificate.grade.startsWith('C') ? 'warning' : 'error'
          }
        />
      </TableCell>
      <TableCell>
        <Chip
          label={certificate.status === 'issued' ? 'مُصدرة' : 'في الانتظار'}
          size="small"
          color={certificate.status === 'issued' ? 'success' : 'warning'}
          icon={certificate.status === 'issued' ? <CheckCircle /> : <Pending />}
        />
      </TableCell>
      <TableCell>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="معاينة">
            <IconButton
              size="small"
              onClick={() => {
                setSelectedCertificate(certificate);
                setPreviewDialog(true);
              }}
            >
              <Visibility />
            </IconButton>
          </Tooltip>
          <Tooltip title="تحميل">
            <IconButton size="small">
              <Download />
            </IconButton>
          </Tooltip>
          <Tooltip title="إرسال">
            <IconButton
              size="small"
              onClick={() => handleSendCertificate(certificate)}
              disabled={certificate.status !== 'issued'}
            >
              <Send />
            </IconButton>
          </Tooltip>
          <Tooltip title="طباعة">
            <IconButton size="small">
              <Print />
            </IconButton>
          </Tooltip>
        </Box>
      </TableCell>
    </TableRow>
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          إدارة الشهادات
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadData}
          >
            تحديث
          </Button>
          <Button
            variant="outlined"
            startIcon={<Edit />}
            onClick={() => setTemplateDialog(true)}
          >
            تخصيص القالب
          </Button>
          <Button
            variant="contained"
            startIcon={<WorkspacePremium />}
            onClick={() => setCertificateDialog(true)}
            sx={{
              background: 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)',
              color: '#000',
              '&:hover': {
                background: 'linear-gradient(135deg, #FFA500 0%, #FFD700 100%)'
              }
            }}
          >
            إصدار شهادة جديدة
          </Button>
        </Box>
      </Box>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي الشهادات"
            value={stats.totalCertificates}
            icon={<WorkspacePremium />}
            color="#FFD700"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="في الانتظار"
            value={stats.pendingCertificates}
            icon={<Pending />}
            color="#FF9800"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="مُصدرة هذا الشهر"
            value={stats.issuedThisMonth}
            icon={<CheckCircle />}
            color="#4CAF50"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="متوسط الدرجات"
            value={stats.averageGrade}
            icon={<Star />}
            color="#9C27B0"
          />
        </Grid>
      </Grid>

      {/* التبويبات */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={selectedTab}
          onChange={(e, newValue) => setSelectedTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="جميع الشهادات" />
          <Tab label="الشهادات المُصدرة" />
          <Tab label="في الانتظار" />
          <Tab label="الإحصائيات" />
        </Tabs>
      </Paper>

      {/* جدول الشهادات */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>رقم الشهادة</TableCell>
              <TableCell>الطالب</TableCell>
              <TableCell>الكورس</TableCell>
              <TableCell>الدرجة</TableCell>
              <TableCell>الحالة</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {certificates
              .filter(cert => {
                if (selectedTab === 1) return cert.status === 'issued';
                if (selectedTab === 2) return cert.status === 'pending';
                return true;
              })
              .map((certificate) => (
                <CertificateRow key={certificate.id} certificate={certificate} />
              ))}
          </TableBody>
        </Table>
      </TableContainer>

      {certificates.length === 0 && (
        <Box sx={{ textAlign: 'center', mt: 8 }}>
          <WorkspacePremium sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            لا توجد شهادات مُصدرة حالياً
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            ابدأ بإصدار أول شهادة
          </Typography>
          <Button
            variant="contained"
            startIcon={<WorkspacePremium />}
            onClick={() => setCertificateDialog(true)}
          >
            إصدار شهادة جديدة
          </Button>
        </Box>
      )}

      {/* نافذة إصدار شهادة جديدة */}
      <Dialog
        open={certificateDialog}
        onClose={() => setCertificateDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>إصدار شهادة جديدة</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>الطالب</InputLabel>
                <Select
                  value={certificateForm.student_id}
                  onChange={(e) => setCertificateForm({ ...certificateForm, student_id: e.target.value })}
                >
                  {students.map((student) => (
                    <MenuItem key={student.id} value={student.id}>
                      {student.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>الكورس</InputLabel>
                <Select
                  value={certificateForm.course_id}
                  onChange={(e) => setCertificateForm({ ...certificateForm, course_id: e.target.value })}
                >
                  {courses.map((course) => (
                    <MenuItem key={course.id} value={course.id}>
                      {course.title} - {course.level}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="تاريخ الإكمال"
                type="date"
                value={certificateForm.completion_date}
                onChange={(e) => setCertificateForm({ ...certificateForm, completion_date: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>الدرجة</InputLabel>
                <Select
                  value={certificateForm.grade}
                  onChange={(e) => setCertificateForm({ ...certificateForm, grade: e.target.value })}
                >
                  <MenuItem value="A+">A+ (ممتاز)</MenuItem>
                  <MenuItem value="A">A (جيد جداً)</MenuItem>
                  <MenuItem value="B+">B+ (جيد)</MenuItem>
                  <MenuItem value="B">B (مقبول)</MenuItem>
                  <MenuItem value="C">C (ضعيف)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="ملاحظات إضافية"
                value={certificateForm.notes}
                onChange={(e) => setCertificateForm({ ...certificateForm, notes: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCertificateDialog(false)}>
            إلغاء
          </Button>
          <Button
            variant="contained"
            onClick={handleIssueCertificate}
            disabled={!certificateForm.student_id || !certificateForm.course_id}
          >
            إصدار الشهادة
          </Button>
        </DialogActions>
      </Dialog>

      {/* نافذة معاينة الشهادة */}
      <Dialog
        open={previewDialog}
        onClose={() => setPreviewDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>معاينة الشهادة</DialogTitle>
        <DialogContent>
          {selectedCertificate && (
            <Box
              sx={{
                p: 4,
                border: '2px solid #FFD700',
                borderRadius: 2,
                background: 'linear-gradient(135deg, #FFF 0%, #F8F9FA 100%)',
                textAlign: 'center',
                position: 'relative'
              }}
            >
              {/* زخرفة الشهادة */}
              <Box
                sx={{
                  position: 'absolute',
                  top: 20,
                  left: 20,
                  right: 20,
                  bottom: 20,
                  border: '1px solid #FFD700',
                  borderRadius: 1
                }}
              />
              
              <Typography variant="h3" sx={{ color: '#FFD700', fontWeight: 'bold', mb: 2 }}>
                شهادة إتمام
              </Typography>
              
              <Typography variant="h6" sx={{ mb: 4 }}>
                SKILLS WORLD ACADEMY
              </Typography>
              
              <Typography variant="h5" sx={{ mb: 2 }}>
                نشهد بأن
              </Typography>
              
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#4169E1', mb: 2 }}>
                {selectedCertificate.student_name}
              </Typography>
              
              <Typography variant="h6" sx={{ mb: 2 }}>
                قد أكمل بنجاح كورس
              </Typography>
              
              <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 4 }}>
                {selectedCertificate.course_title}
              </Typography>
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
                <Box>
                  <Typography variant="body2">تاريخ الإكمال</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    {selectedCertificate.completion_date}
                  </Typography>
                </Box>
                
                <Box>
                  <Typography variant="body2">الدرجة</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    {selectedCertificate.grade}
                  </Typography>
                </Box>
                
                <Box>
                  <Typography variant="body2">رقم الشهادة</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    {selectedCertificate.certificate_number}
                  </Typography>
                </Box>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialog(false)}>
            إغلاق
          </Button>
          <Button variant="contained" startIcon={<Download />}>
            تحميل
          </Button>
          <Button variant="contained" startIcon={<Print />}>
            طباعة
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CertificateManagement;
