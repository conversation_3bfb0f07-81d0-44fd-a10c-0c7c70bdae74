-- SKILLS WORLD ACADEMY - Database Setup
-- إعد<PERSON> قاعدة البيانات لأكاديمية عالم المهارات

-- تفعيل Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الطلاب
CREATE TABLE IF NOT EXISTS students (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20),
    student_code VARCHAR(6) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    bio TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول فئات الكورسات
CREATE TABLE IF NOT EXISTS course_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#4169E1',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الكورسات
CREATE TABLE IF NOT EXISTS courses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category_id UUID REFERENCES course_categories(id) ON DELETE SET NULL,
    level VARCHAR(20) DEFAULT 'beginner' CHECK (level IN ('beginner', 'intermediate', 'advanced')),
    duration VARCHAR(50),
    price DECIMAL(10,2) DEFAULT 0,
    thumbnail_url TEXT,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول التسجيلات
CREATE TABLE IF NOT EXISTS enrollments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    student_id UUID REFERENCES students(id) ON DELETE CASCADE,
    course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
    enrollment_date DATE DEFAULT CURRENT_DATE,
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, course_id)
);

-- إنشاء جدول فيديوهات الكورسات
CREATE TABLE IF NOT EXISTS course_videos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    video_url TEXT,
    file_path TEXT,
    file_size BIGINT,
    duration INTEGER, -- بالثواني
    order_index INTEGER DEFAULT 0,
    is_free BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول مواد الكورسات (PDF وملفات أخرى)
CREATE TABLE IF NOT EXISTS course_materials (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    file_path TEXT NOT NULL,
    file_type VARCHAR(50),
    file_size BIGINT,
    order_index INTEGER DEFAULT 0,
    is_downloadable BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الشهادات
CREATE TABLE IF NOT EXISTS certificates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    student_id UUID REFERENCES students(id) ON DELETE CASCADE,
    course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
    certificate_number VARCHAR(50) UNIQUE NOT NULL,
    completion_date DATE NOT NULL,
    issue_date DATE DEFAULT CURRENT_DATE,
    grade VARCHAR(5) DEFAULT 'A',
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'issued', 'revoked')),
    notes TEXT,
    certificate_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, course_id)
);

-- إنشاء جدول النشاطات (سجل العمليات)
CREATE TABLE IF NOT EXISTS activities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id VARCHAR(255),
    user_name VARCHAR(255),
    action VARCHAR(255) NOT NULL,
    description TEXT,
    entity_type VARCHAR(50), -- student, course, certificate, etc.
    entity_id UUID,
    metadata JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الإعدادات
CREATE TABLE IF NOT EXISTS settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    category VARCHAR(100) DEFAULT 'general',
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_students_code ON students(student_code);
CREATE INDEX IF NOT EXISTS idx_students_email ON students(email);
CREATE INDEX IF NOT EXISTS idx_courses_category ON courses(category_id);
CREATE INDEX IF NOT EXISTS idx_courses_status ON courses(status);
CREATE INDEX IF NOT EXISTS idx_enrollments_student ON enrollments(student_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_course ON enrollments(course_id);
CREATE INDEX IF NOT EXISTS idx_course_videos_course ON course_videos(course_id);
CREATE INDEX IF NOT EXISTS idx_course_materials_course ON course_materials(course_id);
CREATE INDEX IF NOT EXISTS idx_certificates_student ON certificates(student_id);
CREATE INDEX IF NOT EXISTS idx_certificates_course ON certificates(course_id);
CREATE INDEX IF NOT EXISTS idx_certificates_number ON certificates(certificate_number);
CREATE INDEX IF NOT EXISTS idx_activities_user ON activities(user_id);
CREATE INDEX IF NOT EXISTS idx_activities_entity ON activities(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(key);

-- إنشاء دوال التحديث التلقائي للوقت
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إضافة المشغلات للتحديث التلقائي
CREATE TRIGGER update_students_updated_at BEFORE UPDATE ON students FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_course_categories_updated_at BEFORE UPDATE ON course_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_courses_updated_at BEFORE UPDATE ON courses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_enrollments_updated_at BEFORE UPDATE ON enrollments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_course_videos_updated_at BEFORE UPDATE ON course_videos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_course_materials_updated_at BEFORE UPDATE ON course_materials FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_certificates_updated_at BEFORE UPDATE ON certificates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إدراج الإعدادات الأساسية
INSERT INTO settings (key, value, description, category, is_public) VALUES
('academy_name', 'SKILLS WORLD ACADEMY', 'اسم الأكاديمية', 'general', true),
('academy_description', 'أكاديمية عالم المهارات لتعليم البرمجة والتقنية', 'وصف الأكاديمية', 'general', true),
('contact_email', 'ALAA <EMAIL>', 'البريد الإلكتروني للتواصل', 'contact', true),
('contact_phone', '0506747770', 'رقم الهاتف للتواصل', 'contact', true),
('default_language', 'ar', 'اللغة الافتراضية', 'general', true),
('certificate_template', 'default', 'قالب الشهادات الافتراضي', 'certificates', false),
('auto_backup_enabled', 'true', 'تفعيل النسخ الاحتياطي التلقائي', 'system', false),
('backup_frequency', 'daily', 'تكرار النسخ الاحتياطي', 'system', false),
('max_file_size', '50MB', 'الحد الأقصى لحجم الملفات', 'system', false),
('allowed_file_types', 'pdf,mp4,avi,mov,jpg,png', 'أنواع الملفات المسموحة', 'system', false)
ON CONFLICT (key) DO NOTHING;

-- إنشاء دالة لتوليد كود طالب فريد
CREATE OR REPLACE FUNCTION generate_student_code()
RETURNS VARCHAR(6) AS $$
DECLARE
    new_code VARCHAR(6);
    code_exists BOOLEAN;
BEGIN
    LOOP
        -- توليد كود عشوائي من 6 أرقام
        new_code := LPAD(FLOOR(RANDOM() * 1000000)::TEXT, 6, '0');
        
        -- فحص إذا كان الكود موجود مسبقاً
        SELECT EXISTS(SELECT 1 FROM students WHERE student_code = new_code) INTO code_exists;
        
        -- إذا لم يكن موجود، اخرج من الحلقة
        IF NOT code_exists THEN
            EXIT;
        END IF;
    END LOOP;
    
    RETURN new_code;
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة لتوليد رقم شهادة فريد
CREATE OR REPLACE FUNCTION generate_certificate_number()
RETURNS VARCHAR(50) AS $$
DECLARE
    new_number VARCHAR(50);
    number_exists BOOLEAN;
    year_part VARCHAR(4);
    sequence_part VARCHAR(3);
BEGIN
    year_part := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
    
    LOOP
        -- توليد رقم تسلسلي عشوائي
        sequence_part := LPAD(FLOOR(RANDOM() * 1000)::TEXT, 3, '0');
        new_number := 'CERT-' || year_part || '-' || sequence_part;
        
        -- فحص إذا كان الرقم موجود مسبقاً
        SELECT EXISTS(SELECT 1 FROM certificates WHERE certificate_number = new_number) INTO number_exists;
        
        -- إذا لم يكن موجود، اخرج من الحلقة
        IF NOT number_exists THEN
            EXIT;
        END IF;
    END LOOP;
    
    RETURN new_number;
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة لتسجيل النشاطات
CREATE OR REPLACE FUNCTION log_activity(
    p_user_id VARCHAR(255),
    p_user_name VARCHAR(255),
    p_action VARCHAR(255),
    p_description TEXT DEFAULT NULL,
    p_entity_type VARCHAR(50) DEFAULT NULL,
    p_entity_id UUID DEFAULT NULL,
    p_metadata JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    activity_id UUID;
BEGIN
    INSERT INTO activities (
        user_id, user_name, action, description, 
        entity_type, entity_id, metadata
    ) VALUES (
        p_user_id, p_user_name, p_action, p_description,
        p_entity_type, p_entity_id, p_metadata
    ) RETURNING id INTO activity_id;
    
    RETURN activity_id;
END;
$$ LANGUAGE plpgsql;

-- تفعيل Row Level Security (RLS)
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE course_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE course_videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE course_materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE certificates ENABLE ROW LEVEL SECURITY;
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان الأساسية (يمكن تخصيصها حسب الحاجة)
-- السماح بالقراءة للجميع للإعدادات العامة
CREATE POLICY "Allow public read on public settings" ON settings
    FOR SELECT USING (is_public = true);

-- السماح للمدراء بالوصول الكامل
CREATE POLICY "Allow admin full access" ON students
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Allow admin full access" ON course_categories
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Allow admin full access" ON courses
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Allow admin full access" ON enrollments
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Allow admin full access" ON course_videos
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Allow admin full access" ON course_materials
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Allow admin full access" ON certificates
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Allow admin full access" ON activities
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Allow admin full access" ON settings
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- إنشاء Storage buckets للملفات
INSERT INTO storage.buckets (id, name, public) VALUES 
('course-content', 'course-content', false),
('certificates', 'certificates', false),
('avatars', 'avatars', true)
ON CONFLICT (id) DO NOTHING;

-- سياسات Storage
CREATE POLICY "Allow admin upload" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id IN ('course-content', 'certificates', 'avatars') AND auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Allow admin delete" ON storage.objects
    FOR DELETE USING (bucket_id IN ('course-content', 'certificates', 'avatars') AND auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Allow public read avatars" ON storage.objects
    FOR SELECT USING (bucket_id = 'avatars');

-- إنهاء الإعداد
SELECT 'Database setup completed successfully!' as status;
