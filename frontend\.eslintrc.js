module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: [
    'react-app',
    'react-app/jest',
  ],
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 12,
    sourceType: 'module',
  },
  plugins: [
    'react',
  ],
  rules: {
    // تحذيرات بدلاً من أخطاء للمتغيرات غير المستخدمة
    'no-unused-vars': 'warn',
    
    // السماح بـ console.log في التطوير
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    
    // تحذيرات للتبعيات المفقودة في useEffect
    'react-hooks/exhaustive-deps': 'warn',
    
    // السماح بالمتغيرات غير المعرفة (للمكتبات الخارجية)
    'no-undef': 'error',
    
    // تحذيرات للمكونات غير المستخدمة
    'react/jsx-no-undef': 'error',
    
    // إعدادات إضافية للمشروع العربي
    'jsx-quotes': ['warn', 'prefer-double'],
    'quotes': ['warn', 'single'],
    'semi': ['warn', 'always'],
    
    // السماح بالأسماء العربية
    'camelcase': 'off',
    
    // تحذيرات للكود غير المستخدم
    'no-unreachable': 'warn',
    'no-unused-expressions': 'warn',
    
    // إعدادات React
    'react/prop-types': 'off', // إيقاف فحص prop-types
    'react/display-name': 'off',
    'react/no-unescaped-entities': 'warn',
    
    // إعدادات الاستيراد
    'import/no-anonymous-default-export': 'warn',
    'import/no-unused-modules': 'off',
    
    // إعدادات عامة
    'prefer-const': 'warn',
    'no-var': 'error',
    'eqeqeq': 'warn',
    'curly': 'warn',
  },
  
  // إعدادات خاصة لملفات معينة
  overrides: [
    {
      files: ['**/*.test.js', '**/*.test.jsx'],
      env: {
        jest: true,
      },
      rules: {
        'no-console': 'off',
      },
    },
    {
      files: ['src/setupTests.js'],
      rules: {
        'import/no-anonymous-default-export': 'off',
      },
    },
  ],
  
  // إعدادات للملفات المستثناة
  ignorePatterns: [
    'build/',
    'node_modules/',
    'public/',
    '*.min.js',
    'serviceWorker.js',
  ],
  
  // إعدادات للمتغيرات العامة
  globals: {
    process: 'readonly',
    Buffer: 'readonly',
    __dirname: 'readonly',
    __filename: 'readonly',
    global: 'readonly',
    module: 'readonly',
    require: 'readonly',
    exports: 'readonly',
  },
};
