-- قواعد الأمان على مستوى الصفوف (RLS) لمشروع Skills World Academy
-- Row Level Security Policies for Skills World Academy

-- تفعيل RLS على جميع الجداول
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE faqs ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE certificates ENABLE ROW LEVEL SECURITY;

-- ===== قواعد جدول المستخدمين (Users) =====

-- المدير يمكنه رؤية جميع المستخدمين
CREATE POLICY "admin_can_view_all_users" ON users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.firebase_id = auth.uid() 
      AND admin_user.role = 'admin'
    )
  );

-- المستخدم يمكنه رؤية بياناته الشخصية فقط
CREATE POLICY "users_can_view_own_profile" ON users
  FOR SELECT USING (firebase_id = auth.uid());

-- المدير يمكنه إنشاء مستخدمين جدد
CREATE POLICY "admin_can_create_users" ON users
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.firebase_id = auth.uid() 
      AND admin_user.role = 'admin'
    )
  );

-- المدير يمكنه تحديث أي مستخدم، المستخدم يمكنه تحديث بياناته الشخصية فقط
CREATE POLICY "admin_can_update_all_users" ON users
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.firebase_id = auth.uid() 
      AND admin_user.role = 'admin'
    )
  );

CREATE POLICY "users_can_update_own_profile" ON users
  FOR UPDATE USING (firebase_id = auth.uid())
  WITH CHECK (
    firebase_id = auth.uid() AND
    role = (SELECT role FROM users WHERE firebase_id = auth.uid()) -- لا يمكن تغيير الدور
  );

-- المدير فقط يمكنه حذف المستخدمين
CREATE POLICY "admin_can_delete_users" ON users
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.firebase_id = auth.uid() 
      AND admin_user.role = 'admin'
    )
  );

-- ===== قواعد جدول الكورسات (Courses) =====

-- الجميع يمكنهم رؤية الكورسات النشطة
CREATE POLICY "everyone_can_view_active_courses" ON courses
  FOR SELECT USING (is_active = true);

-- المدير يمكنه رؤية جميع الكورسات
CREATE POLICY "admin_can_view_all_courses" ON courses
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.firebase_id = auth.uid() 
      AND admin_user.role = 'admin'
    )
  );

-- المدير فقط يمكنه إدارة الكورسات
CREATE POLICY "admin_can_manage_courses" ON courses
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.firebase_id = auth.uid() 
      AND admin_user.role = 'admin'
    )
  );

-- ===== قواعد جدول التسجيلات (Enrollments) =====

-- المدير يمكنه رؤية جميع التسجيلات
CREATE POLICY "admin_can_view_all_enrollments" ON enrollments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.firebase_id = auth.uid() 
      AND admin_user.role = 'admin'
    )
  );

-- الطالب يمكنه رؤية تسجيلاته فقط
CREATE POLICY "students_can_view_own_enrollments" ON enrollments
  FOR SELECT USING (
    user_id = (
      SELECT id FROM users 
      WHERE firebase_id = auth.uid()
    )
  );

-- المدير فقط يمكنه إدارة التسجيلات
CREATE POLICY "admin_can_manage_enrollments" ON enrollments
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.firebase_id = auth.uid() 
      AND admin_user.role = 'admin'
    )
  );

-- ===== قواعد جدول الأسئلة الشائعة (FAQs) =====

-- الجميع يمكنهم رؤية الأسئلة الشائعة النشطة
CREATE POLICY "everyone_can_view_active_faqs" ON faqs
  FOR SELECT USING (is_active = true);

-- المدير يمكنه رؤية جميع الأسئلة
CREATE POLICY "admin_can_view_all_faqs" ON faqs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.firebase_id = auth.uid() 
      AND admin_user.role = 'admin'
    )
  );

-- المدير فقط يمكنه إدارة الأسئلة الشائعة
CREATE POLICY "admin_can_manage_faqs" ON faqs
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.firebase_id = auth.uid() 
      AND admin_user.role = 'admin'
    )
  );

-- ===== قواعد جدول الإشعارات (Notifications) =====

-- المدير يمكنه رؤية جميع الإشعارات
CREATE POLICY "admin_can_view_all_notifications" ON notifications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.firebase_id = auth.uid() 
      AND admin_user.role = 'admin'
    )
  );

-- المستخدم يمكنه رؤية إشعاراته فقط
CREATE POLICY "users_can_view_own_notifications" ON notifications
  FOR SELECT USING (
    user_id = (
      SELECT id FROM users 
      WHERE firebase_id = auth.uid()
    )
  );

-- المدير فقط يمكنه إنشاء وإدارة الإشعارات
CREATE POLICY "admin_can_manage_notifications" ON notifications
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.firebase_id = auth.uid() 
      AND admin_user.role = 'admin'
    )
  );

-- ===== قواعد جدول الشهادات (Certificates) =====

-- المدير يمكنه رؤية جميع الشهادات
CREATE POLICY "admin_can_view_all_certificates" ON certificates
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.firebase_id = auth.uid() 
      AND admin_user.role = 'admin'
    )
  );

-- الطالب يمكنه رؤية شهاداته فقط
CREATE POLICY "students_can_view_own_certificates" ON certificates
  FOR SELECT USING (
    user_id = (
      SELECT id FROM users 
      WHERE firebase_id = auth.uid()
    )
  );

-- المدير فقط يمكنه إدارة الشهادات
CREATE POLICY "admin_can_manage_certificates" ON certificates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.firebase_id = auth.uid() 
      AND admin_user.role = 'admin'
    )
  );

-- ===== إنشاء فهارس لتحسين الأداء =====

-- فهارس جدول المستخدمين
CREATE INDEX IF NOT EXISTS idx_users_firebase_id ON users(firebase_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_student_code ON users(student_code);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);

-- فهارس جدول الكورسات
CREATE INDEX IF NOT EXISTS idx_courses_firebase_id ON courses(firebase_id);
CREATE INDEX IF NOT EXISTS idx_courses_category ON courses(category);
CREATE INDEX IF NOT EXISTS idx_courses_level ON courses(level);
CREATE INDEX IF NOT EXISTS idx_courses_is_active ON courses(is_active);

-- فهارس جدول التسجيلات
CREATE INDEX IF NOT EXISTS idx_enrollments_user_id ON enrollments(user_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_course_id ON enrollments(course_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_status ON enrollments(status);
CREATE INDEX IF NOT EXISTS idx_enrollments_user_course ON enrollments(user_id, course_id);

-- فهارس جدول الإشعارات
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);

-- فهارس جدول الشهادات
CREATE INDEX IF NOT EXISTS idx_certificates_user_id ON certificates(user_id);
CREATE INDEX IF NOT EXISTS idx_certificates_course_id ON certificates(course_id);
CREATE INDEX IF NOT EXISTS idx_certificates_number ON certificates(certificate_number);

-- ===== دوال مساعدة =====

-- دالة للحصول على معرف المستخدم من Firebase UID
CREATE OR REPLACE FUNCTION get_user_id_from_firebase_uid(firebase_uid TEXT)
RETURNS UUID AS $$
BEGIN
  RETURN (SELECT id FROM users WHERE firebase_id = firebase_uid LIMIT 1);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة للتحقق من أن المستخدم مدير
CREATE OR REPLACE FUNCTION is_admin(firebase_uid TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE firebase_id = firebase_uid 
    AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
