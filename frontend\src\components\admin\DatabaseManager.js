import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  LinearProgress,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Storage,
  Refresh,
  Delete,
  Backup,
  CloudDownload,
  Warning,
  CheckCircle,
  Error as ErrorIcon,
  Build,
  Analytics,
  CleaningServices,
  Security,
  Speed,
  TableChart,
  DataUsage,
  Sync,
  RestoreFromTrash,
  VerifiedUser,
  Schedule
} from '@mui/icons-material';
import { useLanguage } from '../../contexts/LanguageContext';
import toast from 'react-hot-toast';

const DatabaseManager = () => {
  const { language, t } = useLanguage();
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState(0);
  
  // حالات النوافذ المنبثقة
  const [backupDialog, setBackupDialog] = useState(false);
  const [cleanupDialog, setCleanupDialog] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState(false);
  
  // حالات البيانات
  const [dbStats, setDbStats] = useState({
    totalTables: 0,
    totalRecords: 0,
    dbSize: '0 MB',
    lastBackup: null,
    status: 'healthy'
  });
  
  const [tables, setTables] = useState([]);
  const [backups, setBackups] = useState([]);
  const [operations, setOperations] = useState([]);
  const [selectedOperation, setSelectedOperation] = useState('');
  const [operationProgress, setOperationProgress] = useState(0);
  const [isOperating, setIsOperating] = useState(false);

  useEffect(() => {
    loadDatabaseInfo();
  }, []);

  const loadDatabaseInfo = async () => {
    try {
      setLoading(true);
      
      // محاكاة تحميل معلومات قاعدة البيانات
      const tablesData = [
        {
          name: 'students',
          records: 150,
          size: '2.5 MB',
          status: 'healthy',
          lastUpdated: new Date().toISOString()
        },
        {
          name: 'courses',
          records: 25,
          size: '1.2 MB',
          status: 'healthy',
          lastUpdated: new Date().toISOString()
        },
        {
          name: 'enrollments',
          records: 300,
          size: '3.8 MB',
          status: 'healthy',
          lastUpdated: new Date().toISOString()
        },
        {
          name: 'certificates',
          records: 89,
          size: '1.5 MB',
          status: 'healthy',
          lastUpdated: new Date().toISOString()
        },
        {
          name: 'course_videos',
          records: 200,
          size: '5.2 MB',
          status: 'warning',
          lastUpdated: new Date().toISOString()
        },
        {
          name: 'course_materials',
          records: 125,
          size: '2.1 MB',
          status: 'healthy',
          lastUpdated: new Date().toISOString()
        }
      ];
      
      const backupsData = [
        {
          id: 1,
          name: 'backup_2024_01_15.sql',
          size: '15.2 MB',
          created: '2024-01-15T10:30:00Z',
          type: 'full',
          status: 'completed'
        },
        {
          id: 2,
          name: 'backup_2024_01_10.sql',
          size: '14.8 MB',
          created: '2024-01-10T10:30:00Z',
          type: 'full',
          status: 'completed'
        },
        {
          id: 3,
          name: 'backup_2024_01_05.sql',
          size: '14.1 MB',
          created: '2024-01-05T10:30:00Z',
          type: 'incremental',
          status: 'completed'
        }
      ];
      
      const operationsData = [
        {
          id: 1,
          operation: 'نسخ احتياطي كامل',
          timestamp: new Date().toISOString(),
          status: 'completed',
          duration: '2.5 دقيقة'
        },
        {
          id: 2,
          operation: 'تنظيف البيانات المؤقتة',
          timestamp: new Date(Date.now() - 86400000).toISOString(),
          status: 'completed',
          duration: '45 ثانية'
        },
        {
          id: 3,
          operation: 'فهرسة الجداول',
          timestamp: new Date(Date.now() - 172800000).toISOString(),
          status: 'completed',
          duration: '1.2 دقيقة'
        }
      ];
      
      setTables(tablesData);
      setBackups(backupsData);
      setOperations(operationsData);
      
      setDbStats({
        totalTables: tablesData.length,
        totalRecords: tablesData.reduce((sum, table) => sum + table.records, 0),
        dbSize: '16.3 MB',
        lastBackup: backupsData[0]?.created,
        status: 'healthy'
      });
      
    } catch (error) {
      console.error('خطأ في تحميل معلومات قاعدة البيانات:', error);
      toast.error('خطأ في تحميل معلومات قاعدة البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleDatabaseOperation = async (operation) => {
    try {
      setIsOperating(true);
      setOperationProgress(0);
      
      // محاكاة تقدم العملية
      const progressInterval = setInterval(() => {
        setOperationProgress(prev => {
          if (prev >= 100) {
            clearInterval(progressInterval);
            return 100;
          }
          return prev + 10;
        });
      }, 200);
      
      // محاكاة العملية
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      clearInterval(progressInterval);
      setOperationProgress(100);
      
      // إضافة العملية إلى السجل
      const newOperation = {
        id: Date.now(),
        operation: operation,
        timestamp: new Date().toISOString(),
        status: 'completed',
        duration: '2.1 دقيقة'
      };
      
      setOperations([newOperation, ...operations]);
      
      toast.success(`تم تنفيذ ${operation} بنجاح`);
      
      // إعادة تحميل البيانات
      loadDatabaseInfo();
      
    } catch (error) {
      console.error('خطأ في تنفيذ العملية:', error);
      toast.error('خطأ في تنفيذ العملية');
    } finally {
      setIsOperating(false);
      setOperationProgress(0);
      setConfirmDialog(false);
    }
  };

  const handleCreateBackup = () => {
    setSelectedOperation('إنشاء نسخة احتياطية');
    setConfirmDialog(true);
  };

  const handleCleanupDatabase = () => {
    setSelectedOperation('تنظيف قاعدة البيانات');
    setConfirmDialog(true);
  };

  const handleOptimizeTables = () => {
    setSelectedOperation('تحسين الجداول');
    setConfirmDialog(true);
  };

  // مكون بطاقة الإحصائيات
  const StatCard = ({ title, value, icon, color, subtitle, action }) => (
    <Card
      sx={{
        background: `linear-gradient(135deg, ${color}15 0%, ${color}05 100%)`,
        border: `1px solid ${color}30`,
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: `0 8px 25px ${color}25`
        }
      }}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box
            sx={{
              p: 1,
              borderRadius: 2,
              bgcolor: `${color}20`,
              color: color,
              mr: 2
            }}
          >
            {icon}
          </Box>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h5" sx={{ fontWeight: 'bold', color }}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
          </Box>
        </Box>
        {subtitle && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            {subtitle}
          </Typography>
        )}
        {action && (
          <Box sx={{ mt: 2 }}>
            {action}
          </Box>
        )}
      </CardContent>
    </Card>
  );

  // مكون صف الجدول
  const TableRow = ({ table }) => (
    <TableRow>
      <TableCell>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <TableChart sx={{ mr: 1, color: 'primary.main' }} />
          {table.name}
        </Box>
      </TableCell>
      <TableCell>{table.records.toLocaleString()}</TableCell>
      <TableCell>{table.size}</TableCell>
      <TableCell>
        <Chip
          label={table.status === 'healthy' ? 'سليم' : 'تحذير'}
          size="small"
          color={table.status === 'healthy' ? 'success' : 'warning'}
          icon={table.status === 'healthy' ? <CheckCircle /> : <Warning />}
        />
      </TableCell>
      <TableCell>
        {new Date(table.lastUpdated).toLocaleDateString('ar')}
      </TableCell>
      <TableCell>
        <Tooltip title="تحسين الجدول">
          <IconButton size="small">
            <Build />
          </IconButton>
        </Tooltip>
        <Tooltip title="تحليل الجدول">
          <IconButton size="small">
            <Analytics />
          </IconButton>
        </Tooltip>
      </TableCell>
    </TableRow>
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          إدارة قاعدة البيانات
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadDatabaseInfo}
          >
            تحديث
          </Button>
          <Button
            variant="contained"
            startIcon={<Backup />}
            onClick={handleCreateBackup}
            sx={{
              background: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)'
              }
            }}
          >
            نسخة احتياطية
          </Button>
        </Box>
      </Box>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي الجداول"
            value={dbStats.totalTables}
            icon={<TableChart />}
            color="#4169E1"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي السجلات"
            value={dbStats.totalRecords.toLocaleString()}
            icon={<DataUsage />}
            color="#FF6B35"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="حجم قاعدة البيانات"
            value={dbStats.dbSize}
            icon={<Storage />}
            color="#9C27B0"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="حالة قاعدة البيانات"
            value="سليمة"
            icon={<CheckCircle />}
            color="#4CAF50"
            subtitle={`آخر نسخة احتياطية: ${dbStats.lastBackup ? new Date(dbStats.lastBackup).toLocaleDateString('ar') : 'لا يوجد'}`}
          />
        </Grid>
      </Grid>

      {/* شريط التقدم للعمليات */}
      {isOperating && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Box sx={{ width: '100%' }}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              جاري تنفيذ: {selectedOperation}
            </Typography>
            <LinearProgress
              variant="determinate"
              value={operationProgress}
              sx={{ height: 8, borderRadius: 4 }}
            />
            <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
              {operationProgress}% مكتمل
            </Typography>
          </Box>
        </Alert>
      )}

      {/* التبويبات */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={selectedTab}
          onChange={(e, newValue) => setSelectedTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="الجداول" />
          <Tab label="النسخ الاحتياطية" />
          <Tab label="العمليات" />
          <Tab label="الصيانة" />
        </Tabs>
      </Paper>

      {/* محتوى التبويبات */}
      {selectedTab === 0 && (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>اسم الجدول</TableCell>
                <TableCell>عدد السجلات</TableCell>
                <TableCell>الحجم</TableCell>
                <TableCell>الحالة</TableCell>
                <TableCell>آخر تحديث</TableCell>
                <TableCell>الإجراءات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {tables.map((table) => (
                <TableRow key={table.name} table={table} />
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {selectedTab === 1 && (
        <Grid container spacing={3}>
          {backups.map((backup) => (
            <Grid item xs={12} sm={6} md={4} key={backup.id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Backup sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      {backup.name}
                    </Typography>
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    الحجم: {backup.size}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    التاريخ: {new Date(backup.created).toLocaleDateString('ar')}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    النوع: {backup.type === 'full' ? 'كامل' : 'تدريجي'}
                  </Typography>
                  
                  <Chip
                    label={backup.status === 'completed' ? 'مكتمل' : 'قيد التقدم'}
                    size="small"
                    color={backup.status === 'completed' ? 'success' : 'warning'}
                  />
                </CardContent>
                <CardActions>
                  <Button size="small" startIcon={<CloudDownload />}>
                    تحميل
                  </Button>
                  <Button size="small" startIcon={<RestoreFromTrash />}>
                    استعادة
                  </Button>
                  <Button size="small" color="error" startIcon={<Delete />}>
                    حذف
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {selectedTab === 2 && (
        <List>
          {operations.map((operation, index) => (
            <React.Fragment key={operation.id}>
              <ListItem>
                <ListItemIcon>
                  {operation.status === 'completed' ? (
                    <CheckCircle sx={{ color: 'success.main' }} />
                  ) : operation.status === 'failed' ? (
                    <ErrorIcon sx={{ color: 'error.main' }} />
                  ) : (
                    <Schedule sx={{ color: 'warning.main' }} />
                  )}
                </ListItemIcon>
                <ListItemText
                  primary={operation.operation}
                  secondary={
                    <Box>
                      <Typography variant="caption" display="block">
                        {new Date(operation.timestamp).toLocaleString('ar')}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        المدة: {operation.duration}
                      </Typography>
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  <Chip
                    label={
                      operation.status === 'completed' ? 'مكتمل' :
                      operation.status === 'failed' ? 'فشل' : 'قيد التقدم'
                    }
                    size="small"
                    color={
                      operation.status === 'completed' ? 'success' :
                      operation.status === 'failed' ? 'error' : 'warning'
                    }
                  />
                </ListItemSecondaryAction>
              </ListItem>
              {index < operations.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </List>
      )}

      {selectedTab === 3 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                  عمليات الصيانة
                </Typography>
                
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <CleaningServices />
                    </ListItemIcon>
                    <ListItemText
                      primary="تنظيف البيانات المؤقتة"
                      secondary="إزالة البيانات غير المستخدمة"
                    />
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={handleCleanupDatabase}
                      disabled={isOperating}
                    >
                      تنظيف
                    </Button>
                  </ListItem>
                  
                  <Divider />
                  
                  <ListItem>
                    <ListItemIcon>
                      <Speed />
                    </ListItemIcon>
                    <ListItemText
                      primary="تحسين الجداول"
                      secondary="تحسين أداء قاعدة البيانات"
                    />
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={handleOptimizeTables}
                      disabled={isOperating}
                    >
                      تحسين
                    </Button>
                  </ListItem>
                  
                  <Divider />
                  
                  <ListItem>
                    <ListItemIcon>
                      <Sync />
                    </ListItemIcon>
                    <ListItemText
                      primary="مزامنة البيانات"
                      secondary="مزامنة مع النسخة الاحتياطية"
                    />
                    <Button
                      variant="outlined"
                      size="small"
                      disabled={isOperating}
                    >
                      مزامنة
                    </Button>
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                  إعدادات النسخ الاحتياطي
                </Typography>
                
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>تكرار النسخ الاحتياطي</InputLabel>
                  <Select defaultValue="daily">
                    <MenuItem value="hourly">كل ساعة</MenuItem>
                    <MenuItem value="daily">يومياً</MenuItem>
                    <MenuItem value="weekly">أسبوعياً</MenuItem>
                    <MenuItem value="monthly">شهرياً</MenuItem>
                  </Select>
                </FormControl>
                
                <TextField
                  fullWidth
                  label="عدد النسخ المحفوظة"
                  type="number"
                  defaultValue={7}
                  sx={{ mb: 2 }}
                />
                
                <Alert severity="info">
                  النسخ الاحتياطية التلقائية مفعلة. آخر نسخة احتياطية تمت في {new Date().toLocaleDateString('ar')}
                </Alert>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* نافذة تأكيد العملية */}
      <Dialog
        open={confirmDialog}
        onClose={() => setConfirmDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>تأكيد العملية</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            هل أنت متأكد من تنفيذ عملية: {selectedOperation}؟
          </Alert>
          <Typography variant="body2">
            قد تستغرق هذه العملية بعض الوقت. يُنصح بعدم إغلاق المتصفح أثناء التنفيذ.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialog(false)}>
            إلغاء
          </Button>
          <Button
            variant="contained"
            onClick={() => handleDatabaseOperation(selectedOperation)}
            disabled={isOperating}
          >
            تأكيد التنفيذ
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DatabaseManager;
