<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />

    <!-- أيقونات الموقع -->
    <link rel="icon" href="%PUBLIC_URL%/favicon.jpg" type="image/jpeg" />
    <link rel="shortcut icon" href="%PUBLIC_URL%/favicon.jpg" type="image/jpeg" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/favicon.jpg" />
    <link rel="apple-touch-icon" sizes="180x180" href="%PUBLIC_URL%/favicon.jpg" />
    <link rel="icon" type="image/jpeg" sizes="32x32" href="%PUBLIC_URL%/favicon.jpg" />
    <link rel="icon" type="image/jpeg" sizes="16x16" href="%PUBLIC_URL%/favicon.jpg" />

    <!-- إعدادات viewport محسنة للأجهزة اللوحية والأجهزة اللمسية -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, minimum-scale=1.0, user-scalable=yes, viewport-fit=cover" />
    <meta name="theme-color" content="#2196F3" />
    <meta name="description" content="SKILLS WORLD ACADEMY - منصة التعلم والتطوير المهني العالمية" />

    <!-- إعدادات خاصة بالأجهزة اللمسية -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Skills World Academy" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="msapplication-tap-highlight" content="no" />

    <!-- إعدادات تحسين الأداء للأجهزة اللوحية -->
    <meta name="renderer" content="webkit" />
    <meta name="force-rendering" content="webkit" />
    <meta name="browsermode" content="application" />
    <meta name="x5-orientation" content="portrait" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="x5-page-mode" content="app" />

    <!-- Meta tags للمشاركة على وسائل التواصل الاجتماعي -->
    <meta property="og:title" content="SKILLS WORLD ACADEMY - منصة التعلم والتطوير المهني العالمية" />
    <meta property="og:description" content="🎓 تعلم المهارات الجديدة وطور قدراتك مع أفضل الكورسات التعليمية والمدربين المحترفين 📚 كورسات متنوعة | 🏆 شهادات معتمدة | 👨‍🏫 مدربين خبراء" />
    <meta property="og:image" content="%PUBLIC_URL%/social-share.jpg" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:type" content="image/jpeg" />
    <meta property="og:url" content="https://marketwise-academy-qhizq.web.app" />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="SKILLS WORLD ACADEMY" />
    <meta property="og:locale" content="ar_SA" />

    <!-- Twitter Card meta tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@SkillsWorldAcademy" />
    <meta name="twitter:creator" content="@SkillsWorldAcademy" />
    <meta name="twitter:title" content="SKILLS WORLD ACADEMY - منصة التعلم والتطوير المهني العالمية" />
    <meta name="twitter:description" content="🎓 تعلم المهارات الجديدة وطور قدراتك مع أفضل الكورسات التعليمية والمدربين المحترفين 📚 كورسات متنوعة | 🏆 شهادات معتمدة | 👨‍🏫 مدربين خبراء" />
    <meta name="twitter:image" content="%PUBLIC_URL%/social-share.jpg" />
    <meta name="twitter:image:alt" content="SKILLS WORLD ACADEMY - منصة التعلم والتطوير المهني العالمية" />

    <!-- LinkedIn meta tags -->
    <meta property="linkedin:owner" content="SKILLS WORLD ACADEMY" />

    <!-- WhatsApp meta tags -->
    <meta property="whatsapp:title" content="SKILLS WORLD ACADEMY - منصة التعلم والتطوير المهني العالمية" />
    <meta property="whatsapp:description" content="🎓 تعلم المهارات الجديدة وطور قدراتك مع أفضل الكورسات التعليمية" />
    <meta property="whatsapp:image" content="%PUBLIC_URL%/social-share.jpg" />

    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />



    <!-- Google Fonts for Arabic and English -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <title>SKILLS WORLD ACADEMY - منصة التعلم والتطوير المهني</title>

    <!-- سكريبت الإعداد التلقائي معطل مؤقتاً -->
    <!-- <script src="%PUBLIC_URL%/auto-setup.js"></script> -->

    <style>
      /* إعدادات أساسية محسنة للأجهزة اللوحية */
      * {
        box-sizing: border-box;
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
      }

      html {
        touch-action: manipulation;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
        height: 100%;
        width: 100%;
        overflow-x: hidden;
      }

      body {
        font-family: 'Cairo', sans-serif;
        margin: 0;
        padding: 0;
        background: #f5f5f5;
        min-height: 100vh;
        width: 100%;
        overflow-x: hidden;
        position: relative;

        /* تحسينات للأجهزة اللمسية */
        touch-action: manipulation;
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: contain;

        /* تحسين الأداء */
        will-change: auto;
        transform: translateZ(0);
        backface-visibility: hidden;
      }

      /* تحسينات خاصة للأجهزة اللوحية */
      @media (min-width: 768px) and (max-width: 1024px) {
        body {
          font-size: 16px !important;
        }

        /* تحسين العناصر التفاعلية للأجهزة اللوحية */
        button,
        [role="button"],
        .MuiIconButton-root,
        .MuiButton-root,
        .MuiListItemButton-root {
          min-height: 48px !important;
          min-width: 48px !important;
          touch-action: manipulation !important;
          cursor: pointer !important;
        }

        /* تحسين النصوص للأجهزة اللوحية */
        .MuiTypography-root {
          line-height: 1.5 !important;
        }
      }

      /* تحسينات للأجهزة اللوحية الكبيرة */
      @media (min-width: 1024px) and (max-width: 1366px) {
        body {
          font-size: 17px !important;
        }

        button,
        [role="button"],
        .MuiIconButton-root,
        .MuiButton-root,
        .MuiListItemButton-root {
          min-height: 52px !important;
          min-width: 52px !important;
        }
      }

      /* تحسينات خاصة بصورة الخلفية للاستجابة */
      @media (max-width: 768px) {
        /* تحسينات للموبايل */
        .login-background::before {
          background-attachment: scroll !important;
          background-size: cover !important;
          background-position: center center !important;
        }
      }

      @media (max-width: 480px) {
        /* تحسينات للشاشات الصغيرة جداً */
        .login-background::before {
          background-size: 120% auto !important;
          background-position: center top !important;
        }
      }

      /* تحسين الأداء للصورة */
      .login-background::before {
        content-visibility: auto;
        contain-intrinsic-size: 100vw 100vh;
      }

      /* تحسينات إضافية عند تحميل الصورة */
      body.background-loaded .login-background::before {
        opacity: 0.28 !important;
        filter: blur(0.5px) brightness(1.15) contrast(1.05) !important;
      }

      /* تحسين الخطوط والنصوص فوق الخلفية */
      .login-background .MuiCard-root {
        backdrop-filter: blur(8px) saturate(1.2);
        background: rgba(255, 255, 255, 0.95) !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 16px rgba(0, 0, 0, 0.08) !important;
      }

      /* تحسين النصوص للقراءة الأفضل */
      .login-background .MuiTypography-root {
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
      }

      /* تحسين الأزرار */
      .login-background .MuiButton-root {
        backdrop-filter: blur(4px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
      }
      
      #root {
        min-height: 100vh;
        width: 100%;
        overflow-x: hidden;
        position: relative;

        /* تحسين الأداء للأجهزة اللوحية */
        will-change: auto;
        transform: translateZ(0);
        backface-visibility: hidden;
      }

      /* تحسينات عامة للتفاعل مع الأجهزة اللمسية */
      @media (pointer: coarse) {
        * {
          touch-action: manipulation !important;
        }

        button,
        [role="button"],
        .MuiIconButton-root,
        .MuiButton-root,
        .MuiListItemButton-root,
        .MuiMenuItem-root {
          min-height: 48px !important;
          min-width: 48px !important;
          padding: 12px !important;
          cursor: pointer !important;
          -webkit-tap-highlight-color: rgba(0, 0, 255, 0.1) !important;
        }
      }

      /* تحسينات للأجهزة التي تدعم hover */
      @media (hover: hover) and (pointer: fine) {
        button:hover,
        [role="button"]:hover,
        .MuiIconButton-root:hover,
        .MuiButton-root:hover,
        .MuiListItemButton-root:hover {
          transform: scale(1.02) !important;
          transition: transform 0.2s ease !important;
        }
      }

      /* تحسين active state لجميع الأجهزة */
      button:active,
      [role="button"]:active,
      .MuiIconButton-root:active,
      .MuiButton-root:active,
      .MuiListItemButton-root:active {
        transform: scale(0.98) !important;
        transition: transform 0.1s ease !important;
        opacity: 0.8 !important;
      }

      /* إزالة أنماط التحميل */
    </style>
  </head>
  <body>
    <noscript>يجب تفعيل JavaScript لتشغيل هذا التطبيق.</noscript>
    <div id="root"></div>
  </body>
</html>
