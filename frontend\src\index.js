import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { setupGlobalErrorHandlers } from './utils/errorHandler';
import './styles/responsive.css';
import './styles/arabic-fonts.css';
import './styles/modern-design.css';
import './styles/tablet-optimizations.css';
import './styles/mobile-optimizations.css';
import './styles/admin-layout-fixes.css';

// إعداد معالجات الأخطاء العامة
setupGlobalErrorHandlers();

const root = ReactDOM.createRoot(document.getElementById('root'));

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
