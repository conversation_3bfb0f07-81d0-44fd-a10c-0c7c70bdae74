/**
 * إنشاء بيانات اختبار للمشروع
 */

import { collection, addDoc, doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../firebase/config';

// إنشاء مدير افتراضي
export const createDefaultAdmin = async () => {
  try {
    console.log('👨‍💼 إنشاء مدير افتراضي...');

    const adminData = {
      name: 'علاء عبد الحميد',
      email: '<EMAIL>',
      phone: '0506747770',
      role: 'admin',
      isActive: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      lastLogin: serverTimestamp()
    };

    // إضافة المدير إلى مجموعة users
    const adminRef = await addDoc(collection(db, 'users'), adminData);
    console.log('✅ تم إنشاء المدير بنجاح:', adminRef.id);

    return {
      success: true,
      adminId: adminRef.id,
      credentials: {
        email: '<EMAIL>',
        password: 'admin123'
      }
    };
  } catch (error) {
    console.error('❌ خطأ في إنشاء المدير:', error);
    return { success: false, error: error.message };
  }
};

// إنشاء طلاب افتراضيين
export const createDefaultStudents = async () => {
  try {
    console.log('👨‍🎓 إنشاء طلاب افتراضيين...');

    const students = [
      {
        name: 'أحمد محمد',
        studentCode: '123456',
        phone: '0501234567',
        role: 'student',
        isActive: true,
        enrolledCourses: [],
        createdAt: serverTimestamp()
      },
      {
        name: 'فاطمة علي',
        studentCode: '234567',
        phone: '0502345678',
        role: 'student',
        isActive: true,
        enrolledCourses: [],
        createdAt: serverTimestamp()
      },
      {
        name: 'محمد سالم',
        studentCode: '345678',
        phone: '0503456789',
        role: 'student',
        isActive: true,
        enrolledCourses: [],
        createdAt: serverTimestamp()
      }
    ];

    const createdStudents = [];
    for (const student of students) {
      const studentRef = await addDoc(collection(db, 'users'), student);
      createdStudents.push({ id: studentRef.id, ...student });
    }

    console.log('✅ تم إنشاء الطلاب بنجاح:', createdStudents.length);
    return { success: true, students: createdStudents };
  } catch (error) {
    console.error('❌ خطأ في إنشاء الطلاب:', error);
    return { success: false, error: error.message };
  }
};

// إنشاء كورسات افتراضية
export const createDefaultCourses = async () => {
  try {
    console.log('📚 إنشاء كورسات افتراضية...');

    const courses = [
      {
        title: 'أساسيات البرمجة',
        description: 'تعلم أساسيات البرمجة من الصفر',
        instructor: 'علاء عبد الحميد',
        duration: '4 أسابيع',
        level: 'مبتدئ',
        price: 299,
        isActive: true,
        createdAt: serverTimestamp()
      },
      {
        title: 'تطوير المواقع',
        description: 'تعلم تطوير المواقع باستخدام HTML, CSS, JavaScript',
        instructor: 'علاء عبد الحميد',
        duration: '6 أسابيع',
        level: 'متوسط',
        price: 499,
        isActive: true,
        createdAt: serverTimestamp()
      },
      {
        title: 'قواعد البيانات',
        description: 'تعلم إدارة قواعد البيانات SQL و NoSQL',
        instructor: 'علاء عبد الحميد',
        duration: '5 أسابيع',
        level: 'متقدم',
        price: 399,
        isActive: true,
        createdAt: serverTimestamp()
      }
    ];

    const createdCourses = [];
    for (const course of courses) {
      const courseRef = await addDoc(collection(db, 'courses'), course);
      createdCourses.push({ id: courseRef.id, ...course });
    }

    console.log('✅ تم إنشاء الكورسات بنجاح:', createdCourses.length);
    return { success: true, courses: createdCourses };
  } catch (error) {
    console.error('❌ خطأ في إنشاء الكورسات:', error);
    return { success: false, error: error.message };
  }
};

// إنشاء أسئلة شائعة افتراضية
export const createDefaultFAQs = async () => {
  try {
    console.log('❓ إنشاء أسئلة شائعة افتراضية...');

    const faqs = [
      {
        question: 'كيف يمكنني التسجيل في الكورس؟',
        answer: 'يمكنك التسجيل من خلال التواصل مع الإدارة أو استخدام كود الطالب الخاص بك',
        isActive: true,
        createdAt: serverTimestamp()
      },
      {
        question: 'ما هي مدة الكورس؟',
        answer: 'تختلف مدة الكورسات حسب المحتوى، وتتراوح من 4 إلى 8 أسابيع',
        isActive: true,
        createdAt: serverTimestamp()
      },
      {
        question: 'هل يمكنني الحصول على شهادة؟',
        answer: 'نعم، ستحصل على شهادة إتمام معتمدة بعد إنهاء الكورس بنجاح',
        isActive: true,
        createdAt: serverTimestamp()
      }
    ];

    const createdFAQs = [];
    for (const faq of faqs) {
      const faqRef = await addDoc(collection(db, 'faqs'), faq);
      createdFAQs.push({ id: faqRef.id, ...faq });
    }

    console.log('✅ تم إنشاء الأسئلة الشائعة بنجاح:', createdFAQs.length);
    return { success: true, faqs: createdFAQs };
  } catch (error) {
    console.error('❌ خطأ في إنشاء الأسئلة الشائعة:', error);
    return { success: false, error: error.message };
  }
};

// إنشاء جميع البيانات الافتراضية
export const createAllTestData = async () => {
  try {
    console.log('🚀 بدء إنشاء جميع البيانات الافتراضية...');

    const results = {
      admin: await createDefaultAdmin(),
      students: await createDefaultStudents(),
      courses: await createDefaultCourses(),
      faqs: await createDefaultFAQs()
    };

    console.log('✅ تم إنشاء جميع البيانات الافتراضية بنجاح!');
    return { success: true, results };
  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات الافتراضية:', error);
    return { success: false, error: error.message };
  }
};

export default {
  createDefaultAdmin,
  createDefaultStudents,
  createDefaultCourses,
  createDefaultFAQs,
  createAllTestData
};
