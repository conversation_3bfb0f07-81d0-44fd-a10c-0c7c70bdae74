/**
 * مُهيئ قاعدة البيانات الكامل لمشروع Skills World Academy
 * يقوم بإنشاء هيكل قاعدة البيانات بدون بيانات تجريبية
 */

import { collection, doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../firebase/config';
import { supabase } from '../supabase/config';

/**
 * إنشاء هيكل Firebase Collections
 */
export const initializeFirebaseCollections = async () => {
  try {
    console.log('🔧 بدء تهيئة مجموعات Firebase...');

    // 1. إعدادات النظام العامة
    const systemSettingsRef = doc(db, 'settings', 'system');
    await setDoc(systemSettingsRef, {
      siteName: 'SKILLS WORLD ACADEMY',
      adminName: 'ALAA ABD HAMIED',
      adminEmail: 'ALAA <EMAIL>',
      adminPhone: '0506747770',
      defaultLanguage: 'ar',
      rtlEnabled: true,
      maxStudentsPerCourse: 50,
      allowSelfRegistration: false,
      maintenanceMode: false,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    }, { merge: true });

    // 2. إعدادات الأمان
    const securitySettingsRef = doc(db, 'settings', 'security');
    await setDoc(securitySettingsRef, {
      passwordMinLength: 6,
      sessionTimeout: 24, // ساعات
      maxLoginAttempts: 5,
      lockoutDuration: 30, // دقائق
      requireEmailVerification: false,
      enableTwoFactor: false,
      allowPasswordReset: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    }, { merge: true });

    // 3. إعدادات الإشعارات
    const notificationSettingsRef = doc(db, 'settings', 'notifications');
    await setDoc(notificationSettingsRef, {
      emailNotifications: true,
      smsNotifications: false,
      pushNotifications: true,
      enrollmentNotifications: true,
      courseUpdateNotifications: true,
      systemMaintenanceNotifications: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    }, { merge: true });

    // 4. إعدادات الملفات والتخزين
    const storageSettingsRef = doc(db, 'settings', 'storage');
    await setDoc(storageSettingsRef, {
      maxFileSize: 100, // MB
      allowedVideoFormats: ['mp4', 'avi', 'mov', 'wmv'],
      allowedDocumentFormats: ['pdf', 'doc', 'docx', 'ppt', 'pptx'],
      allowedImageFormats: ['jpg', 'jpeg', 'png', 'gif'],
      videoQuality: 'high',
      autoBackup: true,
      backupFrequency: 'daily',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    }, { merge: true });

    // 5. إنشاء مجموعة المستخدمين (فارغة)
    const usersCollectionRef = collection(db, 'users');
    console.log('📁 تم إنشاء مجموعة المستخدمين');

    // 6. إنشاء مجموعة الكورسات (فارغة)
    const coursesCollectionRef = collection(db, 'courses');
    console.log('📁 تم إنشاء مجموعة الكورسات');

    // 7. إنشاء مجموعة التسجيلات (فارغة)
    const enrollmentsCollectionRef = collection(db, 'enrollments');
    console.log('📁 تم إنشاء مجموعة التسجيلات');

    // 8. إنشاء مجموعة الأسئلة الشائعة (فارغة)
    const faqsCollectionRef = collection(db, 'faqs');
    console.log('📁 تم إنشاء مجموعة الأسئلة الشائعة');

    // 9. إنشاء مجموعة الإشعارات (فارغة)
    const notificationsCollectionRef = collection(db, 'notifications');
    console.log('📁 تم إنشاء مجموعة الإشعارات');

    // 10. إنشاء مجموعة الشهادات (فارغة)
    const certificatesCollectionRef = collection(db, 'certificates');
    console.log('📁 تم إنشاء مجموعة الشهادات');

    // 11. إنشاء مجموعة التفاعلات (فارغة)
    const interactionsCollectionRef = collection(db, 'interactions');
    console.log('📁 تم إنشاء مجموعة التفاعلات');

    // 12. إنشاء مجموعة النشاطات (فارغة)
    const activitiesCollectionRef = collection(db, 'activities');
    console.log('📁 تم إنشاء مجموعة النشاطات');

    console.log('✅ تم تهيئة جميع مجموعات Firebase بنجاح');
    return { success: true, message: 'تم تهيئة Firebase بنجاح' };

  } catch (error) {
    console.error('❌ خطأ في تهيئة Firebase:', error);
    return { success: false, error: error.message };
  }
};

/**
 * إنشاء هيكل Supabase Tables
 */
export const initializeSupabaseTables = async () => {
  try {
    console.log('🔧 بدء تهيئة جداول Supabase...');

    // 1. جدول المستخدمين
    const createUsersTable = `
      CREATE TABLE IF NOT EXISTS users (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        firebase_id TEXT UNIQUE,
        name TEXT NOT NULL,
        email TEXT UNIQUE,
        phone TEXT,
        student_code TEXT UNIQUE,
        role TEXT CHECK (role IN ('admin', 'student')) NOT NULL,
        is_active BOOLEAN DEFAULT true,
        profile_image TEXT,
        last_login TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    // 2. جدول الكورسات
    const createCoursesTable = `
      CREATE TABLE IF NOT EXISTS courses (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        firebase_id TEXT UNIQUE,
        title TEXT NOT NULL,
        description TEXT,
        instructor TEXT NOT NULL,
        category TEXT,
        level TEXT CHECK (level IN ('beginner', 'intermediate', 'advanced')),
        duration INTEGER, -- بالساعات
        price DECIMAL(10,2),
        thumbnail_url TEXT,
        video_url TEXT,
        pdf_url TEXT,
        is_active BOOLEAN DEFAULT true,
        max_students INTEGER DEFAULT 50,
        current_students INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    // 3. جدول التسجيلات
    const createEnrollmentsTable = `
      CREATE TABLE IF NOT EXISTS enrollments (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        firebase_id TEXT UNIQUE,
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
        enrollment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        completion_date TIMESTAMP WITH TIME ZONE,
        progress DECIMAL(5,2) DEFAULT 0.00, -- نسبة الإنجاز
        status TEXT CHECK (status IN ('active', 'completed', 'suspended')) DEFAULT 'active',
        grade DECIMAL(5,2),
        certificate_issued BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, course_id)
      );
    `;

    // 4. جدول الأسئلة الشائعة
    const createFAQsTable = `
      CREATE TABLE IF NOT EXISTS faqs (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        firebase_id TEXT UNIQUE,
        question TEXT NOT NULL,
        answer TEXT NOT NULL,
        category TEXT,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    // 5. جدول الإشعارات
    const createNotificationsTable = `
      CREATE TABLE IF NOT EXISTS notifications (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        firebase_id TEXT UNIQUE,
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT CHECK (type IN ('info', 'success', 'warning', 'error')) DEFAULT 'info',
        is_read BOOLEAN DEFAULT false,
        action_url TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    // 6. جدول الشهادات
    const createCertificatesTable = `
      CREATE TABLE IF NOT EXISTS certificates (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        firebase_id TEXT UNIQUE,
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
        certificate_number TEXT UNIQUE NOT NULL,
        issue_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        expiry_date TIMESTAMP WITH TIME ZONE,
        certificate_url TEXT,
        is_valid BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    // تنفيذ الاستعلامات
    const tables = [
      { name: 'users', query: createUsersTable },
      { name: 'courses', query: createCoursesTable },
      { name: 'enrollments', query: createEnrollmentsTable },
      { name: 'faqs', query: createFAQsTable },
      { name: 'notifications', query: createNotificationsTable },
      { name: 'certificates', query: createCertificatesTable }
    ];

    for (const table of tables) {
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: table.query });
        if (error) {
          console.warn(`⚠️ تحذير في إنشاء جدول ${table.name}:`, error.message);
        } else {
          console.log(`✅ تم إنشاء جدول ${table.name} بنجاح`);
        }
      } catch (err) {
        console.warn(`⚠️ تحذير في إنشاء جدول ${table.name}:`, err.message);
      }
    }

    console.log('✅ تم تهيئة جميع جداول Supabase بنجاح');
    return { success: true, message: 'تم تهيئة Supabase بنجاح' };

  } catch (error) {
    console.error('❌ خطأ في تهيئة Supabase:', error);
    return { success: false, error: error.message };
  }
};

/**
 * تهيئة قاعدة البيانات الكاملة
 */
export const initializeCompleteDatabase = async () => {
  try {
    console.log('🚀 بدء تهيئة قاعدة البيانات الكاملة...');

    // تهيئة Firebase
    const firebaseResult = await initializeFirebaseCollections();
    if (!firebaseResult.success) {
      throw new Error('فشل في تهيئة Firebase: ' + firebaseResult.error);
    }

    // تهيئة Supabase
    const supabaseResult = await initializeSupabaseTables();
    if (!supabaseResult.success) {
      console.warn('تحذير في تهيئة Supabase:', supabaseResult.error);
    }

    console.log('🎉 تم تهيئة قاعدة البيانات الكاملة بنجاح!');
    return {
      success: true,
      message: 'تم تهيئة قاعدة البيانات بنجاح',
      firebase: firebaseResult,
      supabase: supabaseResult
    };

  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
    return { success: false, error: error.message };
  }
};

export default {
  initializeFirebaseCollections,
  initializeSupabaseTables,
  initializeCompleteDatabase
};
