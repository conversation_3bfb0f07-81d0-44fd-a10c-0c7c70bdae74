import React, { useState, useEffect } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Badge,
  useTheme,
  useMediaQuery,
  Container,
  Alert,
  CircularProgress,
  Chip,
  Tooltip,
  Menu,
  MenuItem
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard,
  School,
  VideoLibrary,
  Group,
  PersonAdd,
  Settings,
  Logout,
  Notifications,
  CheckCircle,
  Error as ErrorIcon,
  Sync,
  Storage,
  WorkspacePremium,
  AccountCircle,
  Language,
  Close
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import toast from 'react-hot-toast';

// استيراد مكونات لوحة التحكم
import DashboardOverview from './admin/DashboardOverview';
import CourseManagement from './admin/CourseManagement';
import StudentManagement from './admin/StudentManagement';
import CertificateManagement from './admin/CertificateManagement';
import AdminProfile from './admin/AdminProfile';
import DatabaseManager from './admin/DatabaseManager';

const drawerWidth = 280;

const AdminDashboard = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, logout } = useAuth();
  const { language, changeLanguage, t, isRTL } = useLanguage();

  // حالات المكون
  const [mobileOpen, setMobileOpen] = useState(false);
  const [selectedSection, setSelectedSection] = useState('dashboard');
  const [loading, setLoading] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [anchorEl, setAnchorEl] = useState(null);

  // قائمة عناصر التنقل
  const navigationItems = [
    {
      id: 'dashboard',
      label: t.dashboard || 'لوحة التحكم',
      icon: <Dashboard />,
      color: '#4169E1'
    },
    {
      id: 'courses',
      label: t.courseManagement || 'إدارة الكورسات',
      icon: <School />,
      color: '#FF6B35'
    },
    {
      id: 'students',
      label: t.studentManagement || 'إدارة الطلاب',
      icon: <Group />,
      color: '#32CD32'
    },
    {
      id: 'certificates',
      label: t.certificateManagement || 'إدارة الشهادات',
      icon: <WorkspacePremium />,
      color: '#FFD700'
    },
    {
      id: 'profile',
      label: t.profile || 'الملف الشخصي',
      icon: <AccountCircle />,
      color: '#9C27B0'
    },
    {
      id: 'database',
      label: t.databaseManagement || 'إدارة قاعدة البيانات',
      icon: <Storage />,
      color: '#FF5722'
    }
  ];

  // تحديث الإشعارات
  useEffect(() => {
    loadNotifications();
  }, []);

  const loadNotifications = async () => {
    try {
      // هنا يمكن تحميل الإشعارات من قاعدة البيانات
      setNotifications([
        { id: 1, message: 'طالب جديد سجل في الكورس', type: 'info', time: '5 دقائق' },
        { id: 2, message: 'تم رفع فيديو جديد بنجاح', type: 'success', time: '10 دقائق' }
      ]);
    } catch (error) {
      console.error('خطأ في تحميل الإشعارات:', error);
    }
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleSectionChange = (section) => {
    setSelectedSection(section);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      toast.success('تم تسجيل الخروج بنجاح');
    } catch (error) {
      toast.error('خطأ في تسجيل الخروج');
    }
  };

  const handleLanguageToggle = () => {
    changeLanguage(language === 'ar' ? 'en' : 'ar');
    toast.success(language === 'ar' ? 'Language changed to English' : 'تم تغيير اللغة إلى العربية');
  };

  // محتوى الشريط الجانبي
  const drawerContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* رأس الشريط الجانبي */}
      <Box
        sx={{
          p: 3,
          background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
          color: 'white',
          textAlign: 'center'
        }}
      >
        <Avatar
          sx={{
            width: 60,
            height: 60,
            mx: 'auto',
            mb: 2,
            bgcolor: '#FFD700',
            color: '#0000FF',
            fontSize: '1.5rem',
            fontWeight: 'bold'
          }}
        >
          {user?.name?.charAt(0) || 'A'}
        </Avatar>
        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 0.5 }}>
          {user?.name || 'المدير'}
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.9 }}>
          SKILLS WORLD ACADEMY
        </Typography>
      </Box>

      {/* قائمة التنقل */}
      <List sx={{ flex: 1, p: 1 }}>
        {navigationItems.map((item) => (
          <ListItem key={item.id} disablePadding sx={{ mb: 0.5 }}>
            <ListItemButton
              selected={selectedSection === item.id}
              onClick={() => handleSectionChange(item.id)}
              sx={{
                borderRadius: 2,
                mx: 1,
                '&.Mui-selected': {
                  bgcolor: `${item.color}15`,
                  color: item.color,
                  '& .MuiListItemIcon-root': {
                    color: item.color
                  }
                },
                '&:hover': {
                  bgcolor: `${item.color}08`,
                  '& .MuiListItemIcon-root': {
                    color: item.color
                  }
                }
              }}
            >
              <ListItemIcon sx={{ minWidth: 40 }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText 
                primary={item.label}
                sx={{
                  '& .MuiTypography-root': {
                    fontWeight: selectedSection === item.id ? 600 : 400
                  }
                }}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>

      <Divider />

      {/* إعدادات سفلية */}
      <Box sx={{ p: 2 }}>
        <ListItemButton
          onClick={handleLanguageToggle}
          sx={{
            borderRadius: 2,
            mb: 1,
            '&:hover': { bgcolor: 'rgba(0,0,0,0.04)' }
          }}
        >
          <ListItemIcon sx={{ minWidth: 40 }}>
            <Language />
          </ListItemIcon>
          <ListItemText primary={language === 'ar' ? 'English' : 'العربية'} />
        </ListItemButton>

        <ListItemButton
          onClick={handleLogout}
          sx={{
            borderRadius: 2,
            color: 'error.main',
            '&:hover': { bgcolor: 'error.light', color: 'white' }
          }}
        >
          <ListItemIcon sx={{ minWidth: 40, color: 'inherit' }}>
            <Logout />
          </ListItemIcon>
          <ListItemText primary={t.logout || 'تسجيل الخروج'} />
        </ListItemButton>
      </Box>
    </Box>
  );

  // عرض المحتوى حسب القسم المحدد
  const renderContent = () => {
    switch (selectedSection) {
      case 'dashboard':
        return <DashboardOverview />;
      case 'courses':
        return <CourseManagement />;
      case 'students':
        return <StudentManagement />;
      case 'certificates':
        return <CertificateManagement />;
      case 'profile':
        return <AdminProfile />;
      case 'database':
        return <DatabaseManager />;
      default:
        return <DashboardOverview />;
    }
  };

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', bgcolor: '#f5f7fa' }}>
      {/* الشريط العلوي */}
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` },
          mr: { md: isRTL ? `${drawerWidth}px` : 0 },
          background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
          boxShadow: '0 4px 20px rgba(65, 105, 225, 0.3)'
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>

          <Typography variant="h6" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
            {navigationItems.find(item => item.id === selectedSection)?.label || 'لوحة التحكم'}
          </Typography>

          {/* الإشعارات */}
          <Tooltip title="الإشعارات">
            <IconButton
              color="inherit"
              onClick={(e) => setAnchorEl(e.currentTarget)}
            >
              <Badge badgeContent={notifications.length} color="error">
                <Notifications />
              </Badge>
            </IconButton>
          </Tooltip>

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={() => setAnchorEl(null)}
            PaperProps={{
              sx: { width: 300, maxHeight: 400 }
            }}
          >
            {notifications.length > 0 ? (
              notifications.map((notification) => (
                <MenuItem key={notification.id} sx={{ whiteSpace: 'normal' }}>
                  <Box>
                    <Typography variant="body2">{notification.message}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      منذ {notification.time}
                    </Typography>
                  </Box>
                </MenuItem>
              ))
            ) : (
              <MenuItem>
                <Typography variant="body2" color="text.secondary">
                  لا توجد إشعارات جديدة
                </Typography>
              </MenuItem>
            )}
          </Menu>
        </Toolbar>
      </AppBar>

      {/* الشريط الجانبي */}
      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              direction: isRTL ? 'rtl' : 'ltr'
            }
          }}
        >
          {drawerContent}
        </Drawer>

        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              direction: isRTL ? 'rtl' : 'ltr',
              borderRight: isRTL ? '1px solid rgba(0, 0, 0, 0.12)' : 'none',
              borderLeft: !isRTL ? '1px solid rgba(0, 0, 0, 0.12)' : 'none'
            }
          }}
          open
        >
          {drawerContent}
        </Drawer>
      </Box>

      {/* المحتوى الرئيسي */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mt: '64px'
        }}
      >
        <Container maxWidth="xl">
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderContent()
          )}
        </Container>
      </Box>
    </Box>
  );
};

export default AdminDashboard;
