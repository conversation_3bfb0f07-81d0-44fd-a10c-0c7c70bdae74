/* خطوط عربية محسنة لمشروع Skills World Academy */
/* Enhanced Arabic Fonts for Skills World Academy */

/* استيراد خطوط Google Fonts العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap');

/* ===== متغيرات الخطوط العربية ===== */
:root {
  /* عائلات الخطوط */
  --font-arabic-primary: 'Cairo', 'Segoe UI', 'Roboto', sans-serif;
  --font-arabic-secondary: '<PERSON>jawal', 'Cairo', sans-serif;
  --font-arabic-elegant: 'Almarai', 'Cairo', sans-serif;
  --font-arabic-traditional: 'Amiri', 'Times New Roman', serif;
  
  /* أوزان الخطوط */
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  /* أحجام الخطوط المتجاوبة */
  --font-size-xs: clamp(0.75rem, 2vw, 0.875rem);
  --font-size-sm: clamp(0.875rem, 2.5vw, 1rem);
  --font-size-base: clamp(1rem, 3vw, 1.125rem);
  --font-size-lg: clamp(1.125rem, 3.5vw, 1.25rem);
  --font-size-xl: clamp(1.25rem, 4vw, 1.5rem);
  --font-size-2xl: clamp(1.5rem, 5vw, 2rem);
  --font-size-3xl: clamp(2rem, 6vw, 2.5rem);
  
  /* ارتفاع الأسطر */
  --line-height-tight: 1.4;
  --line-height-normal: 1.6;
  --line-height-relaxed: 1.8;
  
  /* تباعد الأحرف */
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  
  /* تباعد الكلمات */
  --word-spacing-normal: 0;
  --word-spacing-wide: 0.1em;
  --word-spacing-wider: 0.2em;
}

/* ===== الفئات الأساسية للخطوط العربية ===== */

/* الخط الأساسي */
.arabic-text {
  font-family: var(--font-arabic-primary);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-normal);
  word-spacing: var(--word-spacing-normal);
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  direction: rtl;
  text-align: right;
}

/* الخط الثانوي */
.arabic-text-secondary {
  font-family: var(--font-arabic-secondary);
}

/* الخط الأنيق */
.arabic-text-elegant {
  font-family: var(--font-arabic-elegant);
  letter-spacing: var(--letter-spacing-wide);
}

/* الخط التقليدي */
.arabic-text-traditional {
  font-family: var(--font-arabic-traditional);
  line-height: var(--line-height-relaxed);
}

/* ===== أوزان الخطوط ===== */
.arabic-text-light { font-weight: var(--font-weight-light); }
.arabic-text-regular { font-weight: var(--font-weight-regular); }
.arabic-text-medium { font-weight: var(--font-weight-medium); }
.arabic-text-semibold { font-weight: var(--font-weight-semibold); }
.arabic-text-bold { font-weight: var(--font-weight-bold); }
.arabic-text-extrabold { font-weight: var(--font-weight-extrabold); }

/* ===== أحجام الخطوط ===== */
.arabic-text-xs { font-size: var(--font-size-xs); }
.arabic-text-sm { font-size: var(--font-size-sm); }
.arabic-text-base { font-size: var(--font-size-base); }
.arabic-text-lg { font-size: var(--font-size-lg); }
.arabic-text-xl { font-size: var(--font-size-xl); }
.arabic-text-2xl { font-size: var(--font-size-2xl); }
.arabic-text-3xl { font-size: var(--font-size-3xl); }

/* ===== ارتفاع الأسطر ===== */
.arabic-leading-tight { line-height: var(--line-height-tight); }
.arabic-leading-normal { line-height: var(--line-height-normal); }
.arabic-leading-relaxed { line-height: var(--line-height-relaxed); }

/* ===== تباعد الأحرف ===== */
.arabic-tracking-tight { letter-spacing: var(--letter-spacing-tight); }
.arabic-tracking-normal { letter-spacing: var(--letter-spacing-normal); }
.arabic-tracking-wide { letter-spacing: var(--letter-spacing-wide); }
.arabic-tracking-wider { letter-spacing: var(--letter-spacing-wider); }

/* ===== ألوان النصوص المحسنة ===== */
.arabic-text-primary {
  color: #1a202c;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.arabic-text-secondary {
  color: #4a5568;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.03);
}

.arabic-text-muted {
  color: #718096;
}

.arabic-text-white {
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.arabic-text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: var(--font-weight-bold);
}

/* ===== تحسينات RTL ===== */
[dir="rtl"] {
  text-align: right;
  direction: rtl;
}

[dir="rtl"] .MuiTypography-root {
  font-family: var(--font-arabic-primary);
  text-align: right;
}

[dir="rtl"] .MuiListItemText-primary {
  font-family: var(--font-arabic-primary);
  font-weight: var(--font-weight-medium);
}

[dir="rtl"] .MuiListItemText-secondary {
  font-family: var(--font-arabic-primary);
  font-weight: var(--font-weight-regular);
}

[dir="rtl"] .MuiButton-root {
  font-family: var(--font-arabic-primary);
  font-weight: var(--font-weight-medium);
}

[dir="rtl"] .MuiTextField-root input {
  font-family: var(--font-arabic-primary);
  text-align: right;
}

[dir="rtl"] .MuiInputLabel-root {
  font-family: var(--font-arabic-primary);
  right: 14px;
  left: auto;
  transform-origin: right;
}

/* ===== تحسينات للعناوين ===== */
.arabic-heading {
  font-family: var(--font-arabic-elegant);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-wide);
  margin-bottom: 1rem;
}

.arabic-heading-1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-extrabold);
}

.arabic-heading-2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
}

.arabic-heading-3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.arabic-heading-4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
}

/* ===== تحسينات للفقرات ===== */
.arabic-paragraph {
  font-family: var(--font-arabic-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  margin-bottom: 1rem;
  color: #4a5568;
}

.arabic-paragraph-lead {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: #2d3748;
}

/* ===== تحسينات للقوائم ===== */
.arabic-list {
  font-family: var(--font-arabic-primary);
  line-height: var(--line-height-normal);
}

.arabic-list-item {
  margin-bottom: 0.5rem;
  padding-right: 1rem;
}

/* ===== تحسينات للأزرار ===== */
.arabic-button {
  font-family: var(--font-arabic-primary);
  font-weight: var(--font-weight-medium);
  letter-spacing: var(--letter-spacing-wide);
}

/* ===== تحسينات للبطاقات ===== */
.arabic-card-title {
  font-family: var(--font-arabic-elegant);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
  margin-bottom: 0.5rem;
}

.arabic-card-content {
  font-family: var(--font-arabic-primary);
  line-height: var(--line-height-normal);
}

/* ===== تحسينات للشاشات الصغيرة ===== */
@media (max-width: 768px) {
  .arabic-text {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
  }
  
  .arabic-heading {
    margin-bottom: 0.75rem;
  }
  
  .arabic-paragraph {
    margin-bottom: 0.75rem;
  }
}

/* ===== تحسينات للطباعة ===== */
@media print {
  .arabic-text {
    font-family: 'Times New Roman', serif;
    color: #000;
    text-shadow: none;
  }
  
  .arabic-text-gradient {
    -webkit-text-fill-color: #000;
    background: none;
  }
}

/* ===== تحسينات إمكانية الوصول ===== */
@media (prefers-reduced-motion: reduce) {
  .arabic-text {
    transition: none;
  }
}

@media (prefers-contrast: high) {
  .arabic-text-primary {
    color: #000;
    text-shadow: none;
  }
  
  .arabic-text-secondary {
    color: #333;
    text-shadow: none;
  }
}

/* ===== تحسينات للوضع المظلم ===== */
@media (prefers-color-scheme: dark) {
  .arabic-text-primary {
    color: #f7fafc;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
  
  .arabic-text-secondary {
    color: #e2e8f0;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
  }
  
  .arabic-text-muted {
    color: #a0aec0;
  }
}
