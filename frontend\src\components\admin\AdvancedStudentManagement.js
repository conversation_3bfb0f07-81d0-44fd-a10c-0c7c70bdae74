import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Avatar,
  LinearProgress,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Badge,
  Menu,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  People,
  PersonAdd,
  Edit,
  Delete,
  Visibility,
  TrendingUp,
  School,
  Assignment,
  Quiz,
  WorkspacePremium,
  Notifications,
  Send,
  Analytics,
  Download,
  FilterList,
  Search,
  MoreVert,
  ExpandMore,
  Star,
  CheckCircle,
  Warning,
  Error as ErrorIcon,
  Schedule,
  Email,
  Phone,
  LocationOn
} from '@mui/icons-material';
import { useLanguage } from '../../contexts/LanguageContext';
import toast from 'react-hot-toast';

const AdvancedStudentManagement = () => {
  const { language, t } = useLanguage();
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState(0);
  
  // حالات البيانات
  const [students, setStudents] = useState([]);
  const [courses, setCourses] = useState([]);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [filteredStudents, setFilteredStudents] = useState([]);
  
  // حالات النوافذ المنبثقة
  const [studentDialog, setStudentDialog] = useState(false);
  const [progressDialog, setProgressDialog] = useState(false);
  const [notificationDialog, setNotificationDialog] = useState(false);
  const [reportDialog, setReportDialog] = useState(false);
  const [gradeDialog, setGradeDialog] = useState(false);
  
  // حالات القوائم والفلاتر
  const [menuAnchor, setMenuAnchor] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [courseFilter, setCourseFilter] = useState('all');
  
  // حالات النماذج
  const [studentForm, setStudentForm] = useState({
    name: '',
    email: '',
    phone: '',
    student_code: '',
    is_active: true,
    bio: '',
    location: '',
    enrollment_date: new Date().toISOString().split('T')[0]
  });

  const [notificationForm, setNotificationForm] = useState({
    title: '',
    message: '',
    type: 'info', // info, success, warning, error
    recipients: 'selected', // all, selected, course
    courseId: '',
    sendEmail: true,
    sendSMS: false
  });

  const [gradeForm, setGradeForm] = useState({
    studentId: '',
    courseId: '',
    grade: '',
    feedback: '',
    certificateEligible: false
  });

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    filterStudents();
  }, [students, searchTerm, statusFilter, courseFilter]);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // محاكاة تحميل البيانات
      const studentsData = [
        {
          id: 1,
          name: 'أحمد محمد علي',
          email: '<EMAIL>',
          phone: '0501234567',
          student_code: '123456',
          is_active: true,
          bio: 'طالب مجتهد يسعى لتعلم البرمجة',
          location: 'الرياض، السعودية',
          enrollment_date: '2024-01-15',
          avatar: null,
          totalCourses: 3,
          completedCourses: 1,
          inProgressCourses: 2,
          totalHours: 45,
          averageGrade: 85,
          certificates: 1,
          lastActivity: new Date().toISOString(),
          enrollments: [
            {
              id: 1,
              course: { id: 1, title: 'أساسيات البرمجة', level: 'beginner' },
              progress: 100,
              grade: 90,
              status: 'completed',
              enrollment_date: '2024-01-15',
              completion_date: '2024-02-15',
              timeSpent: 40
            },
            {
              id: 2,
              course: { id: 2, title: 'تطوير المواقع', level: 'intermediate' },
              progress: 65,
              grade: 80,
              status: 'in_progress',
              enrollment_date: '2024-02-20',
              completion_date: null,
              timeSpent: 25
            }
          ],
          activities: [
            {
              id: 1,
              action: 'أكمل درس "المتغيرات في JavaScript"',
              timestamp: new Date().toISOString(),
              course: 'تطوير المواقع'
            },
            {
              id: 2,
              action: 'حصل على شهادة "أساسيات البرمجة"',
              timestamp: new Date(Date.now() - 86400000).toISOString(),
              course: 'أساسيات البرمجة'
            }
          ]
        },
        {
          id: 2,
          name: 'فاطمة أحمد السالم',
          email: '<EMAIL>',
          phone: '0507654321',
          student_code: '654321',
          is_active: true,
          bio: 'مهتمة بتعلم التصميم والبرمجة',
          location: 'جدة، السعودية',
          enrollment_date: '2024-01-20',
          avatar: null,
          totalCourses: 2,
          completedCourses: 0,
          inProgressCourses: 2,
          totalHours: 30,
          averageGrade: 92,
          certificates: 0,
          lastActivity: new Date(Date.now() - 3600000).toISOString(),
          enrollments: [
            {
              id: 3,
              course: { id: 1, title: 'أساسيات البرمجة', level: 'beginner' },
              progress: 45,
              grade: 88,
              status: 'in_progress',
              enrollment_date: '2024-01-20',
              completion_date: null,
              timeSpent: 18
            },
            {
              id: 4,
              course: { id: 3, title: 'التصميم الجرافيكي', level: 'beginner' },
              progress: 30,
              grade: 95,
              status: 'in_progress',
              enrollment_date: '2024-02-01',
              completion_date: null,
              timeSpent: 12
            }
          ],
          activities: [
            {
              id: 3,
              action: 'بدأ كورس "التصميم الجرافيكي"',
              timestamp: new Date(Date.now() - 3600000).toISOString(),
              course: 'التصميم الجرافيكي'
            }
          ]
        }
      ];

      const coursesData = [
        { id: 1, title: 'أساسيات البرمجة', level: 'beginner' },
        { id: 2, title: 'تطوير المواقع', level: 'intermediate' },
        { id: 3, title: 'التصميم الجرافيكي', level: 'beginner' }
      ];
      
      setStudents(studentsData);
      setCourses(coursesData);
      
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      toast.error('خطأ في تحميل البيانات');
    } finally {
      setLoading(false);
    }
  };

  // فلترة الطلاب
  const filterStudents = () => {
    let filtered = students;

    // فلترة بالبحث
    if (searchTerm) {
      filtered = filtered.filter(student =>
        student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.student_code.includes(searchTerm)
      );
    }

    // فلترة بالحالة
    if (statusFilter !== 'all') {
      filtered = filtered.filter(student => {
        switch (statusFilter) {
          case 'active':
            return student.is_active;
          case 'inactive':
            return !student.is_active;
          case 'completed':
            return student.completedCourses > 0;
          case 'in_progress':
            return student.inProgressCourses > 0;
          default:
            return true;
        }
      });
    }

    // فلترة بالكورس
    if (courseFilter !== 'all') {
      filtered = filtered.filter(student =>
        student.enrollments.some(enrollment => 
          enrollment.course.id === parseInt(courseFilter)
        )
      );
    }

    setFilteredStudents(filtered);
  };

  // إنشاء طالب جديد
  const handleCreateStudent = () => {
    setStudentForm({
      name: '',
      email: '',
      phone: '',
      student_code: generateStudentCode(),
      is_active: true,
      bio: '',
      location: '',
      enrollment_date: new Date().toISOString().split('T')[0]
    });
    setSelectedStudent(null);
    setStudentDialog(true);
  };

  // تحرير طالب
  const handleEditStudent = (student) => {
    setStudentForm({
      name: student.name,
      email: student.email,
      phone: student.phone,
      student_code: student.student_code,
      is_active: student.is_active,
      bio: student.bio || '',
      location: student.location || '',
      enrollment_date: student.enrollment_date
    });
    setSelectedStudent(student);
    setStudentDialog(true);
  };

  // حفظ الطالب
  const handleSaveStudent = async () => {
    try {
      setLoading(true);
      
      if (selectedStudent) {
        // تحديث طالب موجود
        const updatedStudents = students.map(student =>
          student.id === selectedStudent.id
            ? { ...student, ...studentForm, updated_at: new Date().toISOString() }
            : student
        );
        setStudents(updatedStudents);
        toast.success('تم تحديث بيانات الطالب بنجاح');
      } else {
        // إنشاء طالب جديد
        const newStudent = {
          id: Date.now(),
          ...studentForm,
          avatar: null,
          totalCourses: 0,
          completedCourses: 0,
          inProgressCourses: 0,
          totalHours: 0,
          averageGrade: 0,
          certificates: 0,
          lastActivity: new Date().toISOString(),
          enrollments: [],
          activities: [],
          created_at: new Date().toISOString()
        };
        
        setStudents([newStudent, ...students]);
        toast.success('تم إنشاء الطالب بنجاح');
      }
      
      setStudentDialog(false);
      setSelectedStudent(null);
      
    } catch (error) {
      console.error('خطأ في حفظ الطالب:', error);
      toast.error('خطأ في حفظ الطالب');
    } finally {
      setLoading(false);
    }
  };

  // حذف طالب
  const handleDeleteStudent = async (studentId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الطالب؟')) {
      try {
        const updatedStudents = students.filter(student => student.id !== studentId);
        setStudents(updatedStudents);
        toast.success('تم حذف الطالب بنجاح');
      } catch (error) {
        console.error('خطأ في حذف الطالب:', error);
        toast.error('خطأ في حذف الطالب');
      }
    }
  };

  // عرض تقدم الطالب
  const handleViewProgress = (student) => {
    setSelectedStudent(student);
    setProgressDialog(true);
  };

  // إرسال إشعار
  const handleSendNotification = (student = null) => {
    if (student) {
      setNotificationForm({
        ...notificationForm,
        recipients: 'selected'
      });
      setSelectedStudent(student);
    }
    setNotificationDialog(true);
  };

  // حفظ الإشعار
  const handleSaveNotification = async () => {
    try {
      setLoading(true);
      
      // محاكاة إرسال الإشعار
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      let recipientCount = 0;
      if (notificationForm.recipients === 'all') {
        recipientCount = students.length;
      } else if (notificationForm.recipients === 'selected') {
        recipientCount = 1;
      } else if (notificationForm.recipients === 'course') {
        recipientCount = students.filter(student =>
          student.enrollments.some(enrollment => 
            enrollment.course.id === parseInt(notificationForm.courseId)
          )
        ).length;
      }
      
      setNotificationDialog(false);
      setNotificationForm({
        title: '',
        message: '',
        type: 'info',
        recipients: 'selected',
        courseId: '',
        sendEmail: true,
        sendSMS: false
      });
      
      toast.success(`تم إرسال الإشعار إلى ${recipientCount} طالب`);
      
    } catch (error) {
      console.error('خطأ في إرسال الإشعار:', error);
      toast.error('خطأ في إرسال الإشعار');
    } finally {
      setLoading(false);
    }
  };

  // تقييم الطالب
  const handleGradeStudent = (student) => {
    setGradeForm({
      studentId: student.id,
      courseId: '',
      grade: '',
      feedback: '',
      certificateEligible: false
    });
    setSelectedStudent(student);
    setGradeDialog(true);
  };

  // حفظ التقييم
  const handleSaveGrade = async () => {
    try {
      setLoading(true);
      
      // محاكاة حفظ التقييم
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setGradeDialog(false);
      setGradeForm({
        studentId: '',
        courseId: '',
        grade: '',
        feedback: '',
        certificateEligible: false
      });
      
      toast.success('تم حفظ التقييم بنجاح');
      
    } catch (error) {
      console.error('خطأ في حفظ التقييم:', error);
      toast.error('خطأ في حفظ التقييم');
    } finally {
      setLoading(false);
    }
  };

  // توليد كود طالب
  const generateStudentCode = () => {
    return Math.floor(100000 + Math.random() * 900000).toString();
  };

  // الحصول على لون حالة الطالب
  const getStatusColor = (student) => {
    if (!student.is_active) return 'error';
    if (student.completedCourses > 0) return 'success';
    if (student.inProgressCourses > 0) return 'warning';
    return 'default';
  };

  // الحصول على نص حالة الطالب
  const getStatusText = (student) => {
    if (!student.is_active) return 'غير نشط';
    if (student.completedCourses > 0) return 'مكتمل';
    if (student.inProgressCourses > 0) return 'قيد التقدم';
    return 'جديد';
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  // تنسيق الوقت النسبي
  const formatRelativeTime = (dateString) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'منذ أقل من ساعة';
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `منذ ${diffInDays} يوم`;
    return formatDate(dateString);
  };

  if (loading && students.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <LinearProgress sx={{ width: '50%' }} />
      </Box>
    );
  }

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          إدارة الطلاب المتقدمة
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<Notifications />}
            onClick={() => handleSendNotification()}
          >
            إرسال إشعار جماعي
          </Button>
          <Button
            variant="contained"
            startIcon={<PersonAdd />}
            onClick={handleCreateStudent}
            sx={{
              background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)'
              }
            }}
          >
            إضافة طالب جديد
          </Button>
        </Box>
      </Box>

      {/* إحصائيات سريعة */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {students.length}
                  </Typography>
                  <Typography variant="body2">
                    إجمالي الطلاب
                  </Typography>
                </Box>
                <People sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {students.filter(s => s.is_active).length}
                  </Typography>
                  <Typography variant="body2">
                    طلاب نشطون
                  </Typography>
                </Box>
                <CheckCircle sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {students.reduce((sum, student) => sum + student.completedCourses, 0)}
                  </Typography>
                  <Typography variant="body2">
                    كورسات مكتملة
                  </Typography>
                </Box>
                <School sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #E91E63 0%, #C2185B 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {students.reduce((sum, student) => sum + student.certificates, 0)}
                  </Typography>
                  <Typography variant="body2">
                    شهادات صادرة
                  </Typography>
                </Box>
                <WorkspacePremium sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* أدوات البحث والفلترة */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="البحث بالاسم، البريد الإلكتروني، أو كود الطالب"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>فلترة بالحالة</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <MenuItem value="all">جميع الحالات</MenuItem>
                  <MenuItem value="active">نشط</MenuItem>
                  <MenuItem value="inactive">غير نشط</MenuItem>
                  <MenuItem value="completed">مكتمل</MenuItem>
                  <MenuItem value="in_progress">قيد التقدم</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>فلترة بالكورس</InputLabel>
                <Select
                  value={courseFilter}
                  onChange={(e) => setCourseFilter(e.target.value)}
                >
                  <MenuItem value="all">جميع الكورسات</MenuItem>
                  {courses.map((course) => (
                    <MenuItem key={course.id} value={course.id}>
                      {course.title}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Download />}
                onClick={() => toast.info('جاري تصدير البيانات...')}
              >
                تصدير
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* جدول الطلاب */}
      <TableContainer component={Paper} sx={{ mb: 3 }}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell sx={{ fontWeight: 'bold' }}>الطالب</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>معلومات الاتصال</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الحالة</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>التقدم</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الإحصائيات</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>آخر نشاط</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredStudents.map((student) => (
              <TableRow key={student.id} hover>
                {/* معلومات الطالب */}
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar
                      src={student.avatar}
                      sx={{
                        width: 50,
                        height: 50,
                        bgcolor: 'primary.main',
                        fontSize: '1.2rem'
                      }}
                    >
                      {student.name.charAt(0)}
                    </Avatar>
                    <Box>
                      <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                        {student.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        كود: {student.student_code}
                      </Typography>
                      {student.location && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mt: 0.5 }}>
                          <LocationOn sx={{ fontSize: 14, color: 'text.secondary' }} />
                          <Typography variant="caption" color="text.secondary">
                            {student.location}
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </Box>
                </TableCell>

                {/* معلومات الاتصال */}
                <TableCell>
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 0.5 }}>
                      <Email sx={{ fontSize: 14, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        {student.email}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <Phone sx={{ fontSize: 14, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        {student.phone}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>

                {/* الحالة */}
                <TableCell>
                  <Chip
                    label={getStatusText(student)}
                    color={getStatusColor(student)}
                    size="small"
                    sx={{ fontWeight: 'bold' }}
                  />
                </TableCell>

                {/* التقدم */}
                <TableCell>
                  <Box sx={{ minWidth: 120 }}>
                    {student.enrollments.map((enrollment, index) => (
                      <Box key={index} sx={{ mb: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                          <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                            {enrollment.course.title}
                          </Typography>
                          <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                            {enrollment.progress}%
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={enrollment.progress}
                          sx={{ height: 4, borderRadius: 2 }}
                          color={enrollment.progress === 100 ? 'success' : 'primary'}
                        />
                      </Box>
                    ))}
                  </Box>
                </TableCell>

                {/* الإحصائيات */}
                <TableCell>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <School sx={{ fontSize: 14, color: 'text.secondary' }} />
                      <Typography variant="caption">
                        {student.totalCourses} كورس
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <Schedule sx={{ fontSize: 14, color: 'text.secondary' }} />
                      <Typography variant="caption">
                        {student.totalHours} ساعة
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <Star sx={{ fontSize: 14, color: '#FFD700' }} />
                      <Typography variant="caption">
                        {student.averageGrade}%
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>

                {/* آخر نشاط */}
                <TableCell>
                  <Typography variant="caption" color="text.secondary">
                    {formatRelativeTime(student.lastActivity)}
                  </Typography>
                </TableCell>

                {/* الإجراءات */}
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 0.5 }}>
                    <Tooltip title="عرض التقدم">
                      <IconButton
                        size="small"
                        onClick={() => handleViewProgress(student)}
                        sx={{ color: 'info.main' }}
                      >
                        <TrendingUp />
                      </IconButton>
                    </Tooltip>

                    <Tooltip title="إرسال إشعار">
                      <IconButton
                        size="small"
                        onClick={() => handleSendNotification(student)}
                        sx={{ color: 'warning.main' }}
                      >
                        <Send />
                      </IconButton>
                    </Tooltip>

                    <Tooltip title="تقييم">
                      <IconButton
                        size="small"
                        onClick={() => handleGradeStudent(student)}
                        sx={{ color: 'success.main' }}
                      >
                        <Star />
                      </IconButton>
                    </Tooltip>

                    <IconButton
                      size="small"
                      onClick={(e) => {
                        setMenuAnchor(e.currentTarget);
                        setSelectedStudent(student);
                      }}
                    >
                      <MoreVert />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* رسالة عدم وجود طلاب */}
      {filteredStudents.length === 0 && (
        <Card sx={{ textAlign: 'center', py: 8 }}>
          <CardContent>
            <People sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h5" color="text.secondary" sx={{ mb: 2 }}>
              {searchTerm || statusFilter !== 'all' || courseFilter !== 'all'
                ? 'لا توجد نتائج للبحث'
                : 'لا يوجد طلاب'}
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              {searchTerm || statusFilter !== 'all' || courseFilter !== 'all'
                ? 'جرب تغيير معايير البحث أو الفلترة'
                : 'ابدأ بإضافة أول طالب'}
            </Typography>
            {!searchTerm && statusFilter === 'all' && courseFilter === 'all' && (
              <Button
                variant="contained"
                startIcon={<PersonAdd />}
                onClick={handleCreateStudent}
                sx={{
                  background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)'
                  }
                }}
              >
                إضافة طالب جديد
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* قائمة خيارات الطالب */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem onClick={() => {
          handleEditStudent(selectedStudent);
          setMenuAnchor(null);
        }}>
          <Edit sx={{ mr: 1 }} />
          تحرير
        </MenuItem>
        <MenuItem onClick={() => {
          handleViewProgress(selectedStudent);
          setMenuAnchor(null);
        }}>
          <Analytics sx={{ mr: 1 }} />
          تقرير مفصل
        </MenuItem>
        <MenuItem onClick={() => {
          setMenuAnchor(null);
          // فتح نافذة تسجيل في كورس
        }}>
          <School sx={{ mr: 1 }} />
          تسجيل في كورس
        </MenuItem>
        <MenuItem
          onClick={() => {
            handleDeleteStudent(selectedStudent?.id);
            setMenuAnchor(null);
          }}
          sx={{ color: 'error.main' }}
        >
          <Delete sx={{ mr: 1 }} />
          حذف
        </MenuItem>
      </Menu>

      {/* نافذة إضافة/تحرير طالب */}
      <Dialog open={studentDialog} onClose={() => setStudentDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <PersonAdd sx={{ color: 'primary.main' }} />
            <Typography variant="h6">
              {selectedStudent ? 'تحرير الطالب' : 'إضافة طالب جديد'}
            </Typography>
          </Box>
        </DialogTitle>

        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="الاسم الكامل"
                  value={studentForm.name}
                  onChange={(e) => setStudentForm({ ...studentForm, name: e.target.value })}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="البريد الإلكتروني"
                  type="email"
                  value={studentForm.email}
                  onChange={(e) => setStudentForm({ ...studentForm, email: e.target.value })}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="رقم الهاتف"
                  value={studentForm.phone}
                  onChange={(e) => setStudentForm({ ...studentForm, phone: e.target.value })}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="كود الطالب"
                  value={studentForm.student_code}
                  onChange={(e) => setStudentForm({ ...studentForm, student_code: e.target.value })}
                  required
                  disabled={selectedStudent} // لا يمكن تغيير الكود للطلاب الموجودين
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="الموقع"
                  value={studentForm.location}
                  onChange={(e) => setStudentForm({ ...studentForm, location: e.target.value })}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="تاريخ التسجيل"
                  type="date"
                  value={studentForm.enrollment_date}
                  onChange={(e) => setStudentForm({ ...studentForm, enrollment_date: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="نبذة شخصية"
                  value={studentForm.bio}
                  onChange={(e) => setStudentForm({ ...studentForm, bio: e.target.value })}
                />
              </Grid>

              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={studentForm.is_active}
                      onChange={(e) => setStudentForm({ ...studentForm, is_active: e.target.checked })}
                    />
                  }
                  label="طالب نشط"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setStudentDialog(false)}>
            إلغاء
          </Button>
          <Button
            variant="contained"
            onClick={handleSaveStudent}
            disabled={loading || !studentForm.name || !studentForm.email || !studentForm.student_code}
            sx={{
              background: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)'
              }
            }}
          >
            {loading ? 'جاري الحفظ...' : (selectedStudent ? 'تحديث' : 'إضافة')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* نافذة عرض التقدم */}
      <Dialog open={progressDialog} onClose={() => setProgressDialog(false)} maxWidth="lg" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <TrendingUp sx={{ color: 'primary.main' }} />
            <Typography variant="h6">
              تقدم الطالب: {selectedStudent?.name}
            </Typography>
          </Box>
        </DialogTitle>

        <DialogContent>
          {selectedStudent && (
            <Box sx={{ mt: 2 }}>
              <Tabs value={selectedTab} onChange={(e, newValue) => setSelectedTab(newValue)}>
                <Tab label="نظرة عامة" />
                <Tab label="الكورسات" />
                <Tab label="النشاطات" />
                <Tab label="الشهادات" />
              </Tabs>

              {/* تبويب النظرة العامة */}
              {selectedTab === 0 && (
                <Box sx={{ mt: 3 }}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6} md={3}>
                      <Card sx={{ textAlign: 'center', p: 2 }}>
                        <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                          {selectedStudent.totalCourses}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          إجمالي الكورسات
                        </Typography>
                      </Card>
                    </Grid>

                    <Grid item xs={12} sm={6} md={3}>
                      <Card sx={{ textAlign: 'center', p: 2 }}>
                        <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                          {selectedStudent.completedCourses}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          كورسات مكتملة
                        </Typography>
                      </Card>
                    </Grid>

                    <Grid item xs={12} sm={6} md={3}>
                      <Card sx={{ textAlign: 'center', p: 2 }}>
                        <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                          {selectedStudent.totalHours}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          ساعات التعلم
                        </Typography>
                      </Card>
                    </Grid>

                    <Grid item xs={12} sm={6} md={3}>
                      <Card sx={{ textAlign: 'center', p: 2 }}>
                        <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                          {selectedStudent.averageGrade}%
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          متوسط الدرجات
                        </Typography>
                      </Card>
                    </Grid>
                  </Grid>
                </Box>
              )}

              {/* تبويب الكورسات */}
              {selectedTab === 1 && (
                <Box sx={{ mt: 3 }}>
                  <List>
                    {selectedStudent.enrollments.map((enrollment, index) => (
                      <React.Fragment key={index}>
                        <ListItem>
                          <ListItemIcon>
                            <School color="primary" />
                          </ListItemIcon>
                          <ListItemText
                            primary={enrollment.course.title}
                            secondary={
                              <Box>
                                <Typography variant="caption" display="block">
                                  تاريخ التسجيل: {formatDate(enrollment.enrollment_date)}
                                </Typography>
                                <Typography variant="caption" display="block">
                                  الوقت المستغرق: {enrollment.timeSpent} ساعة
                                </Typography>
                                <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <LinearProgress
                                    variant="determinate"
                                    value={enrollment.progress}
                                    sx={{ flexGrow: 1, height: 8, borderRadius: 4 }}
                                  />
                                  <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                                    {enrollment.progress}%
                                  </Typography>
                                </Box>
                              </Box>
                            }
                          />
                          <ListItemSecondaryAction>
                            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                              <Chip
                                label={enrollment.status === 'completed' ? 'مكتمل' : 'قيد التقدم'}
                                color={enrollment.status === 'completed' ? 'success' : 'warning'}
                                size="small"
                              />
                              {enrollment.grade && (
                                <Chip
                                  label={`${enrollment.grade}%`}
                                  color="primary"
                                  size="small"
                                  icon={<Star />}
                                />
                              )}
                            </Box>
                          </ListItemSecondaryAction>
                        </ListItem>
                        {index < selectedStudent.enrollments.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                </Box>
              )}

              {/* تبويب النشاطات */}
              {selectedTab === 2 && (
                <Box sx={{ mt: 3 }}>
                  <List>
                    {selectedStudent.activities.map((activity, index) => (
                      <React.Fragment key={index}>
                        <ListItem>
                          <ListItemIcon>
                            <CheckCircle color="success" />
                          </ListItemIcon>
                          <ListItemText
                            primary={activity.action}
                            secondary={
                              <Box>
                                <Typography variant="caption" display="block">
                                  {activity.course}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {formatRelativeTime(activity.timestamp)}
                                </Typography>
                              </Box>
                            }
                          />
                        </ListItem>
                        {index < selectedStudent.activities.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                </Box>
              )}

              {/* تبويب الشهادات */}
              {selectedTab === 3 && (
                <Box sx={{ mt: 3 }}>
                  {selectedStudent.certificates > 0 ? (
                    <Alert severity="success">
                      حصل الطالب على {selectedStudent.certificates} شهادة
                    </Alert>
                  ) : (
                    <Alert severity="info">
                      لم يحصل الطالب على أي شهادات بعد
                    </Alert>
                  )}
                </Box>
              )}
            </Box>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setProgressDialog(false)}>
            إغلاق
          </Button>
          <Button
            variant="contained"
            startIcon={<Download />}
            onClick={() => toast.info('جاري تصدير التقرير...')}
            sx={{
              background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)'
              }
            }}
          >
            تصدير التقرير
          </Button>
        </DialogActions>
      </Dialog>

      {/* نافذة إرسال الإشعارات */}
      <Dialog open={notificationDialog} onClose={() => setNotificationDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Notifications sx={{ color: 'primary.main' }} />
            <Typography variant="h6">
              إرسال إشعار
            </Typography>
          </Box>
        </DialogTitle>

        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="عنوان الإشعار"
                  value={notificationForm.title}
                  onChange={(e) => setNotificationForm({ ...notificationForm, title: e.target.value })}
                  required
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="نص الإشعار"
                  value={notificationForm.message}
                  onChange={(e) => setNotificationForm({ ...notificationForm, message: e.target.value })}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>نوع الإشعار</InputLabel>
                  <Select
                    value={notificationForm.type}
                    onChange={(e) => setNotificationForm({ ...notificationForm, type: e.target.value })}
                  >
                    <MenuItem value="info">معلومات</MenuItem>
                    <MenuItem value="success">نجاح</MenuItem>
                    <MenuItem value="warning">تحذير</MenuItem>
                    <MenuItem value="error">خطأ</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>المستقبلون</InputLabel>
                  <Select
                    value={notificationForm.recipients}
                    onChange={(e) => setNotificationForm({ ...notificationForm, recipients: e.target.value })}
                  >
                    <MenuItem value="selected">الطالب المحدد</MenuItem>
                    <MenuItem value="all">جميع الطلاب</MenuItem>
                    <MenuItem value="course">طلاب كورس معين</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              {notificationForm.recipients === 'course' && (
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>الكورس</InputLabel>
                    <Select
                      value={notificationForm.courseId}
                      onChange={(e) => setNotificationForm({ ...notificationForm, courseId: e.target.value })}
                    >
                      {courses.map((course) => (
                        <MenuItem key={course.id} value={course.id}>
                          {course.title}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              )}

              <Grid item xs={12}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notificationForm.sendEmail}
                        onChange={(e) => setNotificationForm({ ...notificationForm, sendEmail: e.target.checked })}
                      />
                    }
                    label="إرسال عبر البريد الإلكتروني"
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={notificationForm.sendSMS}
                        onChange={(e) => setNotificationForm({ ...notificationForm, sendSMS: e.target.checked })}
                      />
                    }
                    label="إرسال عبر الرسائل النصية"
                  />
                </Box>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setNotificationDialog(false)}>
            إلغاء
          </Button>
          <Button
            variant="contained"
            onClick={handleSaveNotification}
            disabled={loading || !notificationForm.title || !notificationForm.message}
            startIcon={<Send />}
            sx={{
              background: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #F57C00 0%, #FF9800 100%)'
              }
            }}
          >
            {loading ? 'جاري الإرسال...' : 'إرسال الإشعار'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* نافذة التقييم */}
      <Dialog open={gradeDialog} onClose={() => setGradeDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Star sx={{ color: 'primary.main' }} />
            <Typography variant="h6">
              تقييم الطالب: {selectedStudent?.name}
            </Typography>
          </Box>
        </DialogTitle>

        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth required>
                  <InputLabel>الكورس</InputLabel>
                  <Select
                    value={gradeForm.courseId}
                    onChange={(e) => setGradeForm({ ...gradeForm, courseId: e.target.value })}
                  >
                    {selectedStudent?.enrollments.map((enrollment) => (
                      <MenuItem key={enrollment.id} value={enrollment.course.id}>
                        {enrollment.course.title}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="الدرجة (%)"
                  type="number"
                  value={gradeForm.grade}
                  onChange={(e) => setGradeForm({ ...gradeForm, grade: e.target.value })}
                  inputProps={{ min: 0, max: 100 }}
                  required
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="ملاحظات وتعليقات"
                  value={gradeForm.feedback}
                  onChange={(e) => setGradeForm({ ...gradeForm, feedback: e.target.value })}
                />
              </Grid>

              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={gradeForm.certificateEligible}
                      onChange={(e) => setGradeForm({ ...gradeForm, certificateEligible: e.target.checked })}
                    />
                  }
                  label="مؤهل للحصول على شهادة"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setGradeDialog(false)}>
            إلغاء
          </Button>
          <Button
            variant="contained"
            onClick={handleSaveGrade}
            disabled={loading || !gradeForm.courseId || !gradeForm.grade}
            sx={{
              background: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)'
              }
            }}
          >
            {loading ? 'جاري الحفظ...' : 'حفظ التقييم'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdvancedStudentManagement;
