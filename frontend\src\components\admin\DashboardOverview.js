import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Paper,
  LinearProgress,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Chip,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  People,
  School,
  WorkspacePremium,
  TrendingUp,
  CheckCircle,
  Star,
  Group,
  VideoLibrary,
  Assignment,
  Refresh,
  Timeline
} from '@mui/icons-material';
import { useLanguage } from '../../contexts/LanguageContext';
// import { supabase } from '../../config/supabase';

const DashboardOverview = () => {
  const { language, t } = useLanguage();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalCourses: 0,
    totalCertificates: 0,
    activeStudents: 0,
    completedCourses: 0,
    pendingCertificates: 0
  });
  const [recentActivity, setRecentActivity] = useState([]);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // محاكاة تحميل البيانات من قاعدة البيانات
      const students = Array.from({ length: 150 }, (_, i) => ({ id: i + 1, is_active: i % 5 !== 0 }));
      const courses = Array.from({ length: 25 }, (_, i) => ({ id: i + 1, status: i % 4 === 0 ? 'completed' : 'active' }));
      const certificates = Array.from({ length: 89 }, (_, i) => ({ id: i + 1, status: i % 10 === 0 ? 'pending' : 'issued' }));
      const activities = [
        {
          id: 1,
          type: 'student_registration',
          description: 'طالب جديد سجل في الكورس',
          created_at: new Date().toISOString(),
          user_name: 'أحمد محمد'
        },
        {
          id: 2,
          type: 'course_completion',
          description: 'تم إكمال كورس البرمجة',
          created_at: new Date().toISOString(),
          user_name: 'فاطمة علي'
        }
      ];

      // تحديث الإحصائيات
      setStats({
        totalStudents: students.length,
        totalCourses: courses.length,
        totalCertificates: certificates.length,
        activeStudents: students.filter(s => s.is_active).length,
        completedCourses: courses.filter(c => c.status === 'completed').length,
        pendingCertificates: certificates.filter(c => c.status === 'pending').length
      });

      setRecentActivity(activities);

    } catch (error) {
      console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
      
      // بيانات تجريبية في حالة الخطأ
      setStats({
        totalStudents: 150,
        totalCourses: 25,
        totalCertificates: 89,
        activeStudents: 120,
        completedCourses: 18,
        pendingCertificates: 12
      });

      setRecentActivity([
        {
          id: 1,
          type: 'student_registration',
          description: 'طالب جديد سجل في الكورس',
          created_at: new Date().toISOString(),
          user_name: 'أحمد محمد'
        },
        {
          id: 2,
          type: 'course_completion',
          description: 'تم إكمال كورس البرمجة',
          created_at: new Date().toISOString(),
          user_name: 'فاطمة علي'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  // بطاقات الإحصائيات
  const StatCard = ({ title, value, icon, color, subtitle, progress }) => (
    <Card
      sx={{
        height: '100%',
        background: `linear-gradient(135deg, ${color}15 0%, ${color}05 100%)`,
        border: `1px solid ${color}30`,
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: `0 8px 25px ${color}25`
        }
      }}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar
            sx={{
              bgcolor: color,
              width: 56,
              height: 56,
              mr: 2
            }}
          >
            {icon}
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', color }}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
          </Box>
        </Box>
        
        {subtitle && (
          <Typography variant="body2" sx={{ mb: 1 }}>
            {subtitle}
          </Typography>
        )}
        
        {progress !== undefined && (
          <LinearProgress
            variant="determinate"
            value={progress}
            sx={{
              height: 6,
              borderRadius: 3,
              bgcolor: `${color}20`,
              '& .MuiLinearProgress-bar': {
                bgcolor: color
              }
            }}
          />
        )}
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            مرحباً بك في لوحة التحكم
          </Typography>
          <Typography variant="body1" color="text.secondary">
            نظرة عامة على أداء الأكاديمية
          </Typography>
        </Box>
        
        <Tooltip title="تحديث البيانات">
          <IconButton
            onClick={handleRefresh}
            disabled={refreshing}
            sx={{
              bgcolor: 'primary.main',
              color: 'white',
              '&:hover': { bgcolor: 'primary.dark' }
            }}
          >
            <Refresh sx={{ animation: refreshing ? 'spin 1s linear infinite' : 'none' }} />
          </IconButton>
        </Tooltip>
      </Box>

      {/* بطاقات الإحصائيات الرئيسية */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="إجمالي الطلاب"
            value={stats.totalStudents}
            icon={<People />}
            color="#4169E1"
            subtitle={`${stats.activeStudents} طالب نشط`}
            progress={(stats.activeStudents / stats.totalStudents) * 100}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="إجمالي الكورسات"
            value={stats.totalCourses}
            icon={<School />}
            color="#FF6B35"
            subtitle={`${stats.completedCourses} كورس مكتمل`}
            progress={(stats.completedCourses / stats.totalCourses) * 100}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="الشهادات المُصدرة"
            value={stats.totalCertificates}
            icon={<WorkspacePremium />}
            color="#FFD700"
            subtitle={`${stats.pendingCertificates} في الانتظار`}
          />
        </Grid>
      </Grid>

      {/* الإحصائيات التفصيلية والنشاط الأخير */}
      <Grid container spacing={3}>
        {/* الإحصائيات التفصيلية */}
        <Grid item xs={12} md={8}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                الإحصائيات التفصيلية
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={6} sm={3}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <VideoLibrary sx={{ fontSize: 40, color: '#9C27B0', mb: 1 }} />
                    <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                      {stats.totalCourses * 8}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي الفيديوهات
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={6} sm={3}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Assignment sx={{ fontSize: 40, color: '#FF5722', mb: 1 }} />
                    <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                      {stats.totalCourses * 5}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      ملفات PDF
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={6} sm={3}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <TrendingUp sx={{ fontSize: 40, color: '#4CAF50', mb: 1 }} />
                    <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                      85%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      معدل الإكمال
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={6} sm={3}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Star sx={{ fontSize: 40, color: '#FFC107', mb: 1 }} />
                    <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                      4.8
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      متوسط التقييم
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* النشاط الأخير */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                النشاط الأخير
              </Typography>
              
              <List>
                {recentActivity.length > 0 ? (
                  recentActivity.map((activity, index) => (
                    <ListItem key={activity.id || index} sx={{ px: 0 }}>
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          {activity.type === 'student_registration' ? <People /> : <CheckCircle />}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={activity.description}
                        secondary={`${activity.user_name || 'مستخدم'} - منذ ${new Date(activity.created_at).toLocaleDateString('ar')}`}
                      />
                    </ListItem>
                  ))
                ) : (
                  <ListItem>
                    <ListItemText
                      primary="لا يوجد نشاط حديث"
                      secondary="سيظهر النشاط الجديد هنا"
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <style jsx>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </Box>
  );
};

export default DashboardOverview;
