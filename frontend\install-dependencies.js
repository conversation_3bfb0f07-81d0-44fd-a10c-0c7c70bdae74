#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 بدء تثبيت التبعيات المطلوبة لأكاديمية عالم المهارات...\n');

// قائمة التبعيات المطلوبة
const dependencies = [
  '@supabase/supabase-js@^2.38.0',
  'react-beautiful-dnd@^13.1.1',
  'recharts@^2.8.0',
  'react-scripts@5.0.1',
  'web-vitals@^2.1.4'
];

// قائمة التبعيات للتطوير
const devDependencies = [
  '@types/react-beautiful-dnd@^13.1.4'
];

function runCommand(command, description) {
  try {
    console.log(`📦 ${description}...`);
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} - تم بنجاح\n`);
  } catch (error) {
    console.error(`❌ خطأ في ${description}:`, error.message);
    process.exit(1);
  }
}

function checkPackageJson() {
  const packageJsonPath = path.join(__dirname, 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    console.error('❌ ملف package.json غير موجود!');
    process.exit(1);
  }
  console.log('✅ تم العثور على ملف package.json\n');
}

function main() {
  try {
    // فحص وجود package.json
    checkPackageJson();

    // تثبيت التبعيات الأساسية
    console.log('📋 تثبيت التبعيات الأساسية...');
    runCommand(`npm install ${dependencies.join(' ')}`, 'تثبيت التبعيات الأساسية');

    // تثبيت تبعيات التطوير
    console.log('📋 تثبيت تبعيات التطوير...');
    runCommand(`npm install --save-dev ${devDependencies.join(' ')}`, 'تثبيت تبعيات التطوير');

    // تحديث التبعيات الموجودة
    console.log('🔄 تحديث التبعيات الموجودة...');
    runCommand('npm update', 'تحديث التبعيات');

    // تنظيف وإعادة تثبيت node_modules
    console.log('🧹 تنظيف وإعادة تثبيت node_modules...');
    runCommand('npm ci', 'تنظيف وإعادة تثبيت');

    console.log('🎉 تم تثبيت جميع التبعيات بنجاح!');
    console.log('\n📝 التبعيات المثبتة:');
    dependencies.forEach(dep => console.log(`   ✓ ${dep}`));
    devDependencies.forEach(dep => console.log(`   ✓ ${dep} (dev)`));

    console.log('\n🚀 يمكنك الآن تشغيل المشروع باستخدام:');
    console.log('   npm start');

  } catch (error) {
    console.error('❌ حدث خطأ أثناء التثبيت:', error.message);
    process.exit(1);
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = { main };
