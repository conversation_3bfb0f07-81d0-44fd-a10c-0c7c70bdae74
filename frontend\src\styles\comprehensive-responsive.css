/* تحسينات التجاوب الشاملة للوحة التحكم الإدارية */
/* Comprehensive Responsive Improvements for Admin Dashboard */

/* ===== متغيرات CSS للتجاوب الشامل ===== */
:root {
  /* نقاط التوقف المحسنة */
  --mobile-max: 767px;
  --tablet-min: 768px;
  --tablet-max: 1024px;
  --laptop-min: 1025px;
  --laptop-max: 1440px;
  --desktop-min: 1441px;

  /* أحجام اللمس المحسنة */
  --touch-mobile: 48px;
  --touch-tablet: 48px;
  --touch-desktop: 44px;

  /* أحجام الخطوط المتجاوبة */
  --font-mobile: 0.9rem;
  --font-tablet: 1rem;
  --font-desktop: 1rem;

  /* المسافات المتجاوبة */
  --spacing-mobile: 16px;
  --spacing-tablet: 24px;
  --spacing-desktop: 32px;

  /* أحجام الدرج الجانبي */
  --drawer-mobile: 280px;
  --drawer-tablet: 300px;
  --drawer-desktop: 320px;

  /* ارتفاع الشريط العلوي */
  --appbar-mobile: 56px;
  --appbar-tablet: 64px;
  --appbar-desktop: 68px;
}

/* ===== إصلاحات أساسية للتجاوب ===== */
.admin-dashboard {
  /* تخطيط أساسي */
  display: flex !important;
  width: 100% !important;
  min-height: 100vh !important;
  overflow-x: hidden !important;
  box-sizing: border-box !important;
  
  /* تحسين الأداء */
  will-change: auto !important;
  transform: translateZ(0) !important;
  backface-visibility: hidden !important;
  
  /* تحسين التفاعل */
  touch-action: manipulation !important;
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
}

/* ===== الأجهزة المحمولة (أقل من 768px) ===== */
@media (max-width: 767px) {
  .admin-dashboard {
    flex-direction: column !important;
  }

  /* الشريط العلوي للأجهزة المحمولة */
  .admin-dashboard .MuiAppBar-root {
    width: 100% !important;
    height: var(--appbar-mobile) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1300 !important;
  }

  .admin-dashboard .MuiToolbar-root {
    min-height: var(--appbar-mobile) !important;
    padding: 0 var(--spacing-mobile) !important;
    justify-content: space-between !important;
  }

  /* الدرج الجانبي للأجهزة المحمولة */
  .admin-dashboard .MuiDrawer-paper {
    width: var(--drawer-mobile) !important;
    max-width: 85vw !important;
    height: 100vh !important;
    top: 0 !important;
    z-index: 1400 !important;
  }

  /* إخفاء الدرج الثابت */
  .admin-dashboard nav > div:not(.MuiDrawer-paper) {
    display: none !important;
  }

  /* المحتوى الرئيسي للأجهزة المحمولة */
  .admin-dashboard main {
    margin-top: var(--appbar-mobile) !important;
    margin-right: 0 !important;
    margin-left: 0 !important;
    width: 100% !important;
    padding: var(--spacing-mobile) !important;
    min-height: calc(100vh - var(--appbar-mobile)) !important;
  }

  /* الأزرار للأجهزة المحمولة */
  .admin-dashboard .MuiButton-root,
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root {
    min-height: var(--touch-mobile) !important;
    min-width: var(--touch-mobile) !important;
    padding: 12px !important;
    font-size: var(--font-mobile) !important;
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: rgba(0, 0, 255, 0.1) !important;
  }

  /* النصوص للأجهزة المحمولة */
  .admin-dashboard .MuiTypography-h6 {
    font-size: 1.1rem !important;
    line-height: 1.4 !important;
  }

  .admin-dashboard .MuiListItemText-primary {
    font-size: 0.95rem !important;
  }

  /* عناصر القائمة للأجهزة المحمولة */
  .admin-dashboard .MuiListItem-root {
    min-height: 52px !important;
    padding: 8px 16px !important;
    margin-bottom: 4px !important;
  }

  /* الجداول للأجهزة المحمولة */
  .admin-dashboard .MuiTable-root {
    font-size: 0.85rem !important;
  }

  .admin-dashboard .MuiTableCell-root {
    padding: 8px 12px !important;
    font-size: 0.85rem !important;
  }

  /* الحقول للأجهزة المحمولة */
  .admin-dashboard .MuiTextField-root {
    margin-bottom: 16px !important;
    width: 100% !important;
  }

  .admin-dashboard .MuiInputBase-root {
    font-size: 1rem !important;
    padding: 12px 16px !important;
  }

  /* البطاقات للأجهزة المحمولة */
  .admin-dashboard .MuiCard-root {
    margin-bottom: 16px !important;
    border-radius: 12px !important;
  }

  .admin-dashboard .MuiCardContent-root {
    padding: 16px !important;
  }

  /* الحاويات للأجهزة المحمولة */
  .admin-dashboard .MuiContainer-root {
    padding: 0 16px !important;
    max-width: 100% !important;
  }
}

/* ===== الأجهزة اللوحية (768px - 1024px) ===== */
@media (min-width: 768px) and (max-width: 1024px) {
  .admin-dashboard {
    flex-direction: row !important;
  }

  /* الشريط العلوي للأجهزة اللوحية */
  .admin-dashboard .MuiAppBar-root {
    width: 100% !important;
    height: var(--appbar-tablet) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1300 !important;
  }

  .admin-dashboard .MuiToolbar-root {
    min-height: var(--appbar-tablet) !important;
    padding: 0 var(--spacing-tablet) !important;
    justify-content: space-between !important;
  }

  /* الدرج الجانبي للأجهزة اللوحية */
  .admin-dashboard nav {
    width: var(--drawer-tablet) !important;
    position: fixed !important;
    top: var(--appbar-tablet) !important;
    right: 0 !important;
    height: calc(100vh - var(--appbar-tablet)) !important;
    z-index: 1200 !important;
  }

  .admin-dashboard .MuiDrawer-paper {
    width: var(--drawer-tablet) !important;
    height: 100% !important;
    position: relative !important;
    top: 0 !important;
  }

  /* إخفاء الدرج المؤقت */
  .admin-dashboard .MuiDrawer-root .MuiDrawer-temporary {
    display: none !important;
  }

  /* المحتوى الرئيسي للأجهزة اللوحية */
  .admin-dashboard main {
    margin-top: var(--appbar-tablet) !important;
    margin-right: var(--drawer-tablet) !important;
    margin-left: 0 !important;
    width: calc(100% - var(--drawer-tablet)) !important;
    padding: var(--spacing-tablet) !important;
    min-height: calc(100vh - var(--appbar-tablet)) !important;
  }

  /* الأزرار للأجهزة اللوحية */
  .admin-dashboard .MuiButton-root,
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root {
    min-height: var(--touch-tablet) !important;
    min-width: var(--touch-tablet) !important;
    padding: 12px 20px !important;
    font-size: var(--font-tablet) !important;
    touch-action: manipulation !important;
  }

  /* عناصر القائمة للأجهزة اللوحية */
  .admin-dashboard .MuiListItem-root {
    min-height: 56px !important;
    padding: 12px 20px !important;
  }

  /* الأيقونات للأجهزة اللوحية */
  .admin-dashboard .MuiSvgIcon-root {
    font-size: 1.4rem !important;
  }

  /* الجداول للأجهزة اللوحية */
  .admin-dashboard .MuiTableCell-root {
    padding: 16px !important;
    font-size: 0.95rem !important;
  }

  /* الحقول للأجهزة اللوحية */
  .admin-dashboard .MuiTextField-root {
    margin-bottom: 16px !important;
  }

  .admin-dashboard .MuiInputBase-root {
    font-size: 1rem !important;
    padding: 12px 16px !important;
  }

  /* البطاقات للأجهزة اللوحية */
  .admin-dashboard .MuiCard-root {
    border-radius: 12px !important;
    margin-bottom: 20px !important;
  }

  .admin-dashboard .MuiCardContent-root {
    padding: 20px !important;
  }

  /* الحاويات للأجهزة اللوحية */
  .admin-dashboard .MuiContainer-root {
    padding: 0 24px !important;
    max-width: 100% !important;
  }
}

/* ===== الشاشات الكبيرة (أكبر من 1024px) ===== */
@media (min-width: 1025px) {
  .admin-dashboard {
    flex-direction: row !important;
  }

  /* الشريط العلوي للشاشات الكبيرة */
  .admin-dashboard .MuiAppBar-root {
    width: 100% !important;
    height: var(--appbar-desktop) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1300 !important;
  }

  .admin-dashboard .MuiToolbar-root {
    min-height: var(--appbar-desktop) !important;
    padding: 0 var(--spacing-desktop) !important;
    justify-content: space-between !important;
  }

  /* الدرج الجانبي للشاشات الكبيرة */
  .admin-dashboard nav {
    width: var(--drawer-desktop) !important;
    position: fixed !important;
    top: var(--appbar-desktop) !important;
    right: 0 !important;
    height: calc(100vh - var(--appbar-desktop)) !important;
    z-index: 1200 !important;
  }

  .admin-dashboard .MuiDrawer-paper {
    width: var(--drawer-desktop) !important;
    height: 100% !important;
    position: relative !important;
    top: 0 !important;
  }

  /* المحتوى الرئيسي للشاشات الكبيرة */
  .admin-dashboard main {
    margin-top: var(--appbar-desktop) !important;
    margin-right: var(--drawer-desktop) !important;
    margin-left: 0 !important;
    width: calc(100% - var(--drawer-desktop)) !important;
    padding: var(--spacing-desktop) !important;
    min-height: calc(100vh - var(--appbar-desktop)) !important;
  }

  /* الأزرار للشاشات الكبيرة */
  .admin-dashboard .MuiButton-root,
  .admin-dashboard .MuiIconButton-root,
  .admin-dashboard .MuiListItemButton-root {
    min-height: var(--touch-desktop) !important;
    min-width: var(--touch-desktop) !important;
    padding: 10px 20px !important;
    font-size: var(--font-desktop) !important;
  }

  /* عناصر القائمة للشاشات الكبيرة */
  .admin-dashboard .MuiListItem-root {
    min-height: 52px !important;
    padding: 10px 16px !important;
  }

  /* الأيقونات للشاشات الكبيرة */
  .admin-dashboard .MuiSvgIcon-root {
    font-size: 1.3rem !important;
  }

  /* الحاويات للشاشات الكبيرة */
  .admin-dashboard .MuiContainer-root {
    padding: 0 32px !important;
    max-width: 100% !important;
  }
}
