import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Container,
  Slide,
  LinearProgress,
  Alert,
  Snackbar,
  IconButton,
  Tooltip,
  Tabs,
  Tab
} from '@mui/material';
import {
  School,
  Login as LoginIcon,
  AutoAwesome,
  Language,
  Phone,
  CheckCircle,
  Error as ErrorIcon,
  AdminPanelSettings,
  Email,
  Lock
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';

const Login = () => {
  const [loading, setLoading] = useState(false);
  const [loginProgress, setLoginProgress] = useState(0);
  const [errors, setErrors] = useState({});
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  const [tabValue, setTabValue] = useState(0);
  const [formValidation, setFormValidation] = useState({
    adminEmail: false,
    adminPassword: false,
    studentCode: false
  });

  // Admin login form
  const [adminForm, setAdminForm] = useState({
    email: '',
    password: ''
  });

  // Student login form
  const [studentForm, setStudentForm] = useState({
    code: ''
  });

  const { loginAdmin, loginStudent } = useAuth();
  const { language, changeLanguage, t } = useLanguage();

  // Enhanced validation functions
  const validateEmail = (email) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const validatePassword = (password) => {
    return password.length >= 6;
  };

  const validateStudentCode = (code) => {
    return /^\d{6}$/.test(code);
  };

  // Enhanced form validation
  useEffect(() => {
    setFormValidation({
      adminEmail: validateEmail(adminForm.email),
      adminPassword: validatePassword(adminForm.password),
      studentCode: validateStudentCode(studentForm.code)
    });
  }, [adminForm.email, adminForm.password, studentForm.code]);

  const showNotification = (message, severity = 'info') => {
    setNotification({
      open: true,
      message,
      severity
    });
  };

  const toggleLanguage = () => {
    changeLanguage(language === 'ar' ? 'en' : 'ar');
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    setErrors({});
  };

  const handleAdminSubmit = async (e) => {
    e.preventDefault();

    // Clear previous errors
    setErrors({});

    // Validate form
    if (!validateEmail(adminForm.email)) {
      setErrors({ email: 'يرجى إدخال بريد إلكتروني صحيح' });
      showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
      return;
    }

    if (!validatePassword(adminForm.password)) {
      setErrors({ password: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' });
      showNotification('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
      return;
    }

    setLoading(true);
    setLoginProgress(0);

    // Simulate loading progress
    const progressInterval = setInterval(() => {
      setLoginProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return prev;
        }
        return prev + 10;
      });
    }, 100);

    try {
      const result = await loginAdmin(adminForm.email, adminForm.password);

      setLoginProgress(100);

      if (result.success) {
        showNotification('تم تسجيل الدخول بنجاح!', 'success');

        // Clear form
        setAdminForm({ email: '', password: '' });

        // Navigate will be handled by the auth context
      } else {
        setErrors({ general: result.message || 'فشل في تسجيل الدخول' });
        showNotification(result.message || 'فشل في تسجيل الدخول', 'error');
      }
    } catch (error) {
      console.error('Login error:', error);
      const errorMessage = error.message || 'حدث خطأ أثناء تسجيل الدخول';
      setErrors({ general: errorMessage });
      showNotification(errorMessage, 'error');
    } finally {
      setLoading(false);
      setTimeout(() => setLoginProgress(0), 1000);
    }
  };

  const handleAdminChange = (field) => (e) => {
    const value = e.target.value;

    setAdminForm(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear field-specific errors when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }

    // Clear general errors
    if (errors.general) {
      setErrors(prev => ({
        ...prev,
        general: undefined
      }));
    }
  };

  const handleStudentSubmit = async (e) => {
    e.preventDefault();

    // Clear previous errors
    setErrors({});

    // Validate form
    if (!validateStudentCode(studentForm.code)) {
      setErrors({ code: 'يرجى إدخال كود مكون من 6 أرقام' });
      showNotification('يرجى إدخال كود مكون من 6 أرقام', 'error');
      return;
    }

    setLoading(true);
    setLoginProgress(0);

    // Simulate progress for better UX
    const progressInterval = setInterval(() => {
      setLoginProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + 10;
      });
    }, 100);

    try {
      const result = await loginStudent(studentForm.code);

      setLoginProgress(100);

      if (result.success) {
        showNotification('تم تسجيل الدخول بنجاح!', 'success');
      } else {
        setErrors({ general: result.message || 'فشل في تسجيل الدخول' });
        showNotification(result.message || 'فشل في تسجيل الدخول', 'error');
      }
    } catch (error) {
      setErrors({ general: 'حدث خطأ غير متوقع' });
      showNotification('حدث خطأ غير متوقع', 'error');
    } finally {
      clearInterval(progressInterval);
      setLoading(false);
      setTimeout(() => setLoginProgress(0), 1000);
    }
  };

  const handleStudentChange = (e) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setStudentForm({ code: value });

    // Clear errors when user starts typing
    if (errors.code) {
      setErrors(prev => ({
        ...prev,
        code: undefined
      }));
    }

    if (errors.general) {
      setErrors(prev => ({
        ...prev,
        general: undefined
      }));
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 2,
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Background decorative elements */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.1,
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }}
      />

      {/* Language Toggle */}
      <Tooltip title={language === 'ar' ? 'English' : 'العربية'}>
        <IconButton
          onClick={toggleLanguage}
          sx={{
            position: 'absolute',
            top: 20,
            right: 20,
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.3)'
            }
          }}
        >
          <Language />
        </IconButton>
      </Tooltip>

      <Container maxWidth="sm">
        <Card
          sx={{
            borderRadius: 4,
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
            overflow: 'hidden',
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)'
          }}
        >
          {/* Header */}
          <Box
            sx={{
              background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
              color: 'white',
              p: 4,
              textAlign: 'center',
              position: 'relative'
            }}
          >
            <AutoAwesome
              sx={{
                fontSize: '3rem',
                mb: 2,
                filter: 'drop-shadow(0 0 10px rgba(255, 215, 0, 0.5))',
                color: '#FFD700'
              }}
            />
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              SKILLS WORLD ACADEMY
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
              ALAA ABD HAMIED
            </Typography>
          </Box>

          {/* Tabs */}
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{
              borderBottom: '1px solid rgba(65, 105, 225, 0.4)',
              background: 'linear-gradient(90deg, #0000FF 0%, #4169E1 50%, #0000FF 100%)',
              '& .MuiTab-root': {
                py: 2,
                fontSize: '1rem',
                fontWeight: 600,
                color: 'rgba(255,255,255,0.8)',
                textTransform: 'none',
                transition: 'all 0.3s ease',
                '&:hover': {
                  color: 'white',
                  background: 'rgba(255,255,255,0.1)'
                },
                '&.Mui-selected': {
                  color: 'white',
                  background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
                  borderBottom: '2px solid #FFD700'
                }
              },
              '& .MuiTabs-indicator': {
                backgroundColor: '#FFD700',
                height: '3px',
                boxShadow: '0 0 8px rgba(255, 215, 0, 0.5)'
              }
            }}
          >
            <Tab
              icon={<AdminPanelSettings sx={{
                color: tabValue === 0 ? '#FFD700' : 'rgba(255, 215, 0, 0.7)',
                filter: tabValue === 0 ? 'drop-shadow(0 0 4px rgba(255, 215, 0, 0.3))' : 'none'
              }} />}
              label={t.admin || 'المدير'}
              iconPosition="start"
            />
            <Tab
              icon={<School sx={{
                color: tabValue === 1 ? '#FFD700' : 'rgba(255, 215, 0, 0.7)',
                filter: tabValue === 1 ? 'drop-shadow(0 0 4px rgba(255, 215, 0, 0.3))' : 'none'
              }} />}
              label={t.student || 'الطالب'}
              iconPosition="start"
            />
          </Tabs>

          <CardContent sx={{ p: 4 }}>
            {/* Admin Login */}
            {tabValue === 0 && (
              <Slide direction="right" in={tabValue === 0} timeout={300}>
                <Box
                  component="form"
                  onSubmit={handleAdminSubmit}
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    width: '100%'
                  }}
                >
                  <Typography
                    variant="h6"
                    sx={{
                      mb: 3,
                      textAlign: 'center',
                      color: '#0000FF',
                      fontWeight: 600
                    }}
                  >
                    {t.adminLogin || 'تسجيل دخول المدير'}
                  </Typography>

                  <TextField
                    fullWidth
                    label={t.email || 'البريد الإلكتروني'}
                    type="email"
                    value={adminForm.email}
                    onChange={handleAdminChange('email')}
                    required
                    sx={{
                      mb: 2,
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'rgba(65, 105, 225, 0.08)',
                        '&:hover': {
                          backgroundColor: 'rgba(65, 105, 225, 0.12)'
                        },
                        '&.Mui-focused': {
                          backgroundColor: 'rgba(65, 105, 225, 0.15)'
                        }
                      }
                    }}
                    InputProps={{
                      startAdornment: <Email sx={{ mr: 1, color: 'text.secondary' }} />
                    }}
                    error={!!errors.email}
                    helperText={errors.email}
                  />

                  <TextField
                    fullWidth
                    label={t.password || 'كلمة المرور'}
                    type="password"
                    value={adminForm.password}
                    onChange={handleAdminChange('password')}
                    required
                    sx={{
                      mb: 2,
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'rgba(65, 105, 225, 0.08)',
                        '&:hover': {
                          backgroundColor: 'rgba(65, 105, 225, 0.12)'
                        },
                        '&.Mui-focused': {
                          backgroundColor: 'rgba(65, 105, 225, 0.15)'
                        }
                      }
                    }}
                    InputProps={{
                      startAdornment: <Lock sx={{ mr: 1, color: 'text.secondary' }} />
                    }}
                    error={!!errors.password}
                    helperText={errors.password}
                  />

                  {/* General error */}
                  {errors.general && (
                    <Alert severity="error" sx={{ width: '100%', mb: 2 }}>
                      {errors.general}
                    </Alert>
                  )}

                  {/* Progress bar */}
                  {loading && (
                    <Box sx={{ width: '100%', mb: 2 }}>
                      <LinearProgress
                        variant="determinate"
                        value={loginProgress}
                        sx={{
                          height: 8,
                          borderRadius: 4,
                          backgroundColor: 'rgba(0, 0, 255, 0.1)',
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: '#0000FF'
                          }
                        }}
                      />
                    </Box>
                  )}

                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    disabled={loading || !formValidation.adminEmail || !formValidation.adminPassword}
                    startIcon={<LoginIcon />}
                    sx={{
                      mt: 2,
                      py: 1.5,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)'
                      },
                      '&:disabled': {
                        background: 'rgba(0, 0, 255, 0.3)'
                      }
                    }}
                  >
                    {loading ? (language === 'ar' ? 'جاري تسجيل الدخول...' : 'Logging in...') : (t.login || 'تسجيل الدخول')}
                  </Button>
                </Box>
              </Slide>
            )}

            {/* Student Login */}
            {tabValue === 1 && (
              <Slide direction="left" in={tabValue === 1} timeout={300}>
              <Box
                component="form"
                onSubmit={handleStudentSubmit}
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  width: '100%'
                }}
              >
                <Typography
                  variant="h6"
                  sx={{
                    mb: 3,
                    textAlign: 'center',
                    color: '#0000FF',
                    fontWeight: 600
                  }}
                >
                  {t.studentLogin}
                </Typography>

                <TextField
                  fullWidth
                  label={t.studentCode}
                  value={studentForm.code}
                  onChange={handleStudentChange}
                  required
                  inputProps={{
                    maxLength: 6,
                    pattern: '[0-9]*',
                    inputMode: 'numeric'
                  }}
                  sx={{
                    mb: 2,
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'rgba(65, 105, 225, 0.08)',
                      '&:hover': {
                        backgroundColor: 'rgba(65, 105, 225, 0.12)'
                      },
                      '&.Mui-focused': {
                        backgroundColor: 'rgba(65, 105, 225, 0.15)'
                      }
                    }
                  }}
                  error={!!errors.code}
                  helperText={errors.code}
                />

                {/* Code validation indicator */}
                {studentForm.code && (
                  <Box sx={{ width: '100%', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {formValidation.studentCode ? (
                        <CheckCircle sx={{ color: 'success.main', fontSize: '1rem' }} />
                      ) : (
                        <ErrorIcon sx={{ color: 'error.main', fontSize: '1rem' }} />
                      )}
                      <Typography
                        variant="caption"
                        sx={{
                          color: formValidation.studentCode ? 'success.main' : 'error.main',
                          fontSize: '0.75rem',
                          fontWeight: 500
                        }}
                      >
                        {formValidation.studentCode ?
                          (language === 'ar' ? 'كود صحيح' : 'Valid code') :
                          (language === 'ar' ? 'يجب أن يكون الكود 6 أرقام' : 'Code must be 6 digits')
                        }
                      </Typography>
                    </Box>
                  </Box>
                )}

                {/* General error */}
                {errors.general && (
                  <Alert severity="error" sx={{ width: '100%', mb: 2 }}>
                    {errors.general}
                  </Alert>
                )}

                {/* Progress bar */}
                {loading && (
                  <Box sx={{ width: '100%', mb: 2 }}>
                    <LinearProgress
                      variant="determinate"
                      value={loginProgress}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: 'rgba(0, 0, 255, 0.1)',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: '#0000FF'
                        }
                      }}
                    />
                  </Box>
                )}

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  disabled={loading || !formValidation.studentCode}
                  startIcon={<LoginIcon />}
                  sx={{
                    mt: 2,
                    py: 1.5,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)'
                    },
                    '&:disabled': {
                      background: 'rgba(0, 0, 255, 0.3)'
                    }
                  }}
                >
                  {loading ? (language === 'ar' ? 'جاري تسجيل الدخول...' : 'Logging in...') : t.login}
                </Button>
              </Box>
            </Slide>
            )}
          </CardContent>

          {/* Footer */}
          <Box
            sx={{
              p: 2,
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              borderTop: '1px solid #e0e0e0'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mb: 1 }}>
              <Phone sx={{ fontSize: '1rem', color: '#666' }} />
              <Typography variant="body2" color="text.secondary">
                0506747770
              </Typography>
            </Box>
            <Typography variant="caption" color="text.secondary">
              ALAA <EMAIL>
            </Typography>
          </Box>
        </Card>
      </Container>

      {/* Notification */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Login;
