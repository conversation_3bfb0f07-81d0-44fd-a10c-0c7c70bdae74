# 🎓 SKILLS WORLD ACADEMY - أكاديمية عالم المهارات

## نظرة عامة

أكاديمية عالم المهارات هي منصة تعليمية شاملة تهدف إلى تقديم كورسات تقنية عالية الجودة. تم تطوير هذا المشروع باستخدام أحدث التقنيات لضمان تجربة مستخدم متميزة وأداء عالي.

## ✨ المميزات الرئيسية

### 🎯 للطلاب
- **تسجيل دخول آمن** بكود طالب مكون من 6 أرقام
- **واجهة مستخدم عربية** مع دعم RTL كامل
- **لوحة تحكم تفاعلية** لمتابعة التقدم
- **مشاهدة الكورسات** مع مشغل فيديو متقدم
- **تحميل المواد التعليمية** (PDF، ملفات أخرى)
- **نظام شهادات** إلكترونية
- **دعم فني** مع نظام FAQ
- **مساعد ذكي** متكامل

### 👨‍💼 للمدراء
- **لوحة تحكم إدارية شاملة** مع إحصائيات مفصلة
- **إدارة الطلاب** (إضافة، تعديل، حذف، تسجيل في كورسات)
- **إدارة الكورسات** (إنشاء، تحرير، رفع محتوى)
- **إدارة الشهادات** مع قوالب قابلة للتخصيص
- **نظام إدارة قاعدة البيانات** مع نسخ احتياطية
- **تقارير وإحصائيات** مفصلة
- **إدارة الملف الشخصي** مع إعدادات الأمان

## 🛠️ التقنيات المستخدمة

### Frontend
- **React 18** - مكتبة JavaScript للواجهات
- **Material-UI (MUI)** - مكتبة مكونات UI
- **React Router** - للتنقل بين الصفحات
- **React Hot Toast** - للإشعارات
- **Stylis RTL** - دعم الكتابة من اليمين لليسار

### Backend & Database
- **Supabase** - قاعدة بيانات PostgreSQL مع API
- **Firebase** - للاستضافة والمصادقة
- **Storage** - لحفظ الملفات والفيديوهات

### أدوات التطوير
- **Create React App** - إعداد المشروع
- **ESLint** - فحص جودة الكود
- **Git** - نظام إدارة الإصدارات

## 📱 التصميم المتجاوب

المشروع مصمم ليعمل بشكل مثالي على جميع الأجهزة:

- **📱 الهواتف الذكية** (< 768px)
- **📱 الأجهزة اللوحية** (768px - 1024px)
- **💻 أجهزة الكمبيوتر** (> 1024px)

## 🚀 التثبيت والتشغيل

### المتطلبات
- Node.js 16 أو أحدث
- npm أو yarn
- حساب Supabase (مجاني)
- حساب Firebase (مجاني)

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone [repository-url]
cd frontend
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **إعداد متغيرات البيئة**
```bash
# إنشاء ملف .env.local
REACT_APP_SUPABASE_URL=your_supabase_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
REACT_APP_FIREBASE_API_KEY=your_firebase_api_key
REACT_APP_FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
REACT_APP_FIREBASE_PROJECT_ID=your_firebase_project_id
```

4. **إعداد قاعدة البيانات**
```bash
# تشغيل سكريبت إعداد Supabase
node setup-supabase.js
```

5. **تشغيل المشروع**
```bash
npm start
```

المشروع سيعمل على: `http://localhost:3000`

## 🔐 بيانات تسجيل الدخول

### للمدراء
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** admin123

### للطلاب
- **كود الطالب:** 123456 (أو أي كود 6 أرقام صالح)

## 📁 هيكل المشروع

```
frontend/
├── public/                 # الملفات العامة
├── src/
│   ├── components/         # مكونات React
│   │   ├── admin/         # مكونات لوحة الإدارة
│   │   ├── Login.js       # صفحة تسجيل الدخول
│   │   ├── StudentDashboard.js  # لوحة الطالب
│   │   └── ...
│   ├── contexts/          # React Contexts
│   ├── services/          # خدمات API
│   ├── config/            # إعدادات التطبيق
│   └── styles/            # ملفات CSS
├── database/              # سكريبتات قاعدة البيانات
└── package.json
```

## 🎨 نظام الألوان

- **الأزرق الأساسي:** #4169E1 (Royal Blue)
- **الأزرق الداكن:** #0000FF (Blue)
- **الذهبي:** #FFD700 (Gold)
- **الأبيض:** #FFFFFF
- **الرمادي:** #F5F5F5

## 📊 قاعدة البيانات

### الجداول الرئيسية
- `students` - بيانات الطلاب
- `courses` - الكورسات
- `course_categories` - فئات الكورسات
- `enrollments` - تسجيلات الطلاب
- `certificates` - الشهادات
- `course_videos` - فيديوهات الكورسات
- `course_materials` - مواد الكورسات
- `activities` - سجل النشاطات
- `settings` - إعدادات النظام

## 🔧 الإعدادات والتخصيص

### تغيير اسم الأكاديمية
```javascript
// في ملف src/config/constants.js
export const ACADEMY_NAME = 'اسم الأكاديمية الجديد';
```

### تخصيص الألوان
```javascript
// في ملف src/theme/colors.js
export const colors = {
  primary: '#4169E1',
  secondary: '#FFD700',
  // ...
};
```

## 🚀 النشر

### Firebase Hosting
```bash
# بناء المشروع
npm run build

# نشر على Firebase
firebase deploy
```

### Netlify
```bash
# بناء المشروع
npm run build

# رفع مجلد build إلى Netlify
```

## 🧪 الاختبار

```bash
# تشغيل الاختبارات
npm test

# تشغيل الاختبارات مع التغطية
npm run test:coverage
```

## 📈 الأداء

- **تحميل سريع** مع lazy loading
- **تحسين الصور** تلقائياً
- **ضغط الملفات** في الإنتاج
- **PWA** جاهز للتثبيت

## 🔒 الأمان

- **Row Level Security** في Supabase
- **تشفير البيانات** الحساسة
- **مصادقة JWT** آمنة
- **حماية CSRF** مدمجة

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في الاتصال بقاعدة البيانات**
   - تأكد من صحة متغيرات البيئة
   - تحقق من حالة خدمة Supabase

2. **مشاكل في التصميم المتجاوب**
   - امسح cache المتصفح
   - تأكد من تحديث المتصفح

3. **بطء في التحميل**
   - تحقق من سرعة الإنترنت
   - راجع حجم الملفات المرفوعة

## 📞 الدعم والتواصل

- **البريد الإلكتروني:** ALAA <EMAIL>
- **الهاتف:** **********
- **GitHub Issues:** [رابط المستودع]

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للمزيد من التفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل إرسال Pull Request.

## 📝 سجل التغييرات

### الإصدار 3.0.0
- ✅ إضافة لوحة تحكم إدارية شاملة
- ✅ دعم قاعدة بيانات Supabase
- ✅ تحسين التصميم المتجاوب
- ✅ إضافة نظام إدارة الشهادات
- ✅ تحسين الأمان والأداء

### الإصدار 2.0.0
- ✅ إعادة تصميم واجهة المستخدم
- ✅ دعم اللغة العربية مع RTL
- ✅ إضافة مشغل فيديو متقدم
- ✅ نظام إشعارات محسن

### الإصدار 1.0.0
- ✅ الإصدار الأولي
- ✅ تسجيل دخول الطلاب
- ✅ عرض الكورسات الأساسي

---

**تم تطوير هذا المشروع بـ ❤️ لأكاديمية عالم المهارات**
