/* ملف CSS نظيف للتجاوب الحقيقي */
/* Clean CSS file for true responsiveness */

/* ===== إعدادات أساسية ===== */
* {
  box-sizing: border-box !important;
}

html, body {
  margin: 0 !important;
  padding: 0 !important;
  overflow-x: hidden !important;
  width: 100% !important;
}

/* ===== الأجهزة المحمولة (< 600px) ===== */
@media (max-width: 599px) {
  .admin-dashboard {
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
    min-height: 100vh !important;
  }

  .admin-dashboard .MuiAppBar-root {
    width: 100% !important;
    height: 56px !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1300 !important;
  }

  .admin-dashboard .MuiToolbar-root {
    min-height: 56px !important;
    padding: 0 16px !important;
  }

  .admin-dashboard nav > div:not(.MuiDrawer-paper) {
    display: none !important;
  }

  .admin-dashboard .MuiDrawer-paper {
    width: 280px !important;
    max-width: 85vw !important;
    height: 100vh !important;
    z-index: 1400 !important;
  }

  .admin-dashboard main {
    margin-top: 56px !important;
    margin-right: 0 !important;
    margin-left: 0 !important;
    width: 100% !important;
    padding: 16px !important;
    min-height: calc(100vh - 56px) !important;
  }

  .admin-dashboard .MuiButton-root,
  .admin-dashboard .MuiIconButton-root {
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 12px !important;
  }
}

/* ===== الأجهزة اللوحية الصغيرة (600px - 899px) ===== */
@media (min-width: 600px) and (max-width: 899px) {
  .admin-dashboard {
    display: flex !important;
    flex-direction: row !important;
    width: 100% !important;
    min-height: 100vh !important;
  }

  .admin-dashboard .MuiAppBar-root {
    width: 100% !important;
    height: 60px !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1300 !important;
  }

  .admin-dashboard .MuiToolbar-root {
    min-height: 60px !important;
    padding: 0 20px !important;
  }

  .admin-dashboard nav {
    width: 300px !important;
    position: fixed !important;
    top: 60px !important;
    right: 0 !important;
    height: calc(100vh - 60px) !important;
    z-index: 1200 !important;
  }

  .admin-dashboard .MuiDrawer-paper {
    width: 300px !important;
    height: 100% !important;
    position: relative !important;
  }

  .admin-dashboard main {
    margin-top: 60px !important;
    margin-right: 300px !important;
    margin-left: 0 !important;
    width: calc(100% - 300px) !important;
    padding: 20px !important;
    min-height: calc(100vh - 60px) !important;
  }

  .admin-dashboard .MuiButton-root,
  .admin-dashboard .MuiIconButton-root {
    min-height: 52px !important;
    min-width: 52px !important;
    padding: 12px 20px !important;
  }
}

/* ===== الأجهزة اللوحية الكبيرة (900px - 1199px) ===== */
@media (min-width: 900px) and (max-width: 1199px) {
  .admin-dashboard {
    display: flex !important;
    flex-direction: row !important;
    width: 100% !important;
    min-height: 100vh !important;
  }

  .admin-dashboard .MuiAppBar-root {
    width: 100% !important;
    height: 64px !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1300 !important;
  }

  .admin-dashboard .MuiToolbar-root {
    min-height: 64px !important;
    padding: 0 24px !important;
  }

  .admin-dashboard nav {
    width: 320px !important;
    position: fixed !important;
    top: 64px !important;
    right: 0 !important;
    height: calc(100vh - 64px) !important;
    z-index: 1200 !important;
  }

  .admin-dashboard .MuiDrawer-paper {
    width: 320px !important;
    height: 100% !important;
    position: relative !important;
  }

  .admin-dashboard main {
    margin-top: 64px !important;
    margin-right: 320px !important;
    margin-left: 0 !important;
    width: calc(100% - 320px) !important;
    padding: 24px !important;
    min-height: calc(100vh - 64px) !important;
  }

  .admin-dashboard .MuiButton-root,
  .admin-dashboard .MuiIconButton-root {
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 12px 24px !important;
  }
}

/* ===== أجهزة الكمبيوتر (1200px - 1535px) ===== */
@media (min-width: 1200px) and (max-width: 1535px) {
  .admin-dashboard {
    display: flex !important;
    flex-direction: row !important;
    width: 100% !important;
    min-height: 100vh !important;
  }

  .admin-dashboard .MuiAppBar-root {
    width: 100% !important;
    height: 68px !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1300 !important;
  }

  .admin-dashboard .MuiToolbar-root {
    min-height: 68px !important;
    padding: 0 32px !important;
  }

  .admin-dashboard nav {
    width: 340px !important;
    position: fixed !important;
    top: 68px !important;
    right: 0 !important;
    height: calc(100vh - 68px) !important;
    z-index: 1200 !important;
  }

  .admin-dashboard .MuiDrawer-paper {
    width: 340px !important;
    height: 100% !important;
    position: relative !important;
  }

  .admin-dashboard main {
    margin-top: 68px !important;
    margin-right: 340px !important;
    margin-left: 0 !important;
    width: calc(100% - 340px) !important;
    padding: 32px !important;
    min-height: calc(100vh - 68px) !important;
  }

  .admin-dashboard .MuiButton-root,
  .admin-dashboard .MuiIconButton-root {
    min-height: 44px !important;
    min-width: 44px !important;
    padding: 10px 20px !important;
  }
}

/* ===== الشاشات الكبيرة (> 1536px) ===== */
@media (min-width: 1536px) {
  .admin-dashboard {
    display: flex !important;
    flex-direction: row !important;
    width: 100% !important;
    min-height: 100vh !important;
  }

  .admin-dashboard .MuiAppBar-root {
    width: 100% !important;
    height: 72px !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1300 !important;
  }

  .admin-dashboard .MuiToolbar-root {
    min-height: 72px !important;
    padding: 0 40px !important;
  }

  .admin-dashboard nav {
    width: 360px !important;
    position: fixed !important;
    top: 72px !important;
    right: 0 !important;
    height: calc(100vh - 72px) !important;
    z-index: 1200 !important;
  }

  .admin-dashboard .MuiDrawer-paper {
    width: 360px !important;
    height: 100% !important;
    position: relative !important;
  }

  .admin-dashboard main {
    margin-top: 72px !important;
    margin-right: 360px !important;
    margin-left: 0 !important;
    width: calc(100% - 360px) !important;
    padding: 40px !important;
    min-height: calc(100vh - 72px) !important;
  }

  .admin-dashboard .MuiButton-root,
  .admin-dashboard .MuiIconButton-root {
    min-height: 44px !important;
    min-width: 44px !important;
    padding: 10px 24px !important;
  }
}

/* ===== تحسينات عامة ===== */
.admin-dashboard {
  overflow-x: hidden !important;
  touch-action: manipulation !important;
  -webkit-overflow-scrolling: touch !important;
}

.admin-dashboard .MuiListItem-root {
  min-height: 48px !important;
  touch-action: manipulation !important;
}

.admin-dashboard .MuiContainer-root {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
}

.admin-dashboard .MuiCard-root {
  border-radius: 12px !important;
  margin-bottom: 16px !important;
}

/* ===== دعم RTL ===== */
[dir="rtl"] .admin-dashboard nav {
  right: 0 !important;
  left: auto !important;
}

[dir="rtl"] .admin-dashboard main {
  margin-right: var(--drawer-width, 300px) !important;
  margin-left: 0 !important;
}

[dir="ltr"] .admin-dashboard nav {
  left: 0 !important;
  right: auto !important;
}

[dir="ltr"] .admin-dashboard main {
  margin-left: var(--drawer-width, 300px) !important;
  margin-right: 0 !important;
}
