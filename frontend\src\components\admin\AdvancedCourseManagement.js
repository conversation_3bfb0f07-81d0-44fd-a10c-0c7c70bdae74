import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  LinearProgress,
  Tabs,
  Tab,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Divider,
  Avatar,
  Badge
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Visibility,
  VisibilityOff,
  School,
  VideoLibrary,
  PictureAsPdf,
  Assignment,
  Quiz,
  ExpandMore,
  Save,
  Cancel,
  CloudUpload,
  Preview,
  Settings,
  Analytics,
  People,
  Star,
  TrendingUp
} from '@mui/icons-material';
import FileUploader from './FileUploader';
import RichTextEditor from './RichTextEditor';
import DraggableLessonList from './DraggableLessonList';
import { useLanguage } from '../../contexts/LanguageContext';
import toast from 'react-hot-toast';

const AdvancedCourseManagement = () => {
  const { language, t } = useLanguage();
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState(0);

  // حالات البيانات
  const [courses, setCourses] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCourse, setSelectedCourse] = useState(null);

  // حالات النوافذ المنبثقة
  const [courseDialog, setCourseDialog] = useState(false);
  const [contentDialog, setContentDialog] = useState(false);
  const [previewDialog, setPreviewDialog] = useState(false);

  // حالات النماذج
  const [courseForm, setCourseForm] = useState({
    title: '',
    description: '',
    category_id: '',
    level: 'beginner',
    duration: '',
    price: '',
    thumbnail: '',
    status: 'draft',
    tags: [],
    requirements: '',
    objectives: '',
    isPublished: false,
    isFeatured: false
  });

  const [activeStep, setActiveStep] = useState(0);
  const [courseContent, setCourseContent] = useState({
    lessons: [],
    materials: [],
    assignments: [],
    quizzes: []
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      // محاكاة تحميل البيانات
      const categoriesData = [
        { id: 1, name: 'البرمجة', description: 'كورسات البرمجة', color: '#4169E1', coursesCount: 15 },
        { id: 2, name: 'التصميم', description: 'كورسات التصميم', color: '#FF6B35', coursesCount: 8 },
        { id: 3, name: 'التسويق', description: 'كورسات التسويق', color: '#32CD32', coursesCount: 12 }
      ];

      const coursesData = [
        {
          id: 1,
          title: 'أساسيات البرمجة',
          description: 'تعلم أساسيات البرمجة من الصفر مع أمثلة عملية وتطبيقات حقيقية',
          level: 'beginner',
          duration: '40 ساعة',
          price: '299',
          category_id: 1,
          category: { name: 'البرمجة', color: '#4169E1' },
          thumbnail: '/images/programming-basics.jpg',
          status: 'published',
          isPublished: true,
          isFeatured: true,
          studentsCount: 150,
          rating: 4.8,
          lessonsCount: 25,
          materialsCount: 15,
          tags: ['JavaScript', 'HTML', 'CSS', 'مبتدئ'],
          requirements: 'لا توجد متطلبات مسبقة',
          objectives: 'فهم أساسيات البرمجة وإنشاء مواقع ويب بسيطة',
          created_at: new Date().toISOString()
        },
        {
          id: 2,
          title: 'تطوير المواقع المتقدم',
          description: 'تعلم تطوير المواقع المتقدمة باستخدام React و Node.js',
          level: 'advanced',
          duration: '60 ساعة',
          price: '499',
          category_id: 1,
          category: { name: 'البرمجة', color: '#4169E1' },
          thumbnail: '/images/advanced-web.jpg',
          status: 'draft',
          isPublished: false,
          isFeatured: false,
          studentsCount: 75,
          rating: 4.9,
          lessonsCount: 35,
          materialsCount: 20,
          tags: ['React', 'Node.js', 'MongoDB', 'متقدم'],
          requirements: 'معرفة أساسيات JavaScript',
          objectives: 'إنشاء تطبيقات ويب متكاملة',
          created_at: new Date().toISOString()
        }
      ];

      setCategories(categoriesData);
      setCourses(coursesData);

    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      toast.error('خطأ في تحميل البيانات');
    } finally {
      setLoading(false);
    }
  };

  // إنشاء كورس جديد
  const handleCreateCourse = () => {
    setCourseForm({
      title: '',
      description: '',
      category_id: '',
      level: 'beginner',
      duration: '',
      price: '',
      thumbnail: '',
      status: 'draft',
      tags: [],
      requirements: '',
      objectives: '',
      isPublished: false,
      isFeatured: false
    });
    setActiveStep(0);
    setCourseDialog(true);
  };

  // تحرير كورس
  const handleEditCourse = (course) => {
    setCourseForm({
      ...course,
      tags: course.tags || []
    });
    setSelectedCourse(course);
    setActiveStep(0);
    setCourseDialog(true);
  };

  // حفظ الكورس
  const handleSaveCourse = async () => {
    try {
      setLoading(true);

      if (selectedCourse) {
        // تحديث كورس موجود
        const updatedCourses = courses.map(course =>
          course.id === selectedCourse.id
            ? { ...course, ...courseForm, updated_at: new Date().toISOString() }
            : course
        );
        setCourses(updatedCourses);
        toast.success('تم تحديث الكورس بنجاح');
      } else {
        // إنشاء كورس جديد
        const newCourse = {
          id: Date.now(),
          ...courseForm,
          category: categories.find(c => c.id === parseInt(courseForm.category_id)),
          studentsCount: 0,
          rating: 0,
          lessonsCount: 0,
          materialsCount: 0,
          created_at: new Date().toISOString()
        };

        setCourses([newCourse, ...courses]);
        toast.success('تم إنشاء الكورس بنجاح');
      }

      setCourseDialog(false);
      setSelectedCourse(null);

    } catch (error) {
      console.error('خطأ في حفظ الكورس:', error);
      toast.error('خطأ في حفظ الكورس');
    } finally {
      setLoading(false);
    }
  };

  // حذف كورس
  const handleDeleteCourse = async (courseId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الكورس؟')) {
      try {
        const updatedCourses = courses.filter(course => course.id !== courseId);
        setCourses(updatedCourses);
        toast.success('تم حذف الكورس بنجاح');
      } catch (error) {
        console.error('خطأ في حذف الكورس:', error);
        toast.error('خطأ في حذف الكورس');
      }
    }
  };

  // تبديل حالة النشر
  const togglePublishStatus = async (courseId) => {
    try {
      const updatedCourses = courses.map(course =>
        course.id === courseId
          ? { ...course, isPublished: !course.isPublished, status: !course.isPublished ? 'published' : 'draft' }
          : course
      );
      setCourses(updatedCourses);

      const course = courses.find(c => c.id === courseId);
      toast.success(course.isPublished ? 'تم إلغاء نشر الكورس' : 'تم نشر الكورس');
    } catch (error) {
      console.error('خطأ في تغيير حالة النشر:', error);
      toast.error('خطأ في تغيير حالة النشر');
    }
  };

  // تبديل حالة الإبراز
  const toggleFeaturedStatus = async (courseId) => {
    try {
      const updatedCourses = courses.map(course =>
        course.id === courseId
          ? { ...course, isFeatured: !course.isFeatured }
          : course
      );
      setCourses(updatedCourses);

      const course = courses.find(c => c.id === courseId);
      toast.success(course.isFeatured ? 'تم إلغاء إبراز الكورس' : 'تم إبراز الكورس');
    } catch (error) {
      console.error('خطأ في تغيير حالة الإبراز:', error);
      toast.error('خطأ في تغيير حالة الإبراز');
    }
  };

  // إدارة محتوى الكورس
  const handleManageContent = (course) => {
    setSelectedCourse(course);

    // محاكاة تحميل محتوى الكورس
    setCourseContent({
      lessons: [
        {
          id: 1,
          title: 'مقدمة في البرمجة',
          description: 'نظرة عامة على البرمجة وأساسياتها',
          type: 'video',
          duration: '15:30',
          order: 1,
          isFree: true,
          isVisible: true
        },
        {
          id: 2,
          title: 'متغيرات JavaScript',
          description: 'تعلم كيفية استخدام المتغيرات',
          type: 'video',
          duration: '20:45',
          order: 2,
          isFree: false,
          isVisible: true
        }
      ],
      materials: [],
      assignments: [],
      quizzes: []
    });

    setContentDialog(true);
  };

  // معاينة الكورس
  const handlePreviewCourse = (course) => {
    setSelectedCourse(course);
    setPreviewDialog(true);
  };

  // خطوات إنشاء الكورس
  const courseSteps = [
    'المعلومات الأساسية',
    'المحتوى والوصف',
    'الإعدادات المتقدمة',
    'المراجعة والنشر'
  ];

  // الحصول على لون مستوى الكورس
  const getLevelColor = (level) => {
    switch (level) {
      case 'beginner': return 'success';
      case 'intermediate': return 'warning';
      case 'advanced': return 'error';
      default: return 'default';
    }
  };

  // الحصول على نص مستوى الكورس
  const getLevelText = (level) => {
    switch (level) {
      case 'beginner': return 'مبتدئ';
      case 'intermediate': return 'متوسط';
      case 'advanced': return 'متقدم';
      default: return level;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <LinearProgress sx={{ width: '50%' }} />
      </Box>
    );
  }

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          إدارة الكورسات المتقدمة
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={handleCreateCourse}
          sx={{
            background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)'
            }
          }}
        >
          إنشاء كورس جديد
        </Button>
      </Box>

      {/* إحصائيات سريعة */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {courses.length}
                  </Typography>
                  <Typography variant="body2">
                    إجمالي الكورسات
                  </Typography>
                </Box>
                <School sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {courses.filter(c => c.isPublished).length}
                  </Typography>
                  <Typography variant="body2">
                    كورسات منشورة
                  </Typography>
                </Box>
                <Visibility sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {courses.reduce((sum, course) => sum + course.studentsCount, 0)}
                  </Typography>
                  <Typography variant="body2">
                    إجمالي الطلاب
                  </Typography>
                </Box>
                <People sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #E91E63 0%, #C2185B 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {courses.filter(c => c.isFeatured).length}
                  </Typography>
                  <Typography variant="body2">
                    كورسات مميزة
                  </Typography>
                </Box>
                <Star sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* قائمة الكورسات */}
      <Grid container spacing={3}>
        {courses.map((course) => (
          <Grid item xs={12} md={6} lg={4} key={course.id}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 6
                }
              }}
            >
              {/* صورة الكورس */}
              <Box
                sx={{
                  height: 200,
                  backgroundImage: course.thumbnail ? `url(${course.thumbnail})` : 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  position: 'relative',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                {!course.thumbnail && (
                  <School sx={{ fontSize: 60, color: 'white', opacity: 0.8 }} />
                )}

                {/* شارات الحالة */}
                <Box sx={{ position: 'absolute', top: 12, right: 12, display: 'flex', gap: 1 }}>
                  {course.isFeatured && (
                    <Chip
                      size="small"
                      label="مميز"
                      color="warning"
                      icon={<Star />}
                      sx={{ color: 'white', fontWeight: 'bold' }}
                    />
                  )}
                  <Chip
                    size="small"
                    label={course.isPublished ? 'منشور' : 'مسودة'}
                    color={course.isPublished ? 'success' : 'default'}
                    sx={{ color: 'white', fontWeight: 'bold' }}
                  />
                </Box>

                {/* تقييم الكورس */}
                {course.rating > 0 && (
                  <Box sx={{ position: 'absolute', bottom: 12, left: 12, display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <Star sx={{ color: '#FFD700', fontSize: 20 }} />
                    <Typography variant="body2" sx={{ color: 'white', fontWeight: 'bold' }}>
                      {course.rating}
                    </Typography>
                  </Box>
                )}
              </Box>

              <CardContent sx={{ flexGrow: 1, p: 3 }}>
                {/* عنوان الكورس */}
                <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1, lineHeight: 1.3 }}>
                  {course.title}
                </Typography>

                {/* وصف الكورس */}
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    mb: 2,
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden'
                  }}
                >
                  {course.description}
                </Typography>

                {/* معلومات الكورس */}
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  <Chip
                    size="small"
                    label={course.category?.name}
                    sx={{
                      backgroundColor: course.category?.color + '20',
                      color: course.category?.color,
                      fontWeight: 'bold'
                    }}
                  />
                  <Chip
                    size="small"
                    label={getLevelText(course.level)}
                    color={getLevelColor(course.level)}
                  />
                  {course.duration && (
                    <Chip
                      size="small"
                      label={course.duration}
                      variant="outlined"
                    />
                  )}
                </Box>

                {/* إحصائيات الكورس */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <People sx={{ fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="caption" color="text.secondary">
                        {course.studentsCount}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <VideoLibrary sx={{ fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="caption" color="text.secondary">
                        {course.lessonsCount}
                      </Typography>
                    </Box>
                  </Box>

                  {course.price && (
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                      {course.price} ر.س
                    </Typography>
                  )}
                </Box>

                {/* الكلمات المفتاحية */}
                {course.tags && course.tags.length > 0 && (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {course.tags.slice(0, 3).map((tag, index) => (
                      <Chip
                        key={index}
                        size="small"
                        label={tag}
                        variant="outlined"
                        sx={{ fontSize: '0.7rem', height: 20 }}
                      />
                    ))}
                    {course.tags.length > 3 && (
                      <Chip
                        size="small"
                        label={`+${course.tags.length - 3}`}
                        variant="outlined"
                        sx={{ fontSize: '0.7rem', height: 20 }}
                      />
                    )}
                  </Box>
                )}
              </CardContent>

              <CardActions sx={{ p: 2, pt: 0, justifyContent: 'space-between' }}>
                {/* أزرار الإجراءات الأساسية */}
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Tooltip title="تحرير">
                    <IconButton
                      size="small"
                      onClick={() => handleEditCourse(course)}
                      sx={{ color: 'primary.main' }}
                    >
                      <Edit />
                    </IconButton>
                  </Tooltip>

                  <Tooltip title="إدارة المحتوى">
                    <IconButton
                      size="small"
                      onClick={() => handleManageContent(course)}
                      sx={{ color: 'success.main' }}
                    >
                      <VideoLibrary />
                    </IconButton>
                  </Tooltip>

                  <Tooltip title="معاينة">
                    <IconButton
                      size="small"
                      onClick={() => handlePreviewCourse(course)}
                      sx={{ color: 'info.main' }}
                    >
                      <Preview />
                    </IconButton>
                  </Tooltip>

                  <Tooltip title="حذف">
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteCourse(course.id)}
                      sx={{ color: 'error.main' }}
                    >
                      <Delete />
                    </IconButton>
                  </Tooltip>
                </Box>

                {/* أزرار التحكم */}
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Tooltip title={course.isPublished ? 'إلغاء النشر' : 'نشر'}>
                    <IconButton
                      size="small"
                      onClick={() => togglePublishStatus(course.id)}
                      sx={{ color: course.isPublished ? 'success.main' : 'text.secondary' }}
                    >
                      {course.isPublished ? <Visibility /> : <VisibilityOff />}
                    </IconButton>
                  </Tooltip>

                  <Tooltip title={course.isFeatured ? 'إلغاء الإبراز' : 'إبراز'}>
                    <IconButton
                      size="small"
                      onClick={() => toggleFeaturedStatus(course.id)}
                      sx={{ color: course.isFeatured ? 'warning.main' : 'text.secondary' }}
                    >
                      <Star />
                    </IconButton>
                  </Tooltip>
                </Box>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* رسالة عدم وجود كورسات */}
      {courses.length === 0 && (
        <Card sx={{ textAlign: 'center', py: 8 }}>
          <CardContent>
            <School sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h5" color="text.secondary" sx={{ mb: 2 }}>
              لا توجد كورسات
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              ابدأ بإنشاء أول كورس لك
            </Typography>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={handleCreateCourse}
              sx={{
                background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)'
                }
              }}
            >
              إنشاء كورس جديد
            </Button>
          </CardContent>
        </Card>
      )}

      {/* نافذة إنشاء/تحرير الكورس */}
      <Dialog
        open={courseDialog}
        onClose={() => setCourseDialog(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { minHeight: '80vh' }
        }}
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <School sx={{ color: 'primary.main' }} />
            <Typography variant="h6">
              {selectedCourse ? 'تحرير الكورس' : 'إنشاء كورس جديد'}
            </Typography>
          </Box>
        </DialogTitle>

        <DialogContent>
          <Box sx={{ mt: 2 }}>
            {/* خطوات الإنشاء */}
            <Stepper activeStep={activeStep} orientation="horizontal" sx={{ mb: 4 }}>
              {courseSteps.map((label, index) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>

            {/* الخطوة الأولى: المعلومات الأساسية */}
            {activeStep === 0 && (
              <Box>
                <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                  المعلومات الأساسية
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="عنوان الكورس"
                      value={courseForm.title}
                      onChange={(e) => setCourseForm({ ...courseForm, title: e.target.value })}
                      required
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth required>
                      <InputLabel>الفئة</InputLabel>
                      <Select
                        value={courseForm.category_id}
                        onChange={(e) => setCourseForm({ ...courseForm, category_id: e.target.value })}
                      >
                        {categories.map((category) => (
                          <MenuItem key={category.id} value={category.id}>
                            {category.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>المستوى</InputLabel>
                      <Select
                        value={courseForm.level}
                        onChange={(e) => setCourseForm({ ...courseForm, level: e.target.value })}
                      >
                        <MenuItem value="beginner">مبتدئ</MenuItem>
                        <MenuItem value="intermediate">متوسط</MenuItem>
                        <MenuItem value="advanced">متقدم</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="المدة"
                      value={courseForm.duration}
                      onChange={(e) => setCourseForm({ ...courseForm, duration: e.target.value })}
                      placeholder="مثال: 40 ساعة"
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="السعر (ر.س)"
                      type="number"
                      value={courseForm.price}
                      onChange={(e) => setCourseForm({ ...courseForm, price: e.target.value })}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="رابط الصورة المصغرة"
                      value={courseForm.thumbnail}
                      onChange={(e) => setCourseForm({ ...courseForm, thumbnail: e.target.value })}
                      placeholder="https://example.com/image.jpg"
                    />
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* الخطوة الثانية: المحتوى والوصف */}
            {activeStep === 1 && (
              <Box>
                <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                  المحتوى والوصف
                </Typography>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
                    وصف الكورس
                  </Typography>
                  <RichTextEditor
                    value={courseForm.description}
                    onChange={(content) => setCourseForm({ ...courseForm, description: content })}
                    placeholder="اكتب وصفاً شاملاً للكورس..."
                    height={200}
                    toolbar="full"
                  />
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
                    متطلبات الكورس
                  </Typography>
                  <RichTextEditor
                    value={courseForm.requirements}
                    onChange={(content) => setCourseForm({ ...courseForm, requirements: content })}
                    placeholder="ما هي المتطلبات المسبقة لهذا الكورس؟"
                    height={150}
                    toolbar="basic"
                  />
                </Box>

                <Box>
                  <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
                    أهداف الكورس
                  </Typography>
                  <RichTextEditor
                    value={courseForm.objectives}
                    onChange={(content) => setCourseForm({ ...courseForm, objectives: content })}
                    placeholder="ماذا سيتعلم الطلاب من هذا الكورس؟"
                    height={150}
                    toolbar="basic"
                  />
                </Box>
              </Box>
            )}

            {/* الخطوة الثالثة: الإعدادات المتقدمة */}
            {activeStep === 2 && (
              <Box>
                <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                  الإعدادات المتقدمة
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="الكلمات المفتاحية (مفصولة بفواصل)"
                      value={courseForm.tags.join(', ')}
                      onChange={(e) => setCourseForm({
                        ...courseForm,
                        tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
                      })}
                      placeholder="JavaScript, React, تطوير ويب"
                      helperText="أدخل الكلمات المفتاحية مفصولة بفواصل"
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>حالة الكورس</InputLabel>
                      <Select
                        value={courseForm.status}
                        onChange={(e) => setCourseForm({ ...courseForm, status: e.target.value })}
                      >
                        <MenuItem value="draft">مسودة</MenuItem>
                        <MenuItem value="published">منشور</MenuItem>
                        <MenuItem value="archived">مؤرشف</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={courseForm.isPublished}
                            onChange={(e) => setCourseForm({ ...courseForm, isPublished: e.target.checked })}
                          />
                        }
                        label="نشر الكورس"
                      />

                      <FormControlLabel
                        control={
                          <Switch
                            checked={courseForm.isFeatured}
                            onChange={(e) => setCourseForm({ ...courseForm, isFeatured: e.target.checked })}
                          />
                        }
                        label="كورس مميز"
                      />
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* الخطوة الرابعة: المراجعة والنشر */}
            {activeStep === 3 && (
              <Box>
                <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                  مراجعة الكورس
                </Typography>

                <Paper sx={{ p: 3, mb: 3 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        عنوان الكورس
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'bold', mb: 2 }}>
                        {courseForm.title}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        الفئة
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'bold', mb: 2 }}>
                        {categories.find(c => c.id === parseInt(courseForm.category_id))?.name}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        المستوى
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'bold', mb: 2 }}>
                        {getLevelText(courseForm.level)}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        السعر
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'bold', mb: 2 }}>
                        {courseForm.price ? `${courseForm.price} ر.س` : 'مجاني'}
                      </Typography>
                    </Grid>

                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">
                        الوصف
                      </Typography>
                      <Box
                        sx={{
                          border: '1px solid #ddd',
                          borderRadius: 1,
                          p: 2,
                          maxHeight: 200,
                          overflow: 'auto'
                        }}
                        dangerouslySetInnerHTML={{ __html: courseForm.description }}
                      />
                    </Grid>
                  </Grid>
                </Paper>

                <Alert severity="info">
                  تأكد من مراجعة جميع المعلومات قبل حفظ الكورس
                </Alert>
              </Box>
            )}
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, justifyContent: 'space-between' }}>
          <Button onClick={() => setCourseDialog(false)}>
            إلغاء
          </Button>

          <Box sx={{ display: 'flex', gap: 1 }}>
            {activeStep > 0 && (
              <Button onClick={() => setActiveStep(activeStep - 1)}>
                السابق
              </Button>
            )}

            {activeStep < courseSteps.length - 1 ? (
              <Button
                variant="contained"
                onClick={() => setActiveStep(activeStep + 1)}
                disabled={
                  (activeStep === 0 && (!courseForm.title || !courseForm.category_id)) ||
                  (activeStep === 1 && !courseForm.description)
                }
              >
                التالي
              </Button>
            ) : (
              <Button
                variant="contained"
                onClick={handleSaveCourse}
                disabled={loading}
                sx={{
                  background: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)'
                  }
                }}
              >
                {loading ? 'جاري الحفظ...' : (selectedCourse ? 'تحديث الكورس' : 'إنشاء الكورس')}
              </Button>
            )}
          </Box>
        </DialogActions>
      </Dialog>

      {/* نافذة إدارة المحتوى */}
      <Dialog
        open={contentDialog}
        onClose={() => setContentDialog(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: { height: '90vh' }
        }}
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <VideoLibrary sx={{ color: 'primary.main' }} />
            <Typography variant="h6">
              إدارة محتوى الكورس: {selectedCourse?.title}
            </Typography>
          </Box>
        </DialogTitle>

        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Tabs value={selectedTab} onChange={(e, newValue) => setSelectedTab(newValue)}>
              <Tab label="الدروس" />
              <Tab label="رفع الملفات" />
              <Tab label="المواد التعليمية" />
              <Tab label="الواجبات" />
            </Tabs>

            {/* تبويب الدروس */}
            {selectedTab === 0 && (
              <Box sx={{ mt: 3 }}>
                <DraggableLessonList
                  courseId={selectedCourse?.id}
                  lessons={courseContent.lessons}
                  onLessonsReorder={(reorderedLessons) => {
                    setCourseContent({ ...courseContent, lessons: reorderedLessons });
                  }}
                  onLessonUpdate={(lessonId, updates) => {
                    const updatedLessons = courseContent.lessons.map(lesson =>
                      lesson.id === lessonId ? { ...lesson, ...updates } : lesson
                    );
                    setCourseContent({ ...courseContent, lessons: updatedLessons });
                  }}
                  onLessonDelete={(lessonId) => {
                    const updatedLessons = courseContent.lessons.filter(lesson => lesson.id !== lessonId);
                    setCourseContent({ ...courseContent, lessons: updatedLessons });
                  }}
                  editable={true}
                />
              </Box>
            )}

            {/* تبويب رفع الملفات */}
            {selectedTab === 1 && (
              <Box sx={{ mt: 3 }}>
                <FileUploader
                  courseId={selectedCourse?.id}
                  onFilesUploaded={(uploadedFiles) => {
                    // تحديث قائمة الدروس بالملفات المرفوعة
                    const newLessons = uploadedFiles.map(file => ({
                      id: file.id,
                      title: file.title,
                      description: file.description,
                      type: file.type.startsWith('video/') ? 'video' : 'material',
                      duration: file.duration || '',
                      order: courseContent.lessons.length + 1,
                      isFree: file.isFree,
                      isVisible: file.isVisible,
                      fileUrl: file.url
                    }));

                    setCourseContent({
                      ...courseContent,
                      lessons: [...courseContent.lessons, ...newLessons]
                    });

                    toast.success(`تم إضافة ${uploadedFiles.length} ملف جديد`);
                  }}
                  acceptedTypes={{
                    'video/*': ['.mp4', '.avi', '.mov', '.wmv'],
                    'application/pdf': ['.pdf'],
                    'image/*': ['.jpg', '.jpeg', '.png', '.gif'],
                    'application/msword': ['.doc', '.docx'],
                    'application/vnd.ms-powerpoint': ['.ppt', '.pptx']
                  }}
                  maxSize={200 * 1024 * 1024} // 200MB
                  multiple={true}
                />
              </Box>
            )}

            {/* تبويب المواد التعليمية */}
            {selectedTab === 2 && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  المواد التعليمية الإضافية
                </Typography>

                <Grid container spacing={2}>
                  {courseContent.materials.map((material, index) => (
                    <Grid item xs={12} sm={6} md={4} key={index}>
                      <Card>
                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <PictureAsPdf color="error" />
                            <Typography variant="subtitle2">
                              {material.title}
                            </Typography>
                          </Box>
                          <Typography variant="caption" color="text.secondary">
                            {material.description}
                          </Typography>
                        </CardContent>
                        <CardActions>
                          <Button size="small">تحرير</Button>
                          <Button size="small" color="error">حذف</Button>
                        </CardActions>
                      </Card>
                    </Grid>
                  ))}

                  {courseContent.materials.length === 0 && (
                    <Grid item xs={12}>
                      <Alert severity="info">
                        لا توجد مواد تعليمية إضافية. استخدم تبويب "رفع الملفات" لإضافة مواد جديدة.
                      </Alert>
                    </Grid>
                  )}
                </Grid>
              </Box>
            )}

            {/* تبويب الواجبات */}
            {selectedTab === 3 && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  الواجبات والاختبارات
                </Typography>

                <Grid container spacing={2}>
                  {courseContent.assignments.map((assignment, index) => (
                    <Grid item xs={12} sm={6} md={4} key={index}>
                      <Card>
                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <Assignment color="warning" />
                            <Typography variant="subtitle2">
                              {assignment.title}
                            </Typography>
                          </Box>
                          <Typography variant="caption" color="text.secondary">
                            {assignment.description}
                          </Typography>
                        </CardContent>
                        <CardActions>
                          <Button size="small">تحرير</Button>
                          <Button size="small" color="error">حذف</Button>
                        </CardActions>
                      </Card>
                    </Grid>
                  ))}

                  {courseContent.assignments.length === 0 && (
                    <Grid item xs={12}>
                      <Alert severity="info">
                        لا توجد واجبات أو اختبارات. يمكنك إضافة واجبات جديدة من خلال رفع الملفات.
                      </Alert>
                    </Grid>
                  )}
                </Grid>
              </Box>
            )}
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setContentDialog(false)}>
            إغلاق
          </Button>
          <Button
            variant="contained"
            startIcon={<Save />}
            sx={{
              background: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)'
              }
            }}
          >
            حفظ التغييرات
          </Button>
        </DialogActions>
      </Dialog>

      {/* نافذة معاينة الكورس */}
      <Dialog
        open={previewDialog}
        onClose={() => setPreviewDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Preview sx={{ color: 'primary.main' }} />
            <Typography variant="h6">
              معاينة الكورس: {selectedCourse?.title}
            </Typography>
          </Box>
        </DialogTitle>

        <DialogContent>
          {selectedCourse && (
            <Box>
              {/* صورة الكورس */}
              <Box
                sx={{
                  height: 250,
                  backgroundImage: selectedCourse.thumbnail ?
                    `url(${selectedCourse.thumbnail})` :
                    'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  borderRadius: 2,
                  mb: 3,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                {!selectedCourse.thumbnail && (
                  <School sx={{ fontSize: 80, color: 'white', opacity: 0.8 }} />
                )}
              </Box>

              {/* معلومات الكورس */}
              <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 2 }}>
                {selectedCourse.title}
              </Typography>

              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 3 }}>
                <Chip
                  label={selectedCourse.category?.name}
                  sx={{
                    backgroundColor: selectedCourse.category?.color + '20',
                    color: selectedCourse.category?.color,
                    fontWeight: 'bold'
                  }}
                />
                <Chip
                  label={getLevelText(selectedCourse.level)}
                  color={getLevelColor(selectedCourse.level)}
                />
                {selectedCourse.duration && (
                  <Chip label={selectedCourse.duration} variant="outlined" />
                )}
                {selectedCourse.price && (
                  <Chip
                    label={`${selectedCourse.price} ر.س`}
                    color="primary"
                    variant="outlined"
                  />
                )}
              </Box>

              {/* الوصف */}
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                وصف الكورس
              </Typography>
              <Box
                sx={{
                  mb: 3,
                  p: 2,
                  border: '1px solid #ddd',
                  borderRadius: 1,
                  maxHeight: 200,
                  overflow: 'auto'
                }}
                dangerouslySetInnerHTML={{ __html: selectedCourse.description }}
              />

              {/* الإحصائيات */}
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6} sm={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                      {selectedCourse.studentsCount}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      طالب مسجل
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                      {selectedCourse.lessonsCount}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      درس
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                      {selectedCourse.rating}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      التقييم
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                      {selectedCourse.materialsCount}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      مادة تعليمية
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              {/* الكلمات المفتاحية */}
              {selectedCourse.tags && selectedCourse.tags.length > 0 && (
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                    الكلمات المفتاحية
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {selectedCourse.tags.map((tag, index) => (
                      <Chip
                        key={index}
                        label={tag}
                        variant="outlined"
                        size="small"
                      />
                    ))}
                  </Box>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setPreviewDialog(false)}>
            إغلاق
          </Button>
          <Button
            variant="contained"
            startIcon={<Edit />}
            onClick={() => {
              setPreviewDialog(false);
              handleEditCourse(selectedCourse);
            }}
            sx={{
              background: 'linear-gradient(135deg, #4169E1 0%, #0000FF 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)'
              }
            }}
          >
            تحرير الكورس
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdvancedCourseManagement;