{"name": "skills-world-academy-frontend", "version": "3.0.0", "description": "أكاديمية عالم المهارات - منصة تعليمية شاملة مع لوحة تحكم إدارية", "author": "ALAA ABD HAMIED <ALAA <EMAIL>>", "license": "MIT", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.3", "@mui/material": "^5.14.5", "@supabase/supabase-js": "^2.50.4", "axios": "^1.5.0", "chart.js": "^4.5.0", "date-fns": "^2.30.0", "firebase": "^10.1.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.15.0", "stylis-plugin-rtl": "^2.1.1", "react-beautiful-dnd": "^13.1.1", "recharts": "^2.8.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:coverage": "react-scripts test --coverage --watchAll=false", "eject": "react-scripts eject", "setup-db": "node setup-supabase.js", "deploy": "npm run build && firebase deploy", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"react-scripts": "^5.0.1"}, "proxy": "http://localhost:5000"}