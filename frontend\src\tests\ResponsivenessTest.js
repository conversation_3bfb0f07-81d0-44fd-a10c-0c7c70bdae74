import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert
} from '@mui/material';
import {
  PhoneAndroid,
  Tablet,
  Computer,
  ExpandMore,
  CheckCircle,
  Error,
  Warning,
  Info
} from '@mui/icons-material';

/**
 * مكون اختبار التجاوب الشامل للوحة التحكم الإدارية
 */
const ResponsivenessTest = () => {
  const [screenSize, setScreenSize] = useState('');
  const [deviceType, setDeviceType] = useState('');
  const [testResults, setTestResults] = useState({});

  useEffect(() => {
    const updateScreenInfo = () => {
      const width = window.innerWidth;
      
      if (width < 768) {
        setScreenSize('صغيرة (أقل من 768px)');
        setDeviceType('mobile');
      } else if (width >= 768 && width <= 1024) {
        setScreenSize('متوسطة (768px - 1024px)');
        setDeviceType('tablet');
      } else {
        setScreenSize('كبيرة (أكبر من 1024px)');
        setDeviceType('desktop');
      }
    };

    updateScreenInfo();
    window.addEventListener('resize', updateScreenInfo);
    
    return () => window.removeEventListener('resize', updateScreenInfo);
  }, []);

  const runResponsivenessTests = () => {
    const tests = {
      mobile: [
        {
          name: 'عرض الشريط العلوي',
          test: () => {
            const appBar = document.querySelector('.admin-dashboard .MuiAppBar-root');
            return appBar && appBar.offsetWidth === window.innerWidth;
          }
        },
        {
          name: 'إخفاء الدرج الثابت',
          test: () => {
            const drawer = document.querySelector('.admin-dashboard nav > div:not(.MuiDrawer-paper)');
            return !drawer || window.getComputedStyle(drawer).display === 'none';
          }
        },
        {
          name: 'حجم الأزرار المناسب',
          test: () => {
            const buttons = document.querySelectorAll('.admin-dashboard .MuiButton-root');
            return Array.from(buttons).every(btn => btn.offsetHeight >= 48);
          }
        },
        {
          name: 'عرض المحتوى الكامل',
          test: () => {
            const main = document.querySelector('.admin-dashboard main');
            return main && main.offsetWidth === window.innerWidth;
          }
        }
      ],
      tablet: [
        {
          name: 'عرض الدرج الجانبي',
          test: () => {
            const nav = document.querySelector('.admin-dashboard nav');
            return nav && nav.offsetWidth >= 300;
          }
        },
        {
          name: 'موضع الشريط العلوي',
          test: () => {
            const appBar = document.querySelector('.admin-dashboard .MuiAppBar-root');
            return appBar && appBar.offsetWidth === window.innerWidth;
          }
        },
        {
          name: 'تخطيط المحتوى الرئيسي',
          test: () => {
            const main = document.querySelector('.admin-dashboard main');
            const nav = document.querySelector('.admin-dashboard nav');
            return main && nav && (main.offsetWidth + nav.offsetWidth) <= window.innerWidth;
          }
        },
        {
          name: 'أحجام الأزرار للمس',
          test: () => {
            const buttons = document.querySelectorAll('.admin-dashboard .MuiButton-root');
            return Array.from(buttons).every(btn => btn.offsetHeight >= 48);
          }
        }
      ],
      desktop: [
        {
          name: 'استغلال المساحة الكاملة',
          test: () => {
            const main = document.querySelector('.admin-dashboard main');
            const nav = document.querySelector('.admin-dashboard nav');
            return main && nav && (main.offsetWidth + nav.offsetWidth) >= window.innerWidth * 0.95;
          }
        },
        {
          name: 'تخطيط متوازن',
          test: () => {
            const nav = document.querySelector('.admin-dashboard nav');
            return nav && nav.offsetWidth >= 320;
          }
        },
        {
          name: 'ارتفاع الشريط العلوي',
          test: () => {
            const appBar = document.querySelector('.admin-dashboard .MuiAppBar-root');
            return appBar && appBar.offsetHeight >= 68;
          }
        }
      ]
    };

    const currentTests = tests[deviceType] || [];
    const results = {};

    currentTests.forEach(test => {
      try {
        results[test.name] = test.test();
      } catch (error) {
        results[test.name] = false;
      }
    });

    setTestResults(results);
  };

  const getDeviceIcon = (type) => {
    switch (type) {
      case 'mobile': return <PhoneAndroid />;
      case 'tablet': return <Tablet />;
      case 'desktop': return <Computer />;
      default: return <Info />;
    }
  };

  const getStatusIcon = (status) => {
    if (status === true) return <CheckCircle sx={{ color: '#4CAF50' }} />;
    if (status === false) return <Error sx={{ color: '#F44336' }} />;
    return <Warning sx={{ color: '#FF9800' }} />;
  };

  const getStatusColor = (status) => {
    if (status === true) return '#4CAF50';
    if (status === false) return '#F44336';
    return '#FF9800';
  };

  const testCategories = [
    {
      title: 'اختبارات الأجهزة المحمولة',
      description: 'فحص التجاوب مع الشاشات الصغيرة (أقل من 768px)',
      tests: [
        'تسجيل الدخول على الهواتف الذكية',
        'التنقل بين أقسام لوحة التحكم',
        'استخدام الدرج الجانبي',
        'التفاعل مع الأزرار والقوائم',
        'عرض الجداول والبيانات',
        'التبديل بين اللغات'
      ]
    },
    {
      title: 'اختبارات الأجهزة اللوحية',
      description: 'فحص التجاوب مع الشاشات المتوسطة (768px - 1024px)',
      tests: [
        'التفاعل باللمس مع جميع العناصر',
        'عرض الدرج الجانبي والمحتوى الرئيسي',
        'اختبار الاتجاهين الأفقي والعمودي',
        'إدارة الدورات والطلاب',
        'استخدام النماذج والحقول',
        'عرض الإحصائيات والتقارير'
      ]
    },
    {
      title: 'اختبارات الشاشات الكبيرة',
      description: 'فحص التجاوب مع الشاشات الكبيرة (أكبر من 1024px)',
      tests: [
        'الاستفادة الكاملة من المساحة المتاحة',
        'تخطيط متوازن للعناصر',
        'عرض محسن للجداول والبيانات',
        'تجربة مستخدم محسنة',
        'سرعة التحميل والأداء',
        'التوافق مع المتصفحات المختلفة'
      ]
    }
  ];

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" sx={{ mb: 3, textAlign: 'center', color: '#0000FF' }}>
        اختبار التجاوب الشامل - لوحة التحكم الإدارية
      </Typography>

      {/* معلومات الجهاز الحالي */}
      <Paper sx={{ p: 3, mb: 4, backgroundColor: '#f8f9fa' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {getDeviceIcon(deviceType)}
          <Typography variant="h6" sx={{ ml: 2, color: '#0000FF' }}>
            معلومات الجهاز الحالي
          </Typography>
        </Box>
        
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Chip 
              label={`حجم الشاشة: ${screenSize}`}
              color="primary"
              sx={{ mb: 1, width: '100%' }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Chip 
              label={`نوع الجهاز: ${deviceType === 'mobile' ? 'محمول' : deviceType === 'tablet' ? 'لوحي' : 'سطح مكتب'}`}
              color="secondary"
              sx={{ mb: 1, width: '100%' }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Chip 
              label={`العرض: ${window.innerWidth}px`}
              color="info"
              sx={{ mb: 1, width: '100%' }}
            />
          </Grid>
        </Grid>

        <Button
          variant="contained"
          onClick={runResponsivenessTests}
          sx={{ mt: 2, backgroundColor: '#0000FF' }}
        >
          تشغيل اختبارات التجاوب
        </Button>
      </Paper>

      {/* نتائج الاختبارات */}
      {Object.keys(testResults).length > 0 && (
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h6" sx={{ mb: 2, color: '#0000FF' }}>
            نتائج الاختبارات التلقائية
          </Typography>
          
          <List>
            {Object.entries(testResults).map(([testName, result]) => (
              <ListItem key={testName}>
                <ListItemIcon>
                  {getStatusIcon(result)}
                </ListItemIcon>
                <ListItemText 
                  primary={testName}
                  secondary={result ? 'نجح الاختبار' : 'فشل الاختبار'}
                />
              </ListItem>
            ))}
          </List>
        </Paper>
      )}

      {/* دليل الاختبارات اليدوية */}
      <Typography variant="h5" sx={{ mb: 3, color: '#0000FF' }}>
        دليل الاختبارات اليدوية
      </Typography>

      {testCategories.map((category, index) => (
        <Accordion key={index} sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="h6" sx={{ color: '#0000FF' }}>
              {category.title}
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body2" sx={{ mb: 2, color: '#666' }}>
              {category.description}
            </Typography>
            
            <List>
              {category.tests.map((test, testIndex) => (
                <ListItem key={testIndex}>
                  <ListItemIcon>
                    <CheckCircle sx={{ color: '#4CAF50' }} />
                  </ListItemIcon>
                  <ListItemText primary={test} />
                </ListItem>
              ))}
            </List>
          </AccordionDetails>
        </Accordion>
      ))}

      {/* تعليمات الاختبار */}
      <Alert severity="info" sx={{ mt: 4 }}>
        <Typography variant="body2">
          <strong>تعليمات الاختبار:</strong><br />
          1. اختبر الموقع على أجهزة مختلفة (هاتف، تابلت، كمبيوتر)<br />
          2. جرب تغيير حجم نافذة المتصفح<br />
          3. اختبر التبديل بين الاتجاهين الأفقي والعمودي<br />
          4. تأكد من سهولة الوصول لجميع العناصر<br />
          5. اختبر التبديل بين العربية والإنجليزية
        </Typography>
      </Alert>
    </Box>
  );
};

export default ResponsivenessTest;
